# Missing Team Stats Analysis Report

## Executive Summary

Based on the analysis of the league testing log file from `logs/league_testing_20250718_145701.log`, this report identifies leagues experiencing missing team statistics warnings during the feature engineering phase.

### Key Findings

- **Total leagues tested**: 314
- **Successful leagues**: 288 (91.7%)
- **Leagues with missing team stats warnings**: 202 out of 288 successful leagues (70.1%)
- **Total missing team stats warnings**: 9,798
- **Average skip percentage**: 24.9%
- **Unique teams affected**: 2,460

## Critical Issues

### 1. High Impact Leagues (Most Warnings)

| Rank | League | Warnings | Total Matches | Skip % |
|------|--------|----------|---------------|--------|
| 1 | ARGENTINA_PRIMERA_NACIONAL | 198 | 746 | 26.5% |
| 2 | NETHERLANDS_EERSTE_DIVISIE | 192 | 304 | 63.2% |
| 3 | NETHERLANDS_DERDE_DIVISIE_SATURDAY | 152 | 320 | 47.5% |
| 4 | NETHERLANDS_TWEEDE_DIVISIE | 144 | 330 | 43.6% |
| 5 | NETHERLANDS_EREDIVISIE | 141 | 243 | 58.0% |
| 6 | GERMANY_OBERLIGA_HAMBURG | 136 | 382 | 35.6% |
| 7 | SPAIN_SEGUNDA_RFEF_GROUP_5 | 130 | 442 | 29.4% |
| 8 | GERMANY_OBERLIGA_HESSEN | 127 | 457 | 27.8% |
| 9 | SPAIN_SEGUNDA_RFEF_GROUP_4 | 127 | 442 | 28.7% |
| 10 | HONG_KONG_PREMIER_LEAGUE | 126 | 216 | 58.3% |

### 2. Highest Skip Rate Leagues (Data Quality Issues)

| Rank | League | Skip % | Warnings |
|------|--------|--------|----------|
| 1 | LATVIA_1_LIGA | 87.5% | 7 |
| 2 | FINLAND_YKKONEN | 85.7% | 6 |
| 3 | BRAZIL_SERIE_D | 83.3% | 5 |
| 4 | SWEDEN_DIV_2_NORRLAND | 80.0% | 4 |
| 5 | BELGIUM_U21_PRO_LEAGUE | 74.6% | 53 |
| 6 | DENMARK_SUPERLIGA | 71.4% | 80 |
| 7 | AUSTRALIA_SOUTH_AUSTRALIA_NPL | 66.7% | 4 |
| 8 | SWEDEN_DIV_2_VASTRA_GOTALAND | 66.7% | 4 |
| 9 | NETHERLANDS_EERSTE_DIVISIE | 63.2% | 192 |
| 10 | FINLAND_KAKKONEN_GROUP_B | 61.9% | 13 |

## Pattern Analysis

### Geographic Distribution
- **Netherlands**: 4 leagues in top 10 by warnings (major systemic issue)
- **Germany**: 2 leagues in top 10 by warnings
- **Spain**: 2 leagues in top 10 by warnings
- **Nordic countries**: High skip rates (Finland, Sweden, Latvia, Denmark)

### League Types Affected
- **Professional leagues**: Netherlands Eredivisie, Denmark Superliga
- **Lower divisions**: Multiple German Oberliga, Spanish Segunda RFEF
- **Youth leagues**: Belgium U21 Pro League
- **Regional leagues**: Various Australian NPL divisions

### Most Problematic Teams
Teams appearing in multiple leagues with missing stats:
1. **Independiente** (4 leagues)
2. **Guadalajara** (2 leagues)
3. Multiple Israeli teams (Hapoel/Ironi clubs)
4. Irish teams (Bohemians, Cork City)

## Root Cause Analysis

### Potential Causes
1. **Team name mismatches** between data sources
2. **Missing team statistics files** in scraped data
3. **Data source inconsistencies** (SoccerStats vs FootyStats)
4. **Recent team additions/changes** not reflected in all datasets
5. **Encoding/character issues** in team names

### Evidence Patterns
- **Netherlands leagues**: Systematic issues suggest data source problems
- **Youth/Reserve teams**: Often missing (Ajax Amateurs, Real Madrid C, etc.)
- **Newly promoted teams**: Higher likelihood of missing stats
- **Non-Latin character teams**: Potential encoding issues

## Recommendations

### Immediate Actions
1. **Investigate Netherlands data sources** - 4 leagues with major issues
2. **Review team name mapping** for top 20 problematic teams
3. **Check data completeness** for leagues with >50% skip rates
4. **Validate team statistics scraping** for affected leagues

### Medium-term Solutions
1. **Implement fuzzy matching** for team names
2. **Add fallback data sources** for missing team stats
3. **Create team name normalization** pipeline
4. **Enhance data validation** during scraping

### Long-term Improvements
1. **Develop comprehensive team database** with aliases
2. **Implement automated data quality monitoring**
3. **Create league-specific data validation rules**
4. **Build redundant data source architecture**

## Files Generated
- `missing_team_stats_analysis.txt` - Detailed breakdown by league
- `missing_team_stats_summary.txt` - Summary statistics and rankings
- `missing_team_stats_data.json` - Raw data for further analysis
- `analyze_missing_team_stats.py` - Analysis script for future use

## Next Steps
1. Review detailed analysis files for specific team/match patterns
2. Prioritize fixes based on league importance and skip percentage
3. Investigate data source issues for Netherlands leagues
4. Implement team name mapping improvements for problematic teams
