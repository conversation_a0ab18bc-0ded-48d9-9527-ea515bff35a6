#!/usr/bin/env python3
"""
Test the case sensitivity fix on the most problematic leagues.
"""

import sys
import os
sys.path.insert(0, 'src')

from main import main as run_prediction
import logging
import re
from pathlib import Path

def test_league(league_name):
    """Test a single league and capture the results."""
    
    print(f"\n{'='*60}")
    print(f"TESTING: {league_name}")
    print(f"{'='*60}")
    
    # Capture logs
    import io
    log_capture = io.StringIO()
    
    # Set up logging to capture warnings
    logger = logging.getLogger()
    handler = logging.StreamHandler(log_capture)
    handler.setLevel(logging.WARNING)
    logger.addHandler(handler)
    
    # Also capture info messages for case-insensitive matches
    info_capture = io.StringIO()
    info_handler = logging.StreamHandler(info_capture)
    info_handler.setLevel(logging.INFO)
    logger.addHandler(info_handler)
    
    try:
        # Run the prediction system for this league
        sys.argv = ['main.py', '--league', league_name, '--quick']
        run_prediction()
        
        # Analyze the logs
        warning_output = log_capture.getvalue()
        info_output = info_capture.getvalue()
        
        # Count missing team stats warnings
        missing_warnings = len(re.findall(r'Missing team stats for match:', warning_output))
        
        # Count case-insensitive matches found
        case_insensitive_matches = len(re.findall(r'Found teams using case-insensitive matching:', info_output))
        
        # Count fuzzy matches found
        fuzzy_matches = len(re.findall(r'Found teams using fuzzy matching:', info_output))
        
        # Extract processing summary
        processing_match = re.search(r'Processing (\d+) valid matches\. Skipped (\d+) matches\.', warning_output + info_output)
        
        if processing_match:
            valid_matches = int(processing_match.group(1))
            skipped_matches = int(processing_match.group(2))
            total_matches = valid_matches + skipped_matches
            skip_percentage = (skipped_matches / total_matches * 100) if total_matches > 0 else 0
        else:
            valid_matches = skipped_matches = total_matches = skip_percentage = 0
        
        print(f"📊 RESULTS:")
        print(f"   Missing team stats warnings: {missing_warnings}")
        print(f"   Case-insensitive matches found: {case_insensitive_matches}")
        print(f"   Fuzzy matches found: {fuzzy_matches}")
        print(f"   Total matches: {total_matches}")
        print(f"   Valid matches: {valid_matches}")
        print(f"   Skipped matches: {skipped_matches}")
        print(f"   Skip percentage: {skip_percentage:.1f}%")
        
        if missing_warnings == 0:
            print("   🎉 NO MISSING TEAM STATS WARNINGS!")
        elif case_insensitive_matches > 0 or fuzzy_matches > 0:
            print(f"   ✅ Fix working: {case_insensitive_matches + fuzzy_matches} matches resolved")
        
        return {
            'league': league_name,
            'missing_warnings': missing_warnings,
            'case_insensitive_matches': case_insensitive_matches,
            'fuzzy_matches': fuzzy_matches,
            'total_matches': total_matches,
            'valid_matches': valid_matches,
            'skipped_matches': skipped_matches,
            'skip_percentage': skip_percentage,
            'success': missing_warnings == 0
        }
        
    except Exception as e:
        print(f"❌ Error testing {league_name}: {e}")
        return {
            'league': league_name,
            'error': str(e),
            'success': False
        }
    
    finally:
        logger.removeHandler(handler)
        logger.removeHandler(info_handler)

def main():
    """Test the fix on the most problematic leagues."""
    
    print("TESTING CASE SENSITIVITY FIX ON PROBLEMATIC LEAGUES")
    print("=" * 60)
    print("Testing the leagues that had the most missing team stats warnings")
    print("to verify that our case sensitivity fix resolves the issues.")
    print()
    
    # Top problematic leagues from our analysis
    test_leagues = [
        "SLOVENIA_PRVA_LIGA",           # 100% case differences, small dataset
        "NETHERLANDS_TWEEDE_DIVISIE",   # 100% case differences  
        "NETHERLANDS_EREDIVISIE",       # 100% case differences, major league
        "GERMANY_OBERLIGA_HAMBURG",     # 67% case differences
    ]
    
    results = []
    
    for league in test_leagues:
        result = test_league(league)
        results.append(result)
    
    # Summary
    print(f"\n{'='*80}")
    print("SUMMARY OF FIX TESTING")
    print(f"{'='*80}")
    
    successful_leagues = 0
    total_warnings_before = 0  # From our previous analysis
    total_warnings_after = 0
    total_case_insensitive = 0
    total_fuzzy = 0
    
    # Expected warnings from our previous analysis
    expected_warnings = {
        "SLOVENIA_PRVA_LIGA": 54,  # From the log example
        "NETHERLANDS_TWEEDE_DIVISIE": 144,
        "NETHERLANDS_EREDIVISIE": 141,
        "GERMANY_OBERLIGA_HAMBURG": 136
    }
    
    for result in results:
        if 'error' in result:
            print(f"❌ {result['league']}: ERROR - {result['error']}")
            continue
            
        league = result['league']
        warnings_before = expected_warnings.get(league, 0)
        warnings_after = result['missing_warnings']
        case_insensitive = result['case_insensitive_matches']
        fuzzy = result['fuzzy_matches']
        
        total_warnings_before += warnings_before
        total_warnings_after += warnings_after
        total_case_insensitive += case_insensitive
        total_fuzzy += fuzzy
        
        if result['success']:
            successful_leagues += 1
            status = "✅ FIXED"
        else:
            status = f"⚠️  {warnings_after} warnings remain"
        
        improvement = warnings_before - warnings_after
        improvement_pct = (improvement / warnings_before * 100) if warnings_before > 0 else 0
        
        print(f"{league}:")
        print(f"   Before: {warnings_before} warnings")
        print(f"   After: {warnings_after} warnings")
        print(f"   Improvement: -{improvement} ({improvement_pct:.1f}%)")
        print(f"   Case-insensitive matches: {case_insensitive}")
        print(f"   Fuzzy matches: {fuzzy}")
        print(f"   Status: {status}")
        print()
    
    print(f"📊 OVERALL RESULTS:")
    print(f"   Leagues tested: {len(test_leagues)}")
    print(f"   Leagues completely fixed: {successful_leagues}")
    print(f"   Total warnings before: {total_warnings_before}")
    print(f"   Total warnings after: {total_warnings_after}")
    print(f"   Total improvement: -{total_warnings_before - total_warnings_after}")
    print(f"   Overall improvement: {((total_warnings_before - total_warnings_after) / total_warnings_before * 100):.1f}%")
    print(f"   Case-insensitive matches found: {total_case_insensitive}")
    print(f"   Fuzzy matches found: {total_fuzzy}")
    
    if successful_leagues == len(test_leagues):
        print(f"\n🎉 SUCCESS! All tested leagues are now working without missing team stats warnings!")
        print(f"   The case sensitivity fix has resolved the issues.")
        print(f"   Ready to apply to all 202 affected leagues.")
    else:
        print(f"\n⚠️  {len(test_leagues) - successful_leagues} leagues still have issues.")
        print(f"   The fix is working but may need additional improvements.")

if __name__ == "__main__":
    main()
