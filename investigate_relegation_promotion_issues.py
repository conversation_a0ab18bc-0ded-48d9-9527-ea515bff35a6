#!/usr/bin/env python3
"""
Investigate remaining missing team stats issues focusing on relegation/promotion scenarios.
"""

import pandas as pd
import json
import os
import requests
from pathlib import Path
from collections import defaultdict
import re

def load_remaining_issues():
    """Load the remaining missing team stats issues after the case sensitivity fix."""
    
    with open('missing_team_stats_data.json', 'r') as f:
        data = json.load(f)
    
    return data

def analyze_team_patterns(missing_data):
    """Analyze patterns in the remaining missing teams."""
    
    print("🔍 ANALYZING REMAINING MISSING TEAM PATTERNS")
    print("=" * 60)
    
    # Collect all missing teams and their leagues
    team_league_map = defaultdict(list)
    league_missing_counts = {}
    
    for league, info in missing_data.items():
        league_missing_counts[league] = info['warning_count']
        for team in info['teams_affected']:
            team_league_map[team].append(league)
    
    # Find teams appearing in multiple leagues
    multi_league_teams = {team: leagues for team, leagues in team_league_map.items() if len(leagues) > 1}
    
    print(f"📊 SUMMARY:")
    print(f"   Total leagues with issues: {len(missing_data)}")
    print(f"   Total unique teams affected: {len(team_league_map)}")
    print(f"   Teams appearing in multiple leagues: {len(multi_league_teams)}")
    
    if multi_league_teams:
        print(f"\n🔄 TEAMS IN MULTIPLE LEAGUES (Potential Relegation/Promotion):")
        for team, leagues in sorted(multi_league_teams.items(), key=lambda x: len(x[1]), reverse=True):
            print(f"   {team}: {leagues}")
    
    # Analyze league hierarchies
    print(f"\n🏆 LEAGUE HIERARCHY ANALYSIS:")
    hierarchy_patterns = analyze_league_hierarchies(multi_league_teams)
    
    return team_league_map, multi_league_teams, hierarchy_patterns

def analyze_league_hierarchies(multi_league_teams):
    """Analyze if teams appear in related league hierarchies."""
    
    hierarchy_patterns = defaultdict(list)
    
    # Common league hierarchy patterns
    hierarchy_keywords = {
        'primera_segunda': ['PRIMERA', 'SEGUNDA'],
        'division_levels': ['DIVISION_1', 'DIVISION_2', 'FIRST_DIVISION', 'SECOND_DIVISION'],
        'serie_levels': ['SERIE_A', 'SERIE_B', 'SERIE_C', 'SERIE_D'],
        'liga_levels': ['1_LIGA', '2_LIGA', '3_LIGA'],
        'bundesliga_levels': ['BUNDESLIGA', '2_BUNDESLIGA', '3_LIGA'],
        'national_levels': ['NATIONAL', 'NATIONAL_2', 'NATIONAL_3'],
        'premier_championship': ['PREMIER', 'CHAMPIONSHIP'],
        'oberliga_regional': ['OBERLIGA', 'REGIONALLIGA']
    }
    
    for team, leagues in multi_league_teams.items():
        for pattern_name, keywords in hierarchy_keywords.items():
            matching_leagues = []
            for league in leagues:
                for keyword in keywords:
                    if keyword in league:
                        matching_leagues.append(league)
                        break
            
            if len(matching_leagues) > 1:
                hierarchy_patterns[pattern_name].append({
                    'team': team,
                    'leagues': matching_leagues
                })
    
    for pattern_name, teams in hierarchy_patterns.items():
        if teams:
            print(f"\n   {pattern_name.upper()} Pattern:")
            for item in teams[:5]:  # Show first 5
                print(f"     {item['team']}: {item['leagues']}")
            if len(teams) > 5:
                print(f"     ... and {len(teams) - 5} more")
    
    return hierarchy_patterns

def investigate_specific_cases(missing_data):
    """Investigate specific high-impact cases."""
    
    print(f"\n🎯 INVESTIGATING HIGH-IMPACT CASES")
    print("=" * 50)
    
    # Sort leagues by warning count
    sorted_leagues = sorted(missing_data.items(), key=lambda x: x[1]['warning_count'], reverse=True)
    
    top_cases = sorted_leagues[:5]
    
    for league, info in top_cases:
        print(f"\n📋 {league}:")
        print(f"   Warnings: {info['warning_count']}")
        print(f"   Teams affected: {len(info['teams_affected'])}")
        print(f"   Skip percentage: {info['skipped_matches']/info['total_matches']*100:.1f}%")
        
        # Check if this looks like a relegation/promotion issue
        teams = info['teams_affected']
        print(f"   Missing teams: {teams[:5]}{'...' if len(teams) > 5 else ''}")
        
        # Try to identify the league level/country
        country_match = re.match(r'^([A-Z_]+)_', league)
        if country_match:
            country = country_match.group(1)
            print(f"   Country: {country}")
            
            # Look for related leagues in the same country
            related_leagues = [l for l in missing_data.keys() if l.startswith(country + '_')]
            if len(related_leagues) > 1:
                print(f"   Related leagues in {country}: {related_leagues}")

def check_team_data_files(league_name, missing_teams):
    """Check what's actually in the team stats files for a league."""
    
    print(f"\n🔍 CHECKING DATA FILES FOR {league_name}")
    print("-" * 50)
    
    data_dir = f"data/raw/{league_name}"
    
    if not os.path.exists(data_dir):
        print(f"❌ Data directory not found: {data_dir}")
        return
    
    # Check results file
    results_file = os.path.join(data_dir, f"{league_name}_results.csv")
    team_stats_file = os.path.join(data_dir, f"{league_name}_team_stats.csv")
    
    if os.path.exists(results_file) and os.path.exists(team_stats_file):
        try:
            results_df = pd.read_csv(results_file)
            team_stats_df = pd.read_csv(team_stats_file)
            
            # Get teams from results
            teams_in_results = set()
            if 'Home Team' in results_df.columns and 'Away Team' in results_df.columns:
                teams_in_results.update(results_df['Home Team'].dropna().unique())
                teams_in_results.update(results_df['Away Team'].dropna().unique())
            
            # Get teams from stats
            teams_in_stats = set()
            if 'Team' in team_stats_df.columns:
                teams_in_stats.update(team_stats_df['Team'].dropna().unique())
            
            print(f"📊 Data Analysis:")
            print(f"   Teams in results: {len(teams_in_results)}")
            print(f"   Teams in team stats: {len(teams_in_stats)}")
            print(f"   Missing from stats: {len(teams_in_results - teams_in_stats)}")
            
            missing_from_stats = teams_in_results - teams_in_stats
            extra_in_stats = teams_in_stats - teams_in_results
            
            if missing_from_stats:
                print(f"   Missing teams: {list(missing_from_stats)[:5]}{'...' if len(missing_from_stats) > 5 else ''}")
            
            if extra_in_stats:
                print(f"   Extra teams in stats: {list(extra_in_stats)[:5]}{'...' if len(extra_in_stats) > 5 else ''}")
                
                # This could indicate relegated/promoted teams
                if missing_from_stats and extra_in_stats:
                    print(f"   🔄 POTENTIAL RELEGATION/PROMOTION SCENARIO:")
                    print(f"      Teams in stats but not in current results might be from previous season")
                    print(f"      Teams in results but not in stats might be newly promoted")
            
        except Exception as e:
            print(f"❌ Error analyzing files: {e}")
    else:
        print(f"❌ Required files not found")

def web_search_team_status(team_name, league_name):
    """Search web for team's current status (relegated, promoted, etc.)."""
    
    print(f"\n🌐 WEB SEARCH: {team_name} in {league_name}")
    print("-" * 40)
    
    # Extract country from league name
    country_match = re.match(r'^([A-Z_]+)_', league_name)
    country = country_match.group(1).replace('_', ' ') if country_match else "unknown"
    
    # Create search queries
    search_queries = [
        f"{team_name} {country} football relegated promoted 2024",
        f"{team_name} {country} soccer league transfer 2024",
        f"{team_name} football club current league 2024"
    ]
    
    print(f"🔍 Search queries for {team_name}:")
    for i, query in enumerate(search_queries, 1):
        print(f"   {i}. {query}")
    
    # Note: In a real implementation, you would use web search APIs
    # For now, we'll provide the search queries for manual investigation
    
    return search_queries

def generate_investigation_report(missing_data):
    """Generate a comprehensive investigation report."""
    
    print(f"\n📋 GENERATING INVESTIGATION REPORT")
    print("=" * 50)
    
    team_league_map, multi_league_teams, hierarchy_patterns = analyze_team_patterns(missing_data)
    
    # Investigate top problematic leagues
    sorted_leagues = sorted(missing_data.items(), key=lambda x: x[1]['warning_count'], reverse=True)
    
    print(f"\n🎯 DETAILED INVESTIGATION OF TOP 3 LEAGUES:")
    
    for i, (league, info) in enumerate(sorted_leagues[:3], 1):
        print(f"\n{i}. {league}")
        check_team_data_files(league, info['teams_affected'])
        
        # For the top case, suggest web searches
        if i == 1:
            print(f"\n🌐 SUGGESTED WEB SEARCHES:")
            for team in info['teams_affected'][:3]:  # Top 3 missing teams
                queries = web_search_team_status(team, league)
    
    # Summary and recommendations
    print(f"\n📊 INVESTIGATION SUMMARY:")
    print(f"   Total remaining issues: {len(missing_data)} leagues")
    print(f"   Teams in multiple leagues: {len(multi_league_teams)}")
    print(f"   Hierarchy patterns found: {len([p for p in hierarchy_patterns.values() if p])}")
    
    print(f"\n💡 RECOMMENDATIONS:")
    print(f"   1. Focus on teams appearing in multiple leagues (relegation/promotion)")
    print(f"   2. Check if team stats are from previous season vs current results")
    print(f"   3. Investigate league hierarchy relationships")
    print(f"   4. Consider implementing season-aware team mapping")
    print(f"   5. Add fallback to previous season data for relegated teams")

def main():
    """Main investigation function."""
    
    print("INVESTIGATING RELEGATION/PROMOTION ISSUES")
    print("=" * 60)
    print("Analyzing remaining missing team stats issues after case sensitivity fix")
    print("Focus: Teams appearing in multiple leagues (relegation/promotion scenarios)")
    print()
    
    # Load the remaining issues
    missing_data = load_remaining_issues()
    
    if not missing_data:
        print("✅ No remaining missing team stats issues found!")
        return
    
    # Generate comprehensive investigation
    generate_investigation_report(missing_data)
    
    print(f"\n🎯 NEXT STEPS:")
    print(f"   1. Manually verify relegation/promotion status for top missing teams")
    print(f"   2. Check if team stats files contain previous season data")
    print(f"   3. Consider implementing season-aware data handling")
    print(f"   4. Add team status tracking (active, relegated, promoted)")

if __name__ == "__main__":
    main()
