#!/usr/bin/env python3
"""
Test script to verify the case sensitivity fix works correctly.
"""

import sys
import os
sys.path.insert(0, 'src')

import pandas as pd
from feature_engineering.utils import validate_team_stats
import logging

# Set up logging to see the info messages
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')

def test_case_sensitivity_fix():
    """Test the case sensitivity fix with real examples from our investigation."""
    
    print("🧪 TESTING CASE SENSITIVITY FIX")
    print("=" * 50)
    
    # Test data based on real examples from SLOVENIA_PRVA_LIGA
    team_stats = pd.DataFrame({
        'Team': ['Nk Celje', 'Nk Olimpija', 'Bravo', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>ft<PERSON>', 'Primorje', 'Radomlje'],
        'goals_scored_per_match_home': [1.5, 2.1, 1.2, 1.8, 1.4, 2.0, 1.3, 1.1, 1.0, 1.6],
        'goals_conceded_per_match_home': [0.8, 1.0, 1.5, 1.2, 1.1, 0.9, 1.4, 1.8, 1.6, 1.3],
        'total_home_wins': [8, 10, 5, 7, 6, 9, 4, 3, 2, 6],
        'total_home_played': [15, 15, 15, 15, 15, 15, 15, 15, 15, 15]
    })
    
    print(f"📊 Test data: {len(team_stats)} teams")
    print(f"   Available teams: {team_stats['Team'].tolist()}")
    print()
    
    # Test cases from the log file (these were failing before)
    test_cases = [
        ("NK Celje", "Bravo"),           # Case difference: NK vs Nk
        ("NK Olimpija", "Bravo"),        # Case difference: NK vs Nk  
        ("Bravo", "NK Celje"),          # Case difference
        ("Bravo", "NK Olimpija"),       # Case difference
        ("Domzale", "NK Olimpija"),     # Mixed case
        ("Domzale", "NK Celje"),        # Mixed case
    ]
    
    successful_matches = 0
    case_insensitive_matches = 0
    fuzzy_matches = 0
    failed_matches = 0
    
    for i, (home_team, away_team) in enumerate(test_cases, 1):
        print(f"Test {i}: {home_team} vs {away_team}")
        
        # Capture log messages to see which matching method was used
        import io
        import logging
        
        log_capture = io.StringIO()
        handler = logging.StreamHandler(log_capture)
        handler.setLevel(logging.INFO)
        logger = logging.getLogger('feature_engineering.utils')
        logger.addHandler(handler)
        
        try:
            home_stats, away_stats = validate_team_stats(home_team, away_team, team_stats)
            
            if home_stats is not None and away_stats is not None:
                successful_matches += 1
                
                # Check log output to see which method was used
                log_output = log_capture.getvalue()
                if "case-insensitive matching" in log_output:
                    case_insensitive_matches += 1
                    print(f"   ✅ SUCCESS (Case-insensitive): Found {home_stats['Team']} vs {away_stats['Team']}")
                elif "fuzzy matching" in log_output:
                    fuzzy_matches += 1
                    print(f"   ✅ SUCCESS (Fuzzy): Found {home_stats['Team']} vs {away_stats['Team']}")
                else:
                    print(f"   ✅ SUCCESS (Exact): Found {home_stats['Team']} vs {away_stats['Team']}")
            else:
                failed_matches += 1
                print(f"   ❌ FAILED: Could not find team stats")
                
        except Exception as e:
            failed_matches += 1
            print(f"   ❌ ERROR: {e}")
        
        finally:
            logger.removeHandler(handler)
        
        print()
    
    # Summary
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 30)
    print(f"Total test cases: {len(test_cases)}")
    print(f"Successful matches: {successful_matches}")
    print(f"  - Case-insensitive: {case_insensitive_matches}")
    print(f"  - Fuzzy matching: {fuzzy_matches}")
    print(f"  - Exact matching: {successful_matches - case_insensitive_matches - fuzzy_matches}")
    print(f"Failed matches: {failed_matches}")
    print(f"Success rate: {successful_matches/len(test_cases)*100:.1f}%")
    
    if successful_matches == len(test_cases):
        print("\n🎉 ALL TESTS PASSED! The fix is working correctly.")
        return True
    else:
        print(f"\n⚠️  {failed_matches} tests failed. The fix may need adjustment.")
        return False

def test_with_real_league_data():
    """Test with actual league data if available."""
    
    print("\n🏆 TESTING WITH REAL LEAGUE DATA")
    print("=" * 40)
    
    # Try to load SLOVENIA_PRVA_LIGA data
    league_name = "SLOVENIA_PRVA_LIGA"
    data_dir = f"data/raw/{league_name}"
    
    results_file = os.path.join(data_dir, f"{league_name}_results.csv")
    team_stats_file = os.path.join(data_dir, f"{league_name}_team_stats.csv")
    
    if not os.path.exists(results_file) or not os.path.exists(team_stats_file):
        print(f"⚠️  Real data files not found for {league_name}")
        print(f"   Looking for: {results_file}")
        print(f"   Looking for: {team_stats_file}")
        return False
    
    try:
        results_df = pd.read_csv(results_file)
        team_stats_df = pd.read_csv(team_stats_file)
        
        print(f"📊 Loaded real data:")
        print(f"   Results: {len(results_df)} matches")
        print(f"   Team stats: {len(team_stats_df)} teams")
        print(f"   Teams in results: {sorted(set(results_df['Home Team'].tolist() + results_df['Away Team'].tolist()))}")
        print(f"   Teams in stats: {sorted(team_stats_df['Team'].tolist())}")
        
        # Test a few real matches
        test_matches = results_df.head(5)
        successful = 0
        
        for _, match in test_matches.iterrows():
            home_team = match['Home Team']
            away_team = match['Away Team']
            
            home_stats, away_stats = validate_team_stats(home_team, away_team, team_stats_df)
            
            if home_stats is not None and away_stats is not None:
                successful += 1
                print(f"   ✅ {home_team} vs {away_team}")
            else:
                print(f"   ❌ {home_team} vs {away_team}")
        
        print(f"\n📊 Real data test: {successful}/{len(test_matches)} matches successful")
        return successful == len(test_matches)
        
    except Exception as e:
        print(f"❌ Error testing with real data: {e}")
        return False

def main():
    """Main test function."""
    
    # Test with synthetic data
    synthetic_success = test_case_sensitivity_fix()
    
    # Test with real data if available
    real_data_success = test_with_real_league_data()
    
    print("\n" + "=" * 60)
    print("FINAL TEST RESULTS")
    print("=" * 60)
    
    if synthetic_success:
        print("✅ Synthetic data tests: PASSED")
    else:
        print("❌ Synthetic data tests: FAILED")
    
    if real_data_success:
        print("✅ Real data tests: PASSED")
    elif real_data_success is False:
        print("❌ Real data tests: FAILED")
    else:
        print("⚠️  Real data tests: SKIPPED (data not available)")
    
    if synthetic_success:
        print("\n🎉 The case sensitivity fix is working!")
        print("   Ready to test on the full dataset.")
    else:
        print("\n⚠️  The fix needs adjustment before full testing.")

if __name__ == "__main__":
    main()
