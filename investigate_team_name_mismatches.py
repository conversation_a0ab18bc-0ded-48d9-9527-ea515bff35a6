#!/usr/bin/env python3
"""
Script to investigate team name mismatches in leagues with missing team stats warnings.
"""

import pandas as pd
import os
import json
from pathlib import Path
from collections import defaultdict

def investigate_league_team_names(league_name):
    """Investigate team name mismatches for a specific league."""
    print(f"\n{'='*60}")
    print(f"INVESTIGATING: {league_name}")
    print(f"{'='*60}")
    
    data_dir = f"data/raw/{league_name}"
    
    # Check if data directory exists
    if not os.path.exists(data_dir):
        print(f"❌ Data directory not found: {data_dir}")
        return None
    
    # Load results file
    results_file = os.path.join(data_dir, f"{league_name}_results.csv")
    if not os.path.exists(results_file):
        print(f"❌ Results file not found: {results_file}")
        return None
    
    # Load team stats file
    team_stats_file = os.path.join(data_dir, f"{league_name}_team_stats.csv")
    if not os.path.exists(team_stats_file):
        print(f"❌ Team stats file not found: {team_stats_file}")
        return None
    
    try:
        results_df = pd.read_csv(results_file)
        team_stats_df = pd.read_csv(team_stats_file)
        
        print(f"📊 Results file: {len(results_df)} matches")
        print(f"📊 Team stats file: {len(team_stats_df)} teams")
        
        # Extract teams from results
        teams_in_results = set()
        if 'Home Team' in results_df.columns and 'Away Team' in results_df.columns:
            teams_in_results.update(results_df['Home Team'].dropna().unique())
            teams_in_results.update(results_df['Away Team'].dropna().unique())
        
        # Extract teams from team stats
        teams_in_stats = set()
        if 'Team' in team_stats_df.columns:
            teams_in_stats.update(team_stats_df['Team'].dropna().unique())
        
        print(f"🏠 Teams in results: {len(teams_in_results)}")
        print(f"📈 Teams in team stats: {len(teams_in_stats)}")
        
        # Find mismatches
        missing_from_stats = teams_in_results - teams_in_stats
        extra_in_stats = teams_in_stats - teams_in_results
        
        print(f"\n🔍 ANALYSIS:")
        print(f"   Teams missing from team stats: {len(missing_from_stats)}")
        print(f"   Extra teams in team stats: {len(extra_in_stats)}")
        
        if missing_from_stats:
            print(f"\n❌ MISSING FROM TEAM STATS:")
            for team in sorted(missing_from_stats):
                print(f"   - '{team}'")
        
        if extra_in_stats:
            print(f"\n➕ EXTRA IN TEAM STATS:")
            for team in sorted(extra_in_stats):
                print(f"   - '{team}'")
        
        # Look for potential matches (case differences, etc.)
        if missing_from_stats and extra_in_stats:
            print(f"\n🔄 POTENTIAL MATCHES:")
            for missing_team in missing_from_stats:
                for extra_team in extra_in_stats:
                    if missing_team.lower() == extra_team.lower():
                        print(f"   Case difference: '{missing_team}' vs '{extra_team}'")
                    elif missing_team.replace(' ', '') == extra_team.replace(' ', ''):
                        print(f"   Space difference: '{missing_team}' vs '{extra_team}'")
                    elif missing_team.replace('.', '') == extra_team.replace('.', ''):
                        print(f"   Dot difference: '{missing_team}' vs '{extra_team}'")
        
        return {
            'league_name': league_name,
            'teams_in_results': teams_in_results,
            'teams_in_stats': teams_in_stats,
            'missing_from_stats': missing_from_stats,
            'extra_in_stats': extra_in_stats
        }
        
    except Exception as e:
        print(f"❌ Error analyzing {league_name}: {e}")
        return None

def investigate_top_problematic_leagues():
    """Investigate the top leagues with most missing team stats warnings."""
    
    # Top leagues from our analysis
    top_leagues = [
        "ARGENTINA_PRIMERA_NACIONAL",
        "NETHERLANDS_EERSTE_DIVISIE", 
        "NETHERLANDS_DERDE_DIVISIE_SATURDAY",
        "NETHERLANDS_TWEEDE_DIVISIE",
        "NETHERLANDS_EREDIVISIE",
        "GERMANY_OBERLIGA_HAMBURG",
        "SPAIN_SEGUNDA_RFEF_GROUP_5",
        "SLOVENIA_PRVA_LIGA"  # From the example in the log
    ]
    
    results = {}
    
    for league in top_leagues:
        result = investigate_league_team_names(league)
        if result:
            results[league] = result
    
    return results

def analyze_patterns(results):
    """Analyze patterns across all investigated leagues."""
    print(f"\n{'='*80}")
    print("PATTERN ANALYSIS ACROSS ALL LEAGUES")
    print(f"{'='*80}")
    
    total_leagues = len(results)
    total_missing = sum(len(r['missing_from_stats']) for r in results.values())
    total_extra = sum(len(r['extra_in_stats']) for r in results.values())
    
    print(f"📊 Summary:")
    print(f"   Leagues analyzed: {total_leagues}")
    print(f"   Total teams missing from stats: {total_missing}")
    print(f"   Total extra teams in stats: {total_extra}")
    
    # Analyze common patterns
    case_differences = 0
    space_differences = 0
    dot_differences = 0
    
    for league_data in results.values():
        missing = league_data['missing_from_stats']
        extra = league_data['extra_in_stats']
        
        for missing_team in missing:
            for extra_team in extra:
                if missing_team.lower() == extra_team.lower():
                    case_differences += 1
                elif missing_team.replace(' ', '') == extra_team.replace(' ', ''):
                    space_differences += 1
                elif missing_team.replace('.', '') == extra_team.replace('.', ''):
                    dot_differences += 1
    
    print(f"\n🔍 Common mismatch patterns:")
    print(f"   Case differences: {case_differences}")
    print(f"   Space differences: {space_differences}")
    print(f"   Dot differences: {dot_differences}")
    
    # Find leagues with perfect mismatches (same number missing and extra)
    perfect_mismatches = []
    for league, data in results.items():
        if len(data['missing_from_stats']) == len(data['extra_in_stats']) and len(data['missing_from_stats']) > 0:
            perfect_mismatches.append(league)
    
    if perfect_mismatches:
        print(f"\n🎯 Leagues with perfect mismatches (same count missing/extra):")
        for league in perfect_mismatches:
            print(f"   - {league}")

def main():
    print("INVESTIGATING TEAM NAME MISMATCHES")
    print("=" * 80)
    print("This script investigates why teams appear in match results")
    print("but are missing from team statistics files.")
    
    results = investigate_top_problematic_leagues()
    
    if results:
        analyze_patterns(results)
        
        # Save detailed results
        output_file = "team_name_mismatch_investigation.json"
        # Convert sets to lists for JSON serialization
        json_results = {}
        for league, data in results.items():
            json_results[league] = {
                'league_name': data['league_name'],
                'teams_in_results': list(data['teams_in_results']),
                'teams_in_stats': list(data['teams_in_stats']),
                'missing_from_stats': list(data['missing_from_stats']),
                'extra_in_stats': list(data['extra_in_stats'])
            }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(json_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Detailed results saved to: {output_file}")
    else:
        print("❌ No results to analyze")

if __name__ == "__main__":
    main()
