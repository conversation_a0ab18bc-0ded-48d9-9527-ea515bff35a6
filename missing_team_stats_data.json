{"SPAIN_SEGUNDA_RFEF_GROUP_5": {"warning_count": 130, "missing_matches": ["Atletico Paso vs Talavera CF", "Yaiza vs Atletico Paso", "CD Coria vs Atletico Paso", "Atletico Paso vs CD Mostoles", "Colonia M. vs Atletico Paso", "Talavera CF vs Atletico Paso", "Atletico Paso vs Yaiza", "Atletico Paso vs CD Coria", "CD Mostoles vs Atletico Paso", "Atletico Paso vs Colonia M.", "Cacereno vs CD Mostoles", "Colonia M. vs Cacereno", "Talavera CF vs Cacereno", "Yaiza vs Cacereno", "CD Coria vs Cacereno", "CD Mostoles vs Cacereno", "Cacereno vs Colonia M.", "Cacereno vs Talavera CF", "Cacereno vs Yaiza", "Cacereno vs CD Coria", "CD Coria vs Conquense", "Conquense vs CD Mostoles", "Colonia M. vs Conquense", "Talavera CF vs Conquense", "Conquense vs Yaiza", "Conquense vs CD Coria", "CD Mostoles vs Conquense", "Conquense vs Colonia M.", "Conquense vs Talavera CF", "Yaiza vs Conquense", "Getafe B vs Yaiza", "Getafe B vs CD Coria", "Getafe B vs CD Mostoles", "Colonia M. vs Getafe B", "Talavera CF vs Getafe B", "Yaiza vs Getafe B", "CD Coria vs Getafe B", "CD Mostoles vs Getafe B", "Getafe B vs Colonia M.", "Getafe B vs Talavera CF", "Guadalajara vs Talavera CF", "Yaiza vs Guadalajara", "CD Coria vs Guadalajara", "Guadalajara vs CD Mostoles", "Colonia M. vs Guadalajara", "Talavera CF vs Guadalajara", "Guadalajara vs Yaiza", "Guadalajara vs CD Coria", "CD Mostoles vs Guadalajara", "Guadalajara vs Colonia M.", "Yaiza vs Illescas", "Illescas vs CD Coria", "CD Mostoles vs Illescas", "Illescas vs Colonia M.", "Illescas vs Talavera CF", "Illescas vs Yaiza", "CD Coria vs Illescas", "Illescas vs CD Mostoles", "Colonia M. vs Illescas", "Talavera CF vs Illescas", "Talavera CF vs Melilla", "Melilla vs Yaiza", "Melilla vs CD Coria", "CD Mostoles vs Melilla", "Melilla vs Colonia M.", "Melilla vs Talavera CF", "Yaiza vs Melilla", "CD Coria vs Melilla", "Melilla vs CD Mostoles", "Colonia M. vs Melilla", "<PERSON><PERSON> vs Navalcarnero", "CD Coria vs Navalcarnero", "CD Mostoles vs Navalcarnero", "Navalcarnero vs Colonia M.", "Navalcarnero vs Talavera CF", "Navalcarnero vs <PERSON><PERSON>", "Navalcarnero vs CD Coria", "Navalcarnero vs CD Mostoles", "Colonia M. vs Navalcarnero", "Talavera CF vs Navalcarnero", "<PERSON><PERSON> vs CD Coria", "CD Mostoles vs <PERSON><PERSON>", "<PERSON><PERSON> vs Colonia M.", "<PERSON><PERSON> vs Talavera CF", "<PERSON><PERSON> vs <PERSON><PERSON>", "CD Coria vs <PERSON><PERSON>", "<PERSON><PERSON> vs CD Mostoles", "Colonia M. vs <PERSON><PERSON>", "Talavera CF vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "Real Madrid C vs CD Coria", "CD Mostoles vs Real Madrid C", "Real Madrid C vs Colonia M.", "Real Madrid C vs Talavera CF", "Real Madrid C vs Yaiza", "CD Coria vs Real Madrid C", "Real Madrid C vs CD Mostoles", "Colonia M. vs Real Madrid C", "Talavera CF vs Real Madrid C", "Yaiza vs Real Madrid C", "<PERSON> Mostoles vs <PERSON><PERSON>.", "<PERSON><PERSON> vs Colonia M.", "S. <PERSON> R. vs Talavera CF", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs CD Coria", "<PERSON><PERSON> vs CD Mostoles", "Colonia M. vs <PERSON><PERSON>.", "Talavera CF vs S. Sebastian R.", "<PERSON><PERSON> vs <PERSON><PERSON>.", "CD Coria vs S<PERSON>.", "Tenerife B vs Talavera CF", "CD Coria vs Tenerife B", "CD Mostoles vs Tenerife B", "Colonia M. vs Tenerife B", "Tenerife B vs Yaiza", "Talavera CF vs Tenerife B", "Tenerife B vs CD Coria", "Tenerife B vs CD Mostoles", "Tenerife B vs Colonia M.", "Yaiza vs Tenerife B", "Union Adarve vs CD Mostoles", "Colonia M. vs Union Adarve", "Talavera CF vs Union Adarve", "Yaiza vs Union Adarve", "CD Coria vs Union Adarve", "CD Mostoles vs Union Adarve", "Union Adarve vs Colonia M.", "Union Adarve vs Talavera CF", "Union Adarve vs Yaiza", "Union Adarve vs CD Coria"], "teams_affected": ["Talavera CF", "Atletico Paso", "Cacereno", "CD Coria", "Real Madrid C", "Conquense", "Illescas", "<PERSON><PERSON>", "<PERSON><PERSON>", "Guadalajara", "<PERSON><PERSON>", "Navalcarnero", "Getafe B", "Melilla", "Tenerife B", "Union Adarve", "Colonia M.", "CD Mostoles"], "total_matches": 442, "valid_matches": 312, "skipped_matches": 130}, "INDIA_I-LEAGUE": {"warning_count": 84, "missing_matches": ["Aizawl vs SC Bengaluru", "Aizawl vs Delhi FC", "SC Bengaluru vs Aizawl", "Delhi FC vs Aizawl", "SC Bengaluru vs Churchill B.", "Delhi FC vs Churchill B.", "Churchill B. vs SC Bengaluru", "Churchill B. vs Delhi FC", "Namdhari vs Delhi FC", "Inter Kashi vs Delhi FC", "Real Kashmir vs Delhi FC", "Sreenidi Deccan vs Delhi FC", "SC Bengaluru vs Delhi FC", "Delhi FC vs Shillong Lajong", "Delhi FC vs Gokulam", "Delhi FC vs Rajasthan", "Delhi FC vs Churchill B.", "Aizawl vs Delhi FC", "Dempo vs Delhi FC", "Delhi FC vs Namdhari", "Delhi FC vs SC Bengaluru", "Churchill B. vs Delhi FC", "Gokulam vs Delhi FC", "Delhi FC vs Inter Kashi", "Delhi FC vs Dempo", "Delhi FC vs Sreenidi Deccan", "Delhi FC vs Aizawl", "Delhi FC vs Real Kashmir", "Rajasthan vs Delhi FC", "Shillong Lajong vs Delhi FC", "SC Bengaluru vs Dempo", "Dempo vs Delhi FC", "Delhi FC vs Dempo", "Dempo vs SC Bengaluru", "Delhi FC vs Gokulam", "Gokulam vs SC Bengaluru", "Gokulam vs Delhi FC", "SC Bengaluru vs Gokulam", "Inter Kashi vs SC Bengaluru", "Inter Kashi vs Delhi FC", "SC Bengaluru vs Inter Kashi", "Delhi FC vs Inter Kashi", "Namdhari vs Delhi FC", "SC Bengaluru vs Namdhari", "Delhi FC vs Namdhari", "<PERSON><PERSON><PERSON> vs SC Bengaluru", "Rajasthan vs SC Bengaluru", "Delhi FC vs Rajasthan", "SC Bengaluru vs Rajasthan", "Rajasthan vs Delhi FC", "Real Kashmir vs Delhi FC", "Real Kashmir vs SC Bengaluru", "SC Bengaluru vs Real Kashmir", "Delhi FC vs Real Kashmir", "Inter Kashi vs SC Bengaluru", "Aizawl vs SC Bengaluru", "SC Bengaluru vs Churchill B.", "SC Bengaluru vs Dempo", "SC Bengaluru vs Delhi FC", "SC Bengaluru vs Namdhari", "Rajasthan vs SC Bengaluru", "Shillong Lajong vs SC Bengaluru", "Real Kashmir vs SC Bengaluru", "SC Bengaluru vs Sreenidi Deccan", "Gokulam vs SC Bengaluru", "Churchill B. vs SC Bengaluru", "Delhi FC vs SC Bengaluru", "SC Bengaluru vs Rajasthan", "SC Bengaluru vs Inter Kashi", "SC Bengaluru vs Shillong Lajong", "SC Bengaluru vs Aizawl", "Dempo vs SC Bengaluru", "SC Bengaluru vs Real Kashmir", "SC Bengaluru vs Gokulam", "<PERSON><PERSON><PERSON> vs SC Bengaluru", "<PERSON><PERSON><PERSON><PERSON> vs SC Bengaluru", "Delhi FC vs Shillong Lajong", "Shillong Lajong vs SC Bengaluru", "SC Bengaluru vs Shillong Lajong", "Shillong Lajong vs Delhi FC", "Sreenidi Deccan vs Delhi FC", "SC Bengaluru vs Sreenidi Deccan", "Delhi FC vs Sreenidi Deccan", "<PERSON><PERSON><PERSON><PERSON> vs SC Bengaluru"], "teams_affected": ["Inter Kashi", "Real Kashmir", "Aizawl", "Shillong Lajong", "Churchill B.", "Dempo", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "SC Bengaluru", "Delhi FC", "Gokulam", "Rajasthan"], "total_matches": 264, "valid_matches": 180, "skipped_matches": 84}, "GERMANY_OBERLIGA_RHEINLAND_PFALZ_SAAR": {"warning_count": 58, "missing_matches": ["<PERSON><PERSON> vs R<PERSON>", "<PERSON><PERSON> vs Idar-Oberstein", "<PERSON><PERSON> vs A. <PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs Auersmacher", "<PERSON><PERSON><PERSON><PERSON> vs Idar-Oberstein", "<PERSON><PERSON><PERSON><PERSON> vs R<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs Auersmacher", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs Diefflen", "<PERSON><PERSON> vs Diefflen", "<PERSON><PERSON><PERSON> vs Idar-Oberstein", "Eisbachtal vs RW Ko<PERSON>nz", "Idar-Oberstein vs Eisbachtal", "RW <PERSON> vs Eisbachtal", "Eisbachtal vs Idar-Oberstein", "<PERSON><PERSON> vs Engers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs Engers", "<PERSON><PERSON> vs R<PERSON>nz", "E<PERSON>born vs RW <PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs Eppelborn", "<PERSON><PERSON> vs Eppelborn", "E<PERSON>born vs Idar-Oberstein", "RW Ko<PERSON>nz vs Gonsenheim", "Gonsenheim vs Idar-Oberstein", "Gonsenheim vs RW Ko<PERSON>nz", "Idar-Oberstein vs Gonsenheim", "Kaiserslaut. B vs RW <PERSON>nz", "Kaiserslaut. B vs Idar-Oberstein", "RW Ko<PERSON>nz vs Kaiserslaut. B", "Idar-Oberstein vs Kaiserslaut. B", "Idar<PERSON><PERSON><PERSON><PERSON> vs Karbach", "<PERSON><PERSON> vs Karbach", "<PERSON><PERSON><PERSON> vs Idar-Oberstein", "RW <PERSON> vs Koblenz", "Idar<PERSON><PERSON><PERSON><PERSON> vs Koblenz", "<PERSON><PERSON>nz vs RW Ko<PERSON>nz", "Koblenz vs Idar-Oberstein", "Mechtersheim vs Idar-Oberstein", "Mechtersheim vs RW <PERSON>nz", "Idar-Oberstein vs Mechtersheim", "RW Ko<PERSON>nz vs Mechtersheim", "Morlautern vs Idar-Oberstein", "Morlautern vs RW <PERSON>", "Idar<PERSON>Oberstein vs Morlautern", "<PERSON><PERSON><PERSON><PERSON> vs R<PERSON>", "<PERSON><PERSON><PERSON><PERSON> vs Idar-Oberstein", "R<PERSON> vs Pirmasens", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs Pirmasens", "R<PERSON> vs Schott Mainz", "<PERSON>r<PERSON><PERSON><PERSON><PERSON> vs Schott Mainz", "<PERSON><PERSON>t Mainz vs RW Koblenz", "<PERSON><PERSON> vs R<PERSON>", "<PERSON><PERSON> vs Idar-Oberstein", "R<PERSON> vs Viktoria Herxhe", "R<PERSON> vs Wormatia Worms", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs Wormatia Worms", "Wormatia Worms vs RW <PERSON>nz", "Wormatia Worms vs Idar-Oberstein"], "teams_affected": ["<PERSON><PERSON><PERSON>", "Kaiserslaut. B", "Morlautern", "<PERSON><PERSON><PERSON><PERSON>", "Eisbachtal", "<PERSON><PERSON><PERSON>", "Idar-<PERSON><PERSON>tein", "Ko<PERSON>nz", "Engers", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Wormatia Worms", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Gonsenheim", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Mechtersheim"], "total_matches": 496, "valid_matches": 438, "skipped_matches": 58}, "SOUTH_KOREA_K3_LEAGUE": {"warning_count": 40, "missing_matches": ["Gyeongju HNP vs Busan Transport", "Jeonbuk Motors vs Busan Transport", "Jeonbuk Motors vs Changwon", "Changwon vs Gyeongju HNP", "Chuncheon vs Jeonbuk Motors", "Chuncheon vs Gyeongju HNP", "Daejeon Korail vs Gyeongju HNP", "Jeonbuk Motors vs Daejeon Korail", "Jeonbuk Motors vs Gangneung City", "Gangneung City vs Gyeongju HNP", "Jeonbuk Motors vs Gimhae", "<PERSON><PERSON><PERSON><PERSON> vs Gyeongju HNP", "<PERSON><PERSON><PERSON><PERSON> vs Jeonbuk Motors", "Daejeon Korail vs Gyeongju HNP", "Gyeongju HNP vs Busan Transport", "<PERSON><PERSON><PERSON><PERSON> vs Gyeongju HNP", "Mokpo City vs Gyeongju HNP", "Gangneung City vs Gyeongju HNP", "Gyeongju HNP vs Siheung Citizen", "Gyeongju HNP vs Paju Citizen", "Gyeongju HNP vs Pocheon", "Chuncheon vs Gyeongju HNP", "Gyeongju HNP vs Yang<PERSON>eong", "Changwon vs Gyeongju HNP", "Gyeongju HNP vs Jeonbuk Motors", "<PERSON><PERSON><PERSON> vs Gyeongju HNP", "Ulsan Citizen vs Gyeongju HNP", "Mokpo City vs Gyeongju HNP", "Mokpo City vs Jeonbuk Motors", "Paju Citizen vs Jeonbuk Motors", "Gyeongju HNP vs Paju Citizen", "Gyeongju HNP vs Pocheon", "Pocheon vs Jeonbuk Motors", "Gyeongju HNP vs Siheung Citizen", "Jeonbuk Motors vs Siheung Citizen", "Ulsan Citizen vs Gyeongju HNP", "Gyeongju HNP vs Yang<PERSON>eong", "Jeonbuk Motors vs Yangpyeong", "<PERSON><PERSON><PERSON> vs Jeonbuk Motors", "<PERSON><PERSON><PERSON> vs Gyeongju HNP"], "teams_affected": ["Gangneung City", "<PERSON><PERSON><PERSON><PERSON>", "Gyeongju HNP", "<PERSON><PERSON><PERSON><PERSON>", "Daejeon Korail", "Busan Transport", "Siheung Citizen", "<PERSON><PERSON><PERSON>", "Mokpo City", "Paju <PERSON>", "Jeonbuk Motors", "Chuncheon", "Pocheon", "Ulsan Citizen", "Changwon"], "total_matches": 208, "valid_matches": 168, "skipped_matches": 40}, "BELARUS_VYSSHAYA_LIGA": {"warning_count": 3, "missing_matches": ["Maxline vs Arsenal D.", "Maxline vs Neman Grodno", "Slutsk vs Maxline"], "teams_affected": ["Arsenal D.", "Slutsk", "Maxline", "<PERSON><PERSON> Grodno"], "total_matches": 29, "valid_matches": 26, "skipped_matches": 3}, "GEORGIA_EROVNULI_LIGA_2": {"warning_count": 8, "missing_matches": ["Bolnisi vs Iberia B", "<PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "Gonio vs Spaeri", "<PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "Spaeri vs Samtredia", "Spaeri vs Iberia B", "Spaeri vs Gonio"], "teams_affected": ["<PERSON><PERSON>", "Gonio", "Bo<PERSON><PERSON>", "Samtredia", "Meshakh<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Iberia B"], "total_matches": 15, "valid_matches": 7, "skipped_matches": 8}, "AUSTRALIA_WESTERN_AUSTRALIA_NPL": {"warning_count": 1, "missing_matches": ["Perth SC vs Fremantle City"], "teams_affected": ["Fremantle City", "Perth SC"], "total_matches": 19, "valid_matches": 18, "skipped_matches": 1}, "TAJIKISTAN_VYSSHAYA_LIGA": {"warning_count": 3, "missing_matches": ["Regar-TadAZ vs Istiqlol D.", "Istiqlol D. vs CSKA Pomir", "Vakhsh vs Eskhata"], "teams_affected": ["CSKA Pomir", "Regar-TadAZ", "Istiqlol D.", "Esk<PERSON><PERSON>", "Vakhsh"], "total_matches": 12, "valid_matches": 9, "skipped_matches": 3}, "GERMANY_REGIONALLIGA_WEST": {"warning_count": 54, "missing_matches": ["FC Koln B vs Bocholt", "Bocholt vs MSV Duisburg", "Bocholt vs FC Koln B", "MSV Duisburg vs Bocholt", "Duren vs FC Koln B", "MSV Duisburg vs Duren", "FC Koln B vs Duren", "<PERSON><PERSON> vs MSV Duisburg", "Dusseldorf B vs FC Koln B", "MSV Duisburg vs Dusseldorf B", "FC Koln B vs Dusseldorf B", "Dusseldorf B vs MSV Duisburg", "<PERSON><PERSON> vs FC Koln B", "MSV Duisburg vs <PERSON><PERSON>", "FC Koln B vs <PERSON><PERSON>", "<PERSON><PERSON> vs MSV Duisburg", "FC Koln B vs Fortuna Koln", "Fortuna Koln vs MSV Duisburg", "Fortuna Koln vs FC Koln B", "MSV Duisburg vs Fortuna Koln", "Gutersloh vs MSV Duisburg", "FC Koln B vs Gutersloh", "MSV Duisburg vs Gutersloh", "Gutersloh vs FC Koln B", "Lotte vs FC Koln B", "MSV Duisburg vs Lotte", "FC Koln B vs Lotte", "MSV Duisburg vs Mgladbach B", "FC Koln B vs Mgladbach B", "Mgladbach B vs FC Koln B", "Mgladbach B vs MSV Duisburg", "Oberhausen vs MSV Duisburg", "FC Koln B vs Oberhausen", "MSV Duisburg vs Oberhausen", "FC Koln B vs Paderborn B", "Paderborn B vs MSV Duisburg", "Paderborn B vs FC Koln B", "MSV Duisburg vs Paderborn B", "Rodinghausen vs FC Koln B", "MSV Duisburg vs Rodinghausen", "FC Koln B vs Rodinghausen", "Rodinghausen vs MSV Duisburg", "FC Koln B vs Schalke 04 B", "Schalke 04 B vs MSV Duisburg", "Schalke 04 B vs FC Koln B", "MSV Duisburg vs Schalke 04 B", "Wiedenbruck vs FC Koln B", "MSV Duisburg vs Wiedenbruck", "FC Koln B vs Wiedenbruck", "Wiedenbruck vs MSV Duisburg", "Wuppertaler vs FC Koln B", "<PERSON><PERSON><PERSON><PERSON> vs MSV Duisburg", "FC Koln B vs Wuppertaler", "MSV Duisburg vs Wuppertaler"], "teams_affected": ["<PERSON><PERSON>", "Mgladbach B", "<PERSON><PERSON><PERSON>", "Gutersloh", "Fortuna Koln", "<PERSON><PERSON>", "Rodinghausen", "<PERSON><PERSON><PERSON><PERSON>", "Oberhausen", "FC Koln B", "Schalke 04 B", "Dusseldorf B", "Lotte", "Paderborn B", "Wiedenbruck", "MSV Duisburg"], "total_matches": 408, "valid_matches": 354, "skipped_matches": 54}, "ISRAEL_LIGA_ALEF": {"warning_count": 58, "missing_matches": ["Kiryat Yam SC vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON><PERSON>k", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON>", "<PERSON><PERSON> vs <PERSON> Tira", "<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> vs MS Tira", "Kiryat Yam SC vs H. Bnei Mu<PERSON>", "<PERSON><PERSON><PERSON> vs H. Bnei <PERSON>", "<PERSON><PERSON> vs H. Bne<PERSON>", "H. Bnei Zalfa vs MS Tira", "Kiryat Yam SC vs H. Bnei Zalfa", "H. Bnei Zalfa vs Migdal HaEmek", "<PERSON>i Araba vs H. Bnei Zalfa", "MS Tira vs H. Bnei Zalfa", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON> Tira vs <PERSON><PERSON> <PERSON><PERSON><PERSON>", "Kiryat Yam SC vs <PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON><PERSON> HaEmek", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON><PERSON>", "Hapoel Bueine vs Kiryat Yam SC", "Migdal HaEmek vs Hapoel Bueine", "Hapoel Bueine vs Ironi Araba", "MS Tira vs Hapoel Bueine", "MS Tira vs Ironi Baka", "<PERSON><PERSON><PERSON> vs Ironi Baka", "Ironi Baka vs Kiryat Yam SC", "<PERSON><PERSON> Baka vs <PERSON><PERSON> Araba", "<PERSON><PERSON> vs MS Tira", "Kiryat Yam SC vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON><PERSON> Ha<PERSON>k", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON> Tira", "<PERSON><PERSON> vs Kiryat Yam SC", "<PERSON><PERSON> vs Kiryat Yam SC", "<PERSON><PERSON><PERSON> vs K. Ata Bialik", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON> Tira vs K. Ata Bialik", "<PERSON><PERSON> <PERSON><PERSON> vs Kiryat Yam SC", "<PERSON><PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON> Tira vs <PERSON><PERSON> <PERSON><PERSON>", "Kiryat Yam SC vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "<PERSON><PERSON> vs Kiryat Yam SC", "<PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs MS Tira", "Kiryat Yam SC vs <PERSON><PERSON>", "<PERSON><PERSON> vs Migdal HaEmek", "Kiryat Yam SC vs <PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON><PERSON> HaEmek", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON> Tira", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> El Fahm vs MS Tira", "<PERSON><PERSON> <PERSON><PERSON> El Fahm vs Kiryat Yam SC", "<PERSON><PERSON><PERSON> vs T<PERSON> <PERSON><PERSON> El Fahm", "<PERSON><PERSON> <PERSON><PERSON> Fahm vs <PERSON><PERSON> Araba"], "teams_affected": ["Hapoel Bueine", "<PERSON><PERSON>", "H. Bnei Zalfa", "MS Tira", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>ik", "<PERSON><PERSON>", "<PERSON><PERSON>", "Kiryat Yam SC", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> El Fahm", "<PERSON><PERSON> <PERSON><PERSON>"], "total_matches": 232, "valid_matches": 174, "skipped_matches": 58}, "GERMANY_REGIONALLIGA_NORD": {"warning_count": 108, "missing_matches": ["Drochtersen.A. vs VfB Lubeck", "Drochtersen.A. vs Bremer SV", "Drochtersen.A. vs BW <PERSON>e", "Hamburger SV B vs Drochtersen.A.", "VfB Lubeck vs Drochtersen.A.", "Bremer SV vs Drochtersen.A.", "BW Lohne vs Drochtersen.A.", "Hamburger SV B vs Havelse", "Bremer SV vs Havelse", "VfB Lubeck vs Havelse", "<PERSON><PERSON><PERSON> vs BW Lohne", "Havelse vs Hamburger SV B", "Havelse vs Bremer SV", "<PERSON><PERSON><PERSON> vs VfB Lubeck", "BW Lohne vs Havelse", "Holstein Kiel B vs Bremer SV", "VfB Lubeck vs Holstein Kiel B", "Holstein Kiel B vs BW Lohne", "Hamburger SV B vs Holstein Kiel B", "Bremer SV vs Holstein Kiel B", "Holstein Kiel B vs VfB Lubeck", "BW Lohne vs Holstein Kiel B", "Holstein Kiel B vs Hamburger SV B", "Bremer SV vs Jeddeloh", "<PERSON>deloh vs VfB Lubeck", "Jeddeloh vs Hamburger SV B", "<PERSON><PERSON><PERSON> vs B<PERSON>e", "Hamburger SV B vs Jeddeloh", "Jeddeloh vs Bremer SV", "VfB Lubeck vs Jeddeloh", "<PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "Kickers <PERSON> vs B<PERSON>", "Hamburger SV B vs Kickers Emden", "Kickers Emden vs Bremer SV", "VfB Lubeck vs Kickers Emden", "B<PERSON>e vs Kickers Emden", "Kickers Emden vs Hamburger SV B", "Bremer SV vs Kickers Emden", "Kickers Emden vs VfB Lubeck", "Meppen vs Bremer SV", "<PERSON><PERSON> vs Meppen", "<PERSON><PERSON>n vs VfB Lubeck", "<PERSON><PERSON><PERSON> vs BW <PERSON>hne", "Hamburger SV B vs Meppen", "VfB Lubeck vs Meppen", "Bremer SV vs Meppen", "Meppen vs Hamburger SV B", "Norderstedt vs Bremer SV", "Norderstedt vs VfB Lubeck", "Norderstedt vs Hamburger SV B", "BW Lohne vs Norderstedt", "Hamburger SV B vs Norderstedt", "Bremer SV vs Norderstedt", "VfB Lubeck vs Norderstedt", "BW Lohne vs Oldenburg", "Hamburger SV B vs Oldenburg", "Bremer SV vs Oldenburg", "VfB Lubeck vs Oldenburg", "Oldenburg vs Hamburger SV B", "Oldenburg vs BW Lohne", "Oldenburg vs Bremer SV", "Oldenburg vs VfB Lubeck", "B<PERSON> vs Phonix <PERSON>", "Phonix Lubeck vs Hamburger SV B", "Phonix <PERSON> vs Bremer SV", "Phonix <PERSON> vs VfB Lubeck", "<PERSON><PERSON><PERSON> vs B<PERSON>", "Hamburger SV B vs Phonix Lubeck", "Bremer SV vs Phonix Lubeck", "VfB Lubeck vs Phonix Lubeck", "St. Pauli B vs BW Lohne", "St. Pauli B vs Hamburger SV B", "St. Pauli B vs VfB Lubeck", "St. Pauli B vs Bremer SV", "VfB Lubeck vs St. Pauli B", "B<PERSON> vs St. Pauli B", "Hamburger SV B vs St. Pauli B", "Teutonia O. vs BW <PERSON>e", "Hamburger SV B vs Teutonia O.", "Bremer SV vs Teutonia O.", "VfB Lubeck vs Teutonia O.", "BW Lohne vs Teutonia O.", "Teutonia O. vs Hamburger SV B", "Teutonia O. vs Bremer SV", "Teutonia O. vs VfB Lubeck", "VfB Lubeck vs Todesfelde", "BW Lohne vs Todesfelde", "Hamburger SV B vs Todesfelde", "Bremer SV vs Todesfelde", "Todesfelde vs VfB Lubeck", "Todesfelde vs BW Lohne", "Todesfelde vs Hamburger SV B", "Todesfelde vs Bremer SV", "Bremer SV vs Weiche F.", "VfB Lubeck vs Weiche F.", "BW Lohne vs Weiche F.", "Weiche F. vs Hamburger SV B", "Weiche F. vs BW <PERSON>hne", "Weiche F. vs VfB Lubeck", "Weiche F. vs Bremer SV", "Hamburger SV B vs Weiche F.", "Werder Bremen B vs BW Lohne", "Werder Bremen B vs Hamburger SV B", "Werder Bremen B vs Bremer SV", "Werder Bremen B vs VfB Lubeck", "Hamburger SV B vs Werder Bremen B", "BW Lohne vs Werder Bremen B", "Bremer SV vs Werder Bremen B"], "teams_affected": ["<PERSON><PERSON><PERSON>", "Hamburger SV B", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Kickers <PERSON>", "Oldenburg", "St. Pauli B", "VfB Lubeck", "Bremer SV", "BW Lohne", "Todesfelde", "Werder Bremen B", "Meppen", "Norderstedt", "Weiche F.", "Teutonia O.", "Drochtersen.A.", "Holstein Kiel B"], "total_matches": 456, "valid_matches": 348, "skipped_matches": 108}, "FINLAND_YKKONEN": {"warning_count": 6, "missing_matches": ["Atlantis vs PKKU", "EPS vs Jazz Pori", "KuPS Ak. vs Jazz Pori", "<PERSON><PERSON><PERSON><PERSON> vs Jazz Pori", "Jazz Pori vs OLS Oulu", "<PERSON><PERSON><PERSON><PERSON> vs Rovaniemi"], "teams_affected": ["KuPS Ak.", "<PERSON><PERSON><PERSON><PERSON>", "EPS", "OLS Oulu", "Rovaniemi", "Jazz Pori", "PKKU", "Atlantis"], "total_matches": 7, "valid_matches": 1, "skipped_matches": 6}, "GERMANY_OBERLIGA_NIEDERRHEIN": {"warning_count": 101, "missing_matches": ["SC St. Tonis vs Baumberg", "<PERSON><PERSON><PERSON> vs SW Essen", "Baumberg vs TVD Velbert", "SSVg Velbert vs Baumberg", "<PERSON><PERSON><PERSON> vs SC St. Tonis", "SW Essen vs Baumberg", "TVD Velbert vs Baumberg", "SW Essen vs Biemenhorst", "TVD Velbert vs Biemenhorst", "<PERSON><PERSON><PERSON>horst vs SSVg Velbert", "<PERSON><PERSON>menhorst vs SC St. Tonis", "<PERSON><PERSON><PERSON><PERSON>st vs SW Essen", "Biemenhorst vs TVD Velbert", "SSVg Velbert vs Biemenhorst", "<PERSON><PERSON>h vs SC St. Tonis", "SW Essen vs Bud<PERSON><PERSON>", "TVD Velbert vs Buderich", "<PERSON><PERSON><PERSON> vs SSVg Velbert", "SC St. Tonis vs Buderich", "<PERSON><PERSON><PERSON> vs SW Essen", "Buderich vs TVD Velbert", "SSVg Velbert vs <PERSON><PERSON><PERSON>", "SSVg <PERSON> vs G. <PERSON>", "<PERSON><PERSON> vs SC St. Tonis", "<PERSON><PERSON> Essen vs G<PERSON>", "TVD Velbert vs G. Ratingen", "<PERSON><PERSON> vs SSVg Velbert", "SC St. Tonis vs G. Ratingen", "<PERSON><PERSON> vs SW Essen", "<PERSON><PERSON> vs TVD Velbert", "SSVg Velbert vs Hilden", "<PERSON><PERSON><PERSON> vs SC St. Tonis", "SW Essen vs Hilden", "TVD Velbert vs Hilden", "<PERSON><PERSON><PERSON> vs SSVg Velbert", "SC St. Tonis vs Hilden", "<PERSON><PERSON><PERSON> vs SW Essen", "Hilden vs TVD Velbert", "Homberg vs TVD Velbert", "Homberg vs SSVg Velbert", "Homberg vs SC St. Tonis", "SW Essen vs Homberg", "TVD Velbert vs Homberg", "SSVg Velbert vs Homberg", "SSVg Velbert vs Kleve", "SC St. Tonis vs Kleve", "SW Essen vs Kleve", "TVD Velbert vs Kleve", "<PERSON><PERSON><PERSON> vs SSVg Velbert", "<PERSON><PERSON>e vs SC St. Tonis", "<PERSON><PERSON><PERSON> vs SW Essen", "<PERSON><PERSON><PERSON><PERSON> vs SSVg Velbert", "SC St. Tonis vs Meerbusch", "<PERSON><PERSON><PERSON><PERSON> vs SW Essen", "Me<PERSON>busch vs TVD Velbert", "SSVg Velbert vs Meerbusch", "<PERSON><PERSON><PERSON><PERSON> vs SC St. Tonis", "SW Essen vs Meerbusch", "TVD Velbert vs Meerbusch", "Monheim vs SW Essen", "Monheim vs SSVg Velbert", "Monheim vs SC St. Tonis", "Monheim vs TVD Velbert", "SSVg Velbert vs Monheim", "SC St. Tonis vs Monheim", "<PERSON><PERSON><PERSON> vs SW Essen", "<PERSON><PERSON><PERSON> vs TVD Velbert", "SSVg <PERSON>bert vs <PERSON><PERSON>heimer", "SC St. Tonis vs Mulheimer", "SW Essen vs Mu<PERSON>heimer", "TVD Velbert vs Mulheimer", "<PERSON><PERSON><PERSON> vs SSVg Velbert", "SC St. Tonis vs Niederwenigern", "<PERSON><PERSON><PERSON><PERSON><PERSON>n vs SW Essen", "Niederwenigern vs TVD Velbert", "SSVg Velbert vs Niederwenigern", "Niederwenigern vs SC St. Tonis", "SW Essen vs Niederwenigern", "TVD Velbert vs Niederwenigern", "SSVg Velbert vs Schonnebeck", "<PERSON><PERSON><PERSON><PERSON> vs TVD Velbert", "SC St. Tonis vs Schonnebeck", "<PERSON><PERSON><PERSON><PERSON> vs SW Essen", "<PERSON><PERSON><PERSON><PERSON> vs SSVg Velbert", "TVD Velbert vs Schonnebeck", "<PERSON><PERSON><PERSON><PERSON> vs SC St. Tonis", "Sonsbeck vs SSVg Velbert", "Sonsbeck vs SC St. Tonis", "SW Essen vs Sonsbeck", "TVD Velbert vs Sonsbeck", "SSVg Velbert vs Sonsbeck", "SC St. Tonis vs Sonsbeck", "Sonsbeck vs SW Essen", "Sonsbeck vs TVD Velbert", "Union Nettetal vs SC St. Tonis", "SW Essen vs Union Nettetal", "TVD Velbert vs Union Nettetal", "Union Nettetal vs SSVg Velbert", "SC St. Tonis vs Union Nettetal", "Union Nettetal vs SW Essen", "Union Nettetal vs TVD Velbert"], "teams_affected": ["TVD Velbert", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "SC St. Tonis", "<PERSON><PERSON><PERSON>", "SW Essen", "Niederwenigern", "Sonsbeck", "Union Nettetal", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Monheim", "<PERSON><PERSON><PERSON><PERSON>", "SSVg <PERSON>", "Homberg", "<PERSON><PERSON>menhorst"], "total_matches": 427, "valid_matches": 326, "skipped_matches": 101}, "NICARAGUA_PRIMERA_DIVISION": {"warning_count": 32, "missing_matches": ["Managua FC vs D. Ocotal", "UNAN Managua vs D. <PERSON>", "D. Ocotal vs Managua FC", "<PERSON><PERSON> vs UNAN Managua", "Diriangen vs UNAN Managua", "Managua FC vs Diriangen", "UNAN Managua vs Diriangen", "Diriangen vs Managua FC", "Managua FC vs Export Sebaco", "Export Sebaco vs UNAN Managua", "Export Sebaco vs Managua FC", "UNAN Managua vs Export Sebaco", "UNAN Managua vs Jalapa", "Managua FC vs Jalapa", "<PERSON>alapa vs UNAN Managua", "Jalapa vs Managua FC", "Matagalpa vs Managua FC", "UNAN Managua vs Matagalpa", "Managua FC vs Matagalpa", "Matagalpa vs UNAN Managua", "Rancho Santana vs Managua FC", "UNAN Managua vs Rancho Santana", "Managua FC vs Rancho Santana", "Rancho Santana vs UNAN Managua", "Real Esteli vs Managua FC", "UNAN Managua vs Real Esteli", "Managua FC vs Real Esteli", "Real Esteli vs UNAN Managua", "<PERSON> vs UNAN Managu<PERSON>", "<PERSON> vs Managua FC", "UNAN Managua vs <PERSON>", "Managua FC vs Walter Ferreti"], "teams_affected": ["Export Sebaco", "Diriangen", "Rancho Santana", "Matagalpa", "UNAN Managua", "Managua FC", "<PERSON><PERSON>", "Real Esteli", "Jalapa", "<PERSON>"], "total_matches": 144, "valid_matches": 112, "skipped_matches": 32}, "AUSTRALIA_VICTORIA_NPL": {"warning_count": 1, "missing_matches": ["Melbourne K. vs M. Victory B"], "teams_affected": ["<PERSON>. <PERSON> B", "Melbourne K."], "total_matches": 21, "valid_matches": 20, "skipped_matches": 1}, "ITALY_SERIE_D_GROUP_A": {"warning_count": 68, "missing_matches": ["<PERSON><PERSON> vs Citta di Varese", "<PERSON><PERSON> vs NovaRomentin", "Citta di Varese vs Asti", "NovaRomentin vs Asti", "<PERSON><PERSON><PERSON> vs Citta di Varese", "<PERSON><PERSON><PERSON> vs NovaRomentin", "Citta di Varese vs Borgaro <PERSON>", "NovaRomentin vs <PERSON>rga<PERSON>", "NovaRomentin vs Bra", "Citta di Varese vs Bra", "Bra vs NovaRomentin", "Bra vs Citta di Varese", "NovaRomentin vs Cairese", "Cairese vs Citta di Varese", "Cairese vs NovaRomentin", "Citta di Varese vs Cairese", "<PERSON>eri vs NovaRomentin", "Citta di Varese vs Chieri", "NovaRomentin vs Chieri", "Chieri vs Citta di Varese", "NovaRomentin vs Chisola", "Chisola vs Citta di Varese", "Chisola vs NovaRomentin", "Citta di Varese vs Chisola", "NovaRomentin vs Derthona", "Citta di Varese vs Derthona", "Derthona vs NovaRomentin", "Derthona vs Citta di Varese", "Citta di Varese vs Fossano", "Fossano vs NovaRomentin", "Fossano vs Citta di Varese", "NovaRomentin vs Fossano", "Gozzano vs NovaRomentin", "Citta di Varese vs Gozzano", "NovaRomentin vs Gozzano", "Gozzano vs Citta di Varese", "Lavagnese vs NovaRomentin", "Lavagnese vs Citta di Varese", "NovaRomentin vs Lavagnese", "Citta di Varese vs Lavagnese", "Ligorna vs Citta di Varese", "NovaRomentin vs Ligorna", "Citta di Varese vs Ligorna", "Ligorna vs NovaRomentin", "NovaRomentin vs Oltrepo", "Oltrepo vs Citta di Varese", "Oltrepo vs NovaRomentin", "Citta di Varese vs Oltrepo", "Citta di Varese vs Pro Imperia", "Pro Imperia vs NovaRomentin", "Pro Imperia vs Citta di Varese", "NovaRomentin vs Pro Imperia", "Citta di Varese vs Saluzzo", "NovaRomentin vs Saluzzo", "Saluzzo vs Citta di Varese", "Saluzzo vs NovaRomentin", "Citta di Varese vs Sanremese", "NovaRomentin vs Sanremese", "Sanremese vs Citta di Varese", "Sanremese vs NovaRomentin", "Vado vs NovaRomentin", "Vado vs Citta di Varese", "NovaRomentin vs Vado", "Citta di Varese vs Vado", "<PERSON><PERSON><PERSON><PERSON> vs Citta di Varese", "NovaRomentin vs Vogherese", "Citta di Varese vs Vogherese", "Vogherese vs NovaRomentin"], "teams_affected": ["<PERSON><PERSON><PERSON><PERSON>", "Saluzzo", "Vado", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Gozzano", "Fossano", "Ligorna", "<PERSON><PERSON>", "NovaRomentin", "Cairese", "Pro Imperia", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Derthona", "Bra", "Lavagnese", "Chisola", "Citta di Varese"], "total_matches": 612, "valid_matches": 544, "skipped_matches": 68}, "IRELAND_WOMENS_NATIONAL_LEAGUE": {"warning_count": 2, "missing_matches": ["Cork City vs Bohemians", "Bohemians vs Cork City"], "teams_affected": ["Bohemians", "Cork City"], "total_matches": 16, "valid_matches": 14, "skipped_matches": 2}, "GERMANY_BUNDESLIGA": {"warning_count": 87, "missing_matches": ["Bayern Munich vs FC Augsburg", "FSV Mainz vs Bayern Munich", "Bayern Munich vs RB Leipzig", "FC Augsburg vs Bayern Munich", "Bayern Munich vs FSV Mainz", "RB Leipzig vs Bayern Munich", "RB Leipzig vs Bochum", "FC Augsburg vs Bochum", "FSV Mainz vs Bochum", "Bochum vs RB Leipzig", "Bochum vs FC Augsburg", "Bochum vs FSV Mainz", "FC Augsburg vs Dortmund", "Dortmund vs RB Leipzig", "FSV Mainz vs Dortmund", "Dortmund vs FC Augsburg", "RB Leipzig vs Dortmund", "Dortmund vs FSV Mainz", "E. Frankfurt vs FC Augsburg", "RB Leipzig vs E. Frankfurt", "E. Frankfurt vs FSV Mainz", "FC Augsburg vs E. Frankfurt", "E. Frankfurt vs RB Leipzig", "FSV Mainz vs E. Frankfurt", "Freiburg vs FC Augsburg", "RB Leipzig vs Freiburg", "Freiburg vs FSV Mainz", "FC Augsburg vs Freiburg", "Freiburg vs RB Leipzig", "FSV Mainz vs Freiburg", "Heidenheim vs FC Augsburg", "FSV Mainz vs Heidenheim", "Heidenheim vs RB Leipzig", "FC Augsburg vs Heidenheim", "Heidenheim vs FSV Mainz", "RB Leipzig vs Heidenheim", "FC Augsburg vs Hoffenheim", "Hoffenheim vs RB Leipzig", "FSV Mainz vs Hoffenheim", "Hoffenheim vs FC Augsburg", "RB Leipzig vs Hoffenheim", "Hoffenheim vs FSV Mainz", "Holstein Kiel vs FSV Mainz", "Holstein Kiel vs RB Leipzig", "Holstein Kiel vs FC Augsburg", "FSV Mainz vs Holstein Kiel", "RB Leipzig vs Holstein Kiel", "FC Augsburg vs Holstein Kiel", "Leverkusen vs RB Leipzig", "FC Augsburg vs Leverkusen", "Leverkusen vs FSV Mainz", "RB Leipzig vs Leverkusen", "Leverkusen vs FC Augsburg", "FC Augsburg vs Monchengladbach", "FSV Mainz vs Monchengladbach", "RB Leipzig vs Monchengladbach", "Monchengladbach vs FC Augsburg", "Monchengladbach vs FSV Mainz", "Monchengladbach vs RB Leipzig", "FC Augsburg vs Sankt Pauli", "Sankt Pauli vs RB Leipzig", "Sankt Pauli vs FSV Mainz", "Sankt Pauli vs FC Augsburg", "RB Leipzig vs Sankt Pauli", "FSV Mainz vs Sankt Pauli", "Stuttgart vs FSV Mainz", "FC Augsburg vs Stuttgart", "Stuttgart vs RB Leipzig", "FSV Mainz vs Stuttgart", "Stuttgart vs FC Augsburg", "FSV Mainz vs Union Berlin", "RB Leipzig vs Union Berlin", "Union Berlin vs FC Augsburg", "Union Berlin vs FSV Mainz", "Union Berlin vs RB Leipzig", "FC Augsburg vs Werder Bremen", "FSV Mainz vs Werder Bremen", "RB Leipzig vs Werder Bremen", "Werder Bremen vs FC Augsburg", "Werder Bremen vs FSV Mainz", "Werder Bremen vs RB Leipzig", "Wolfsburg vs FC Augsburg", "RB Leipzig vs Wolfsburg", "Wolfsburg vs FSV Mainz", "FC Augsburg vs Wolfsburg", "Wolfsburg vs RB Leipzig", "FSV Mainz vs Wolfsburg"], "teams_affected": ["Bayern Munich", "Leverkusen", "Bochum", "Holstein Kiel", "FC Augsburg", "Stuttgart", "FSV Mainz", "Dortmund", "Sankt Pauli", "E. Frankfurt", "Wolfsburg", "Hoffenheim", "Union Berlin", "RB Leipzig", "Heidenheim", "Monchengladbach", "Freiburg", "Werder Bremen"], "total_matches": 495, "valid_matches": 408, "skipped_matches": 87}, "ICELAND_3_DEILD": {"warning_count": 40, "missing_matches": ["KFK Kopavogur vs Arbaer", "Arbaer vs KV Reykjavik", "Arbaer vs KFK Kopavogur", "KV Reykjavik vs Arbaer", "Augnablik vs KFK Kopavogur", "KV Reykjavik vs Augnablik", "KFK Kopavogur vs Augnablik", "Augnablik vs KV Reykjavik", "KFK Kopavogur vs Ellidi", "Ellidi vs KV Reykjavik", "Ellidi vs KFK Kopavogur", "KV Reykjavik vs Ellidi", "Hafnafjordur vs KV Reykjavik", "KFK Kopavogur vs Hafnafjordur", "KV Reykjavik vs Hafnafjordur", "Hafnafjordur vs KFK Kopavogur", "KFK Kopavogur vs Hviti <PERSON>", "<PERSON><PERSON><PERSON> vs KV Reykjavik", "<PERSON><PERSON><PERSON> vs KFK Kopavogur", "KV Reykjavik vs Hviti Riddarinn", "<PERSON><PERSON> vs KFK Kopavogur", "KV Reykjavik vs Kari", "KFK Kopavogur vs <PERSON><PERSON>", "<PERSON><PERSON> vs KV Reykjavik", "KFK Kopavogur vs Magni", "Ma<PERSON> vs KV Reykjavik", "<PERSON><PERSON> vs KFK Kopavogur", "KV Reykjavik vs Magni", "Sindri vs KFK Kopavogur", "KV Reykjavik vs Sindri", "Sindri vs KV Reykjavik", "KFK Kopavogur vs Sindri", "Vaengir Jupiter vs KFK Kopavogur", "KV Reykjavik vs Vaengir Jupiter", "KFK Kopavogur vs Vaengir Jupiter", "Vaengir Jupiter vs KV Reykjavik", "KFK Kopavogur vs Vidir", "Vidir vs KV Reykjavik", "<PERSON><PERSON><PERSON> vs KFK Kopavogur", "KV Reykjavik vs Vidir"], "teams_affected": ["KFK Kopavogur", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Augnablik", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "KV Reykjavik", "Hafnafjordur", "Vaengir Jupiter", "<PERSON><PERSON>"], "total_matches": 220, "valid_matches": 180, "skipped_matches": 40}, "PERU_PRIMERA_DIVISION": {"warning_count": 7, "missing_matches": ["Juan Pablo II C vs Alianza A.", "<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON> vs <PERSON><PERSON>", "<PERSON> vs FBC Melgar", "Universitario vs <PERSON><PERSON>", "Universitario vs FBC Melgar", "Binacional vs Universitario"], "teams_affected": ["<PERSON>", "<PERSON><PERSON>", "Universitario", "Binacional", "Alianza A.", "FBC Melgar", "Juan Pablo II C", "Cienciano"], "total_matches": 21, "valid_matches": 14, "skipped_matches": 7}, "COLOMBIA_PRIMERA_A": {"warning_count": 51, "missing_matches": ["<PERSON><PERSON> vs Jaguares de C.", "<PERSON><PERSON> vs America de Cali", "<PERSON><PERSON> vs Fortaleza CEIF", "A. Nacional vs America de Cali", "Jaguares de C. vs A. Nacional", "Fortaleza CEIF vs A. Nacional", "A<PERSON> Petrolera vs Fortaleza CEIF", "<PERSON><PERSON> vs America de Cali", "Jaguares de C. vs A. <PERSON>era", "Boyaca Chico vs Fortaleza CEIF", "Boyaca Chico vs America de Cali", "Jaguares de C. vs Boyaca Chico", "Fortaleza CEIF vs D. Pereira", "America de Cali vs <PERSON><PERSON>", "Jaguares de C. vs <PERSON><PERSON> Pereira", "Fortaleza CEIF vs Deportes Tolima", "Jaguares de C. vs Deportes Tolima", "America de Cali vs Deportes Tolima", "Fortaleza CEIF vs Deportivo Cali", "America de Cali vs Deportivo Cali", "Deportivo Cali vs Jaguares de C.", "Jaguares de C. vs Deportivo Pasto", "Fortaleza CEIF vs Deportivo Pasto", "Deportivo Pasto vs America de Cali", "America de Cali vs Envigado", "Envigado vs Fortaleza CEIF", "Jaguares de C. vs Envigado", "Fortaleza CEIF vs <PERSON><PERSON>", "<PERSON> de Cali vs <PERSON><PERSON>", "<PERSON><PERSON> vs Jaguares de C.", "Junior vs Fortaleza CEIF", "Jaguares de C. vs Junior", "Junior vs America de Cali", "La Equidad vs Jaguares de C.", "La Equidad vs America de Cali", "Fortaleza CEIF vs La Equidad", "Millonarios vs Jaguares de C.", "America de Cali vs Millonarios", "Millonarios vs Fortaleza CEIF", "Once Caldas vs Jaguares de C.", "Once Caldas vs Fortaleza CEIF", "America de Cali vs Once Caldas", "Jaguares de C. vs Patriotas", "Patriotas vs Fortaleza CEIF", "Patriotas vs America de Cali", "<PERSON><PERSON> vs America de Cali", "<PERSON><PERSON> vs Jaguares de C.", "Fortaleza CEIF vs R. <PERSON>", "Fortaleza CEIF vs Santa Fe", "America de Cali vs Santa Fe", "Santa Fe vs Jaguares de C."], "teams_affected": ["<PERSON><PERSON><PERSON>", "Boyaca Chico", "<PERSON><PERSON>", "America de Cali", "Patriotas", "Deportes Tolima", "Deportivo Pasto", "La Equidad", "Jaguares de C.", "Deportivo Cali", "<PERSON><PERSON>", "Junior", "<PERSON><PERSON>", "Fortaleza CEIF", "<PERSON><PERSON>", "A. Nacional", "Millonarios", "<PERSON><PERSON>", "Once Caldas", "Santa Fe"], "total_matches": 323, "valid_matches": 272, "skipped_matches": 51}, "DENMARK_SUPERLIGA": {"warning_count": 80, "missing_matches": ["Lyngby vs FC Kobenhavn", "SonderjyskE vs Lyngby", "Lyngby vs Brondby IF", "Lyngby vs FC Midtjylland", "Lyngby vs Vejle BK", "Aalborg BK vs Lyngby", "Lyngby vs AGF Aarhus", "Randers FC vs Lyngby", "Vejle BK vs Lyngby", "AGF Aarhus vs Lyngby", "Lyngby vs Aalborg BK", "FC Kobenhavn vs Lyngby", "Lyngby vs SonderjyskE", "FC Midtjylland vs Lyngby", "Lyngby vs Randers FC", "Brondby IF vs Lyngby", "Lyngby vs Vejle BK", "Aalborg BK vs Lyngby", "Lyngby vs SonderjyskE", "Nordsjaelland vs Aalborg BK", "Nordsjaelland vs FC Midtjylland", "Vejle BK vs Nordsjaelland", "Nordsjaelland vs FC Kobenhavn", "AGF Aarhus vs Nordsjaelland", "Nordsjaelland vs Randers FC", "Nordsjaelland vs Brondby IF", "SonderjyskE vs Nordsjaelland", "Randers FC vs Nordsjaelland", "Brondby IF vs Nordsjaelland", "Nordsjaelland vs AGF Aarhus", "FC Kobenhavn vs Nordsjaelland", "Nordsjaelland vs SonderjyskE", "Aalborg BK vs Nordsjaelland", "FC Midtjylland vs Nordsjaelland", "Nordsjaelland vs Vejle BK", "AGF Aarhus vs Nordsjaelland", "Nordsjaelland vs FC Kobenhavn", "Nordsjaelland vs Randers FC", "Brondby IF vs Nordsjaelland", "Nordsjaelland vs FC Midtjylland", "FC Midtjylland vs Nordsjaelland", "Silkeborg vs SonderjyskE", "Aalborg BK vs Silkeborg", "Randers FC vs Silkeborg", "Vejle BK vs Silkeborg", "Silkeborg vs FC Midtjylland", "AGF Aarhus vs Silkeborg", "Silkeborg vs Brondby IF", "Silkeborg vs FC Kobenhavn", "Silkeborg vs Aalborg BK", "FC Kobenhavn vs Silkeborg", "Silkeborg vs Randers FC", "FC Midtjylland vs Silkeborg", "Silkeborg vs AGF Aarhus", "Silkeborg vs Vejle BK", "SonderjyskE vs Silkeborg", "Brondby IF vs Silkeborg", "SonderjyskE vs Silkeborg", "Silkeborg vs Aalborg BK", "Vejle BK vs Silkeborg", "Viborg vs Brondby IF", "Randers FC vs Viborg", "Viborg vs Aalborg BK", "FC Kobenhavn vs Viborg", "SonderjyskE vs Viborg", "Vejle BK vs Viborg", "FC Midtjylland vs Viborg", "Viborg vs AGF Aarhus", "Viborg vs SonderjyskE", "Viborg vs FC Midtjylland", "Aalborg BK vs Viborg", "Viborg vs Randers FC", "Brondby IF vs Viborg", "Viborg vs Vejle BK", "AGF Aarhus vs Viborg", "Viborg vs FC Kobenhavn", "Aalborg BK vs Viborg", "Viborg vs SonderjyskE", "Vejle BK vs Viborg", "SonderjyskE vs Viborg"], "teams_affected": ["Lyn<PERSON>by", "Vejle BK", "Brondby IF", "Vib<PERSON>g", "FC Kobenhavn", "Nordsjaelland", "Aalborg BK", "FC Midtjylland", "Randers FC", "Silkeborg", "SonderjyskE", "AGF Aarhus"], "total_matches": 112, "valid_matches": 32, "skipped_matches": 80}, "AUSTRALIA_QUEENSLAND_NPL": {"warning_count": 2, "missing_matches": ["SC Wanderers vs Gold C. Knights", "Gold C. Knights vs St George W."], "teams_affected": ["SC Wanderers", "St George W.", "Gold C. Knights"], "total_matches": 10, "valid_matches": 8, "skipped_matches": 2}, "COSTA_RICA_LIGA_DE_ASCENSO": {"warning_count": 10, "missing_matches": ["Alajuelense vs Santa Ana", "Sporting SJ vs Alajuelense", "Santos Guapiles vs Alajuelense", "Alajuelense vs Santos Guapiles", "Alajuelense vs Sporting SJ", "Santa Ana vs Alajuelense", "Santos Guapiles vs Guanacasteca", "Guanacasteca vs Santos Guapiles", "Zeledon vs Sporting SJ", "Sporting SJ vs Zeledon"], "teams_affected": ["<PERSON><PERSON><PERSON>", "Guanacasteca", "Sporting SJ", "Alajuelense", "Santos Guapiles", "Santa Ana"], "total_matches": 40, "valid_matches": 30, "skipped_matches": 10}, "KUWAIT_PREMIER_LEAGUE": {"warning_count": 20, "missing_matches": ["Al Fahaheel vs Al Arabi SC", "Al Fahaheel vs Al Arabi SC", "Al Arabi SC vs Al Fahaheel", "Al Kuwait vs Al Arabi SC", "Al Arabi SC vs Al Kuwait", "Al Nasar vs Al Arabi SC", "Al Arabi SC vs Al Nasar", "Al Arabi SC vs Al Qadsia", "Al Qadsia vs Al Arabi SC", "Al Salmiyah vs Al Arabi SC", "Al Arabi SC vs Al Salmiyah", "Al Salmiyah vs Al Arabi SC", "Al Tadhamon vs Al Arabi SC", "Al Arabi SC vs Al Tadhamon", "Al Arabi SC vs Kazma", "<PERSON><PERSON><PERSON> vs Al Arabi SC", "Al Arabi SC vs Khaitan", "K<PERSON><PERSON> vs Al Arabi SC", "Al Arabi SC vs Yarmouk", "Yarmouk vs Al Arabi SC"], "teams_affected": ["Al Salmiyah", "Al Tadhamon", "<PERSON><PERSON><PERSON>", "Khaitan", "Al Kuwait", "Al Qadsia", "Yarmouk", "Al Arabi SC", "Al Nasar", "Al <PERSON>"], "total_matches": 176, "valid_matches": 156, "skipped_matches": 20}, "ITALY_SERIE_D_GROUP_E": {"warning_count": 32, "missing_matches": ["FC Siena vs Fezzanese", "Fezzanese vs FC Siena", "Figline vs FC Siena", "FC Siena vs Figline", "FC Siena vs Flaminia", "Flaminia vs FC Siena", "FC Siena vs Fulgens Foligno", "Fulgens Foligno vs FC Siena", "Gavorrano vs FC Siena", "FC Siena vs Gavorrano", "Ghiviborgo vs FC Siena", "FC Siena vs Ghiviborgo", "FC Siena vs Grosseto", "FC Siena vs Livorno", "Livorno vs FC Siena", "FC Siena vs Montevarchi", "Montevarchi vs FC Siena", "Orvietana vs FC Siena", "FC Siena vs Orvietana", "Ostia Mare vs FC Siena", "Poggibonsi vs FC Siena", "FC Siena vs Poggibonsi", "FC Siena vs S. Trestina", "S. Trestina vs FC Siena", "San Donato vs FC Siena", "FC Siena vs San Donato", "FC Siena vs Sangiovannese", "Sangiovannese vs FC Siena", "FC Siena vs Seravezza", "Seravezza vs FC Siena", "T<PERSON> Trai<PERSON> vs FC Siena", "FC Siena vs T. Traiana"], "teams_affected": ["<PERSON>st<PERSON>", "Poggibonsi", "S. T<PERSON>ina", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Seravezza", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Orvietana", "FC Siena", "Gavorrano", "Grosseto", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Fulgens Foli<PERSON>", "Livorno", "Sang<PERSON>van<PERSON>", "Flaminia", "San Donato"], "total_matches": 544, "valid_matches": 512, "skipped_matches": 32}, "ITALY_SERIE_D_GROUP_B": {"warning_count": 72, "missing_matches": ["SantAngelo vs Arconatese", "USD Casatese vs Arconatese", "Arconatese vs SantAngelo", "Arconatese vs USD Casatese", "Breno vs SantAngelo", "Breno vs USD Casatese", "SantAngelo vs Breno", "USD Casatese vs Breno", "Castellanzese vs USD Casatese", "Castellanzese vs SantAngelo", "USD Casatese vs Castellanzese", "SantAngelo vs Castellanzese", "SantAngelo vs Chievo", "USD Casatese vs Chievo", "Chievo vs SantAngelo", "Chievo vs USD Casatese", "Ciliverghe vs USD Casatese", "SantAngelo vs Ciliverghe", "USD Casatese vs Ciliverghe", "Ciliverghe vs SantAngelo", "Club Milano vs USD Casatese", "SantAngelo vs Club Milano", "USD Casatese vs Club Milano", "Club Milano vs SantAngelo", "USD Casatese vs Crema", "Crema vs SantAngelo", "Crema vs USD Casatese", "SantAngelo vs Crema", "SantAngelo vs Desenxano", "Desenxano vs USD Casatese", "<PERSON>enxano vs SantAngelo", "USD Casatese vs Desenxano", "USD Casatese vs F. <PERSON>", "<PERSON><PERSON> vs SantAngelo", "<PERSON><PERSON> vs USD Casatese", "<PERSON><PERSON><PERSON><PERSON> vs F. Cara<PERSON>e", "SantAngelo vs Fanfulla", "Fanfulla vs USD Casatese", "Fanfulla vs SantAngelo", "USD Casatese vs Fanfulla", "USD Casatese vs Magenta", "Sant<PERSON>ngelo vs Magenta", "Magenta vs USD Casatese", "Magenta vs SantAngelo", "USD Casatese vs Ospitaletto", "Ospitaletto vs SantAngelo", "Ospitaletto vs USD Casatese", "SantAngelo vs Ospitaletto", "Pro Palazzolo vs SantAngelo", "Pro Palazzolo vs USD Casatese", "SantAngelo vs Pro Palazzolo", "USD Casatese vs Pro Palazzolo", "Pro Sesto vs USD Casatese", "SantAngelo vs Pro Sesto", "USD Casatese vs Pro Sesto", "Pro Sesto vs SantAngelo", "SantAngelo vs Sangiuliano C.", "USD Casatese vs Sangiuliano C.", "Sangiuliano C. vs SantAngelo", "Sangiuliano C. vs USD Casatese", "Sondrio vs SantAngelo", "Sondrio vs USD Casatese", "SantAngelo vs Sondrio", "USD Casatese vs Sondrio", "Varesina vs SantAngelo", "USD Casatese vs Varesina", "SantAngelo vs Varesina", "Varesina vs USD Casatese", "USD Casatese vs Vigasio", "Vigasio vs SantAngelo", "Vigasio vs USD Casatese", "SantAngelo vs Vigasio"], "teams_affected": ["Castellanzese", "Ma<PERSON><PERSON>", "Club Milano", "SantAnge<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Crema", "Sondr<PERSON>", "Vigasio", "Ciliverghe", "Breno", "Pro Sesto", "Arconatese", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Sangiuliano C.", "Pro Palazzolo", "USD Casatese", "<PERSON><PERSON>", "Varesina", "Chievo"], "total_matches": 666, "valid_matches": 594, "skipped_matches": 72}, "JAPAN_J3_LEAGUE": {"warning_count": 9, "missing_matches": ["<PERSON><PERSON><PERSON> vs Gifu", "Kagoshima Utd vs Kamatamare S.", "AC Numazu vs Matsumoto Y.", "<PERSON><PERSON><PERSON> Y. vs SC Sagamihara", "<PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON>.", "<PERSON><PERSON><PERSON> Y. vs Kochi United", "<PERSON><PERSON>moto Y. vs Tochigi City", "Nara Club vs Kusatsu", "<PERSON><PERSON> vs SC Sagamihara"], "teams_affected": ["<PERSON><PERSON><PERSON>", "SC Sagamihara", "Kochi United", "Tochigi City", "<PERSON><PERSON>", "Nara Club", "Gifu", "AC Numazu", "Kamatamare S.", "Kagoshima Utd", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>."], "total_matches": 17, "valid_matches": 8, "skipped_matches": 9}, "LATVIA_1_LIGA": {"warning_count": 7, "missing_matches": ["Marupe vs Augsdaugava", "Rezekne vs JFK Ventspils", "Skanste vs Augsdaugava", "Riga Mariners vs Skanste", "RFS B vs Skanste", "<PERSON><PERSON><PERSON> vs Alberts JDFS", "Tukums B vs Leevon PPK"], "teams_affected": ["Rezekne", "Riga Mariners", "Marupe", "JFK Ventspils", "Augsdaugava", "RFS B", "Leevon PPK", "Alberts JDFS", "Tukums B", "Skanste"], "total_matches": 8, "valid_matches": 1, "skipped_matches": 7}, "SOUTH_AFRICA_PREMIERSHIP": {"warning_count": 88, "missing_matches": ["Royal AM vs Cape Town City", "Cape Town City vs SuperSport Utd", "Cape Town City vs AmaZulu", "Marumo G. vs Cape Town City", "TS Galaxy vs Cape Town City", "AmaZulu vs Cape Town City", "Cape Town City vs TS Galaxy", "Cape Town City vs Marumo G.", "SuperSport Utd vs Cape Town City", "Chippa Utd vs TS Galaxy", "AmaZulu vs Chippa Utd", "Chippa Utd vs SuperSport Utd", "Chippa Utd vs Marumo G.", "TS Galaxy vs Chippa Utd", "Chippa Utd vs AmaZulu", "SuperSport Utd vs Chippa Utd", "Golden Arrows vs Marumo G.", "Golden Arrows vs SuperSport Utd", "Royal AM vs Golden Arrows", "AmaZulu vs Golden Arrows", "Golden Arrows vs TS Galaxy", "TS Galaxy vs Golden Arrows", "<PERSON><PERSON><PERSON> G. vs Golden Arrows", "Golden Arrows vs AmaZulu", "<PERSON><PERSON><PERSON> G. vs Kaizer Chiefs", "AmaZulu vs Kaizer Chiefs", "SuperSport Utd vs Kaizer Chiefs", "Kaizer Chiefs vs Royal AM", "TS Galaxy vs Kaizer Chiefs", "Kaizer Chiefs vs AmaZulu", "Kaizer Chiefs vs SuperSport Utd", "Kaizer Chiefs vs TS Galaxy", "Kaizer Chiefs vs Marumo G.", "<PERSON><PERSON><PERSON> vs Royal AM", "<PERSON><PERSON><PERSON> G. vs <PERSON><PERSON><PERSON>", "SuperSport Utd vs Magesi", "TS Galaxy vs Magesi", "<PERSON><PERSON><PERSON> vs AmaZulu", "AmaZulu vs Magesi", "Magesi vs SuperSport Utd", "Magesi vs TS Galaxy", "Mamelodi S. vs SuperSport Utd", "Mamelodi S. vs Marumo G.", "<PERSON><PERSON><PERSON><PERSON> vs Royal AM", "AmaZulu vs Mamelodi S.", "SuperSport Utd vs Mamelodi S.", "TS Galaxy vs Mamelodi S.", "<PERSON><PERSON><PERSON> G. vs Mamelodi S.", "Mamelodi S. vs TS Galaxy", "Mamelodi S. vs AmaZulu", "Orlando Pirates vs SuperSport Utd", "Orlando Pirates vs AmaZulu", "TS Galaxy vs Orlando Pirates", "Orlando Pirates vs Marumo G.", "Marumo G. vs Orlando Pirates", "Polokwane vs AmaZulu", "Royal AM vs Polokwane", "Polokwane vs TS Galaxy", "Polokwane vs Marumo G.", "SuperSport Utd vs Polokwane", "TS Galaxy vs Polokwane", "<PERSON>umo G. vs Polokwane", "Polokwane vs SuperSport Utd", "AmaZulu vs Polokwane", "Richards Bay vs TS Galaxy", "Royal AM vs Richards Bay", "Richards Bay vs AmaZulu", "Richards Bay vs SuperSport Utd", "<PERSON><PERSON><PERSON> G. vs Richards Bay", "Richards Bay vs Marumo G.", "AmaZulu vs Richards Bay", "TS Galaxy vs Richards Bay", "Royal AM vs Sekh<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs TS Galaxy", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs Marumo G.", "Am<PERSON>Z<PERSON> vs <PERSON><PERSON><PERSON><PERSON><PERSON>", "SuperSport Utd vs Sekhukhune", "Sekhukhune vs SuperSport Utd", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs AmaZulu", "<PERSON><PERSON><PERSON> G. vs <PERSON><PERSON><PERSON><PERSON><PERSON>", "TS Galaxy vs Sekhukhune", "Stellenbosch vs Marumo G.", "Stellenbosch vs TS Galaxy", "SuperSport Utd vs Stellenbosch", "AmaZulu vs Stellenbosch", "TS Galaxy vs Stellenbosch", "Marumo G. vs Stellenbosch", "Stellenbosch vs AmaZulu"], "teams_affected": ["Chippa Utd", "TS Galaxy", "Golden Arrows", "Royal AM", "Marumo G.", "Polokwane", "AmaZulu", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Stellenbosch", "<PERSON><PERSON><PERSON>", "Cape Town City", "<PERSON>", "SuperSport Utd", "Orlando Pirates", "Kaizer Chiefs", "Mamelodi S."], "total_matches": 286, "valid_matches": 198, "skipped_matches": 88}, "JAPAN_WE_LEAGUE": {"warning_count": 4, "missing_matches": ["Elfen Saitama W vs INAC Kobe L. W", "INAC Kobe L. W vs Elfen Saitama W", "Omiya Ardija W vs JEF United W", "JEF United W vs Omiya Ardija W"], "teams_affected": ["INAC Kobe L. W", "JEF United W", "Omiya Ardija W", "Elfen Saitama W"], "total_matches": 18, "valid_matches": 14, "skipped_matches": 4}, "GHANA_PREMIER_LEAGUE": {"warning_count": 78, "missing_matches": ["Accra Lions vs Heart of Lions", "Accra Lions vs Hearts of Oak", "Accra Lions vs Bibiani GS", "Heart of Lions vs Accra Lions", "Aduana Stars vs Heart of Lions", "Hearts of Oak vs Aduana Stars", "Bibiani GS vs Aduana Stars", "Heart of Lions vs Aduana Stars", "Aduana Stars vs Bibiani GS", "Aduana Stars vs Hearts of Oak", "<PERSON><PERSON> Kotoko vs Heart of Lions", "<PERSON><PERSON> Kotoko vs Hearts of Oak", "<PERSON>ante Kotoko vs Bibiani GS", "Heart of Lions vs Asante Kotoko", "Hearts of Oak vs Asante Kotoko", "Hearts of Oak vs Bechem Utd", "Bibiani GS vs Bechem Utd", "Bechem Utd vs Heart of Lions", "Bechem Utd vs Hearts of Oak", "Bechem Utd vs Bibiani GS", "Heart of Lions vs Berekum Chelsea", "Hearts of Oak vs Berekum Chelsea", "Bibiani GS vs Berekum Chelsea", "<PERSON>rekum Chelsea vs Heart of Lions", "Dreams vs Heart of Lions", "Dreams vs Hearts of Oak", "Bibiani GS vs Dreams", "Hearts of Oak vs Dreams", "Heart of Lions vs Dreams", "Dreams vs Bibiani GS", "Hearts of Oak vs Holy Stars", "Bibiani GS vs Holy Stars", "Heart of Lions vs Holy Stars", "Holy Stars vs Hearts of Oak", "Holy Stars vs Bibiani GS", "Karel<PERSON> vs Heart of Lions", "Karela vs Hearts of Oak", "Karela vs Bibiani GS", "Heart of Lions vs Karela", "Hearts of Oak vs Karela", "Bibiani GS vs Karela", "Bibiani GS vs Legon Cities", "Heart of Lions vs Legon Cities", "Hearts of Oak vs Legon Cities", "Legon Cities vs Bibiani GS", "Legon Cities vs Heart of Lions", "Medeama vs Heart of Lions", "Medeama vs Hearts of Oak", "Medeama vs Bibiani GS", "Hearts of Oak vs Medeama", "Heart of Lions vs Medeama", "Bibiani GS vs Medeama", "Nations vs Hearts of Oak", "Nations vs Bibiani GS", "Heart of Lions vs Nations", "Bibiani GS vs Nations", "Hearts of Oak vs Nations", "Heart of Lions vs Nsoatreman", "Hearts of Oak vs Nsoatreman", "Bibiani GS vs Nsoatreman", "Nsoatreman vs Heart of Lions", "Nsoatreman vs Hearts of Oak", "Samartex vs Bibiani GS", "Samartex vs Heart of Lions", "Samartex vs Hearts of Oak", "Bibiani GS vs Samartex", "Heart of Lions vs Samartex", "Heart of Lions vs Vision", "Hearts of Oak vs Vision", "Bibiani GS vs Vision", "Vision vs Heart of Lions", "Vision vs Hearts of Oak", "Vision vs Bibiani GS", "Young Apostles vs Hearts of Oak", "Young Apostles vs Bibiani GS", "Heart of Lions vs Young Apostles", "Hearts of Oak vs Young Apostles", "Bibiani GS vs Young Apostles"], "teams_affected": ["Young Apostles", "Heart of Lions", "<PERSON><PERSON>um Chelsea", "Aduana Stars", "Bibiani GS", "<PERSON><PERSON>", "Bechem Utd", "Dreams", "<PERSON><PERSON><PERSON>", "Hearts of Oak", "Legon Cities", "Medeama", "Nsoatreman", "Vision", "Accra Lions", "Nations", "Samartex", "Holy Stars"], "total_matches": 450, "valid_matches": 372, "skipped_matches": 78}, "SPAIN_PRIMERA_RFEF_GROUP_2": {"warning_count": 68, "missing_matches": ["Alcorcon vs Sevilla FC B", "Alcorcon vs UD Ibiza", "UD Ibiza vs Alcorcon", "UD Ibiza vs Alcoyano", "Alcoyano vs Sevilla FC B", "Alcoyano vs UD Ibiza", "Sevilla FC B vs Alcoyano", "Algeciras vs Sevilla FC B", "UD Ibiza vs Algeciras", "Sevilla FC B vs Algeciras", "Algeciras vs UD Ibiza", "Sevilla FC B vs Antequera", "Antequera vs UD Ibiza", "UD Ibiza vs Antequera", "Antequera vs Sevilla FC B", "UD Ibiza vs Atletico M. B", "Atletico M. B vs Sevilla FC B", "Sevilla FC B vs Atletico M. B", "Sevilla FC B vs Ceuta", "Ceuta vs UD Ibiza", "Ceuta vs Sevilla FC B", "Fuenlabrada vs UD Ibiza", "Sevilla FC B vs Fuenlabrada", "Fuenlabrada vs Sevilla FC B", "UD Ibiza vs Fuenlabrada", "UD Ibiza vs Hercules", "Hercules vs Sevilla FC B", "Sevilla FC B vs Hercules", "Hercules vs UD Ibiza", "Intercity vs UD Ibiza", "Intercity vs Sevilla FC B", "Sevilla FC B vs Intercity", "UD Ibiza vs Intercity", "Marbella vs Sevilla FC B", "UD Ibiza vs Marbella", "Sevilla FC B vs Marbella", "Marbella vs UD Ibiza", "Merida vs UD Ibiza", "Sevilla FC B vs Merida", "UD Ibiza vs Merida", "Merida vs Sevilla FC B", "UD Ibiza vs Real Betis B", "Real Betis B vs Sevilla FC B", "Real Betis B vs UD Ibiza", "Sevilla FC B vs Real Betis B", "Real Madrid B vs UD Ibiza", "Sevilla FC B vs Real Madrid B", "Real Madrid B vs Sevilla FC B", "UD Ibiza vs Real Madrid B", "Sevilla FC B vs Real Murcia", "UD Ibiza vs Real Murcia", "Real Murcia vs Sevilla FC B", "Real Murcia vs UD Ibiza", "Recreativo H. vs Sevilla FC B", "UD Ibiza vs Recreativo H.", "Sevilla FC B vs Recreativo H.", "Recreativo H. vs UD Ibiza", "Sevilla FC B vs Sanluqueno", "Sanluqueno vs UD Ibiza", "Sanluqueno vs Sevilla FC B", "UD Ibiza vs Sanluqueno", "UD Ibiza vs Villarreal B", "Sevilla FC B vs Villarreal B", "Villarreal B vs UD Ibiza", "Yeclano vs UD Ibiza", "Sevilla FC B vs Yeclano", "Yeclano vs Sevilla FC B", "UD Ibiza vs Yeclano"], "teams_affected": ["Fuenlabrada", "Villarreal B", "Marbella", "Atletico M. B", "<PERSON><PERSON><PERSON><PERSON>", "Alcorcon", "<PERSON><PERSON>", "UD Ibiza", "Sevilla FC B", "Real Madrid B", "Alcoyano", "Sanluqueno", "Hercules", "Real Murcia", "Yeclano", "Algeciras", "<PERSON><PERSON>", "Real Betis B", "Recreativo H.", "Intercity"], "total_matches": 648, "valid_matches": 580, "skipped_matches": 68}, "THAILAND_THAI_LEAGUE_2": {"warning_count": 60, "missing_matches": ["Bangkok FC vs Ayutthaya Utd", "Ayutthaya Utd vs Trat FC", "Trat FC vs Ayutthaya Utd", "Ayutthaya Utd vs Bangkok FC", "Chainat vs Bangkok FC", "Trat FC vs Chainat", "Bangkok FC vs Chainat", "Chainat vs Trat FC", "Trat FC vs Chanthaburi", "Chanthaburi vs Bangkok FC", "Bangkok FC vs Chanthaburi", "Chanthaburi vs Trat FC", "Chiangmai Utd vs Bangkok FC", "Trat FC vs Chiangmai Utd", "Bangkok FC vs Chiangmai Utd", "Chiangmai Utd vs Trat FC", "Bangkok FC vs Chonburi", "Chonburi vs Trat FC", "Chonburi vs Bangkok FC", "Trat FC vs Chonburi", "Kanchanaburi vs Trat FC", "Kanchanaburi vs Bangkok FC", "Trat FC vs Kanchanaburi", "Bangkok FC vs Kanchanaburi", "Bangkok FC vs Kasetsart", "Kasetsart vs Trat FC", "Kasetsart vs Bangkok FC", "Trat FC vs Kasetsart", "Lampang vs Bangkok FC", "Trat FC vs Lampang", "Bangkok FC vs Lampang", "Lampang vs Trat FC", "Mahasarakham vs Bangkok FC", "Trat FC vs Mahasarakham", "Bangkok FC vs Mahasarakham", "Mahasarakham vs Trat FC", "Nakhon Si vs Bangkok FC", "Nakhon Si vs Trat FC", "Bangkok FC vs Nakhon Si", "Trat FC vs Nakhon Si", "Trat FC vs Pattaya United", "Bangkok FC vs Pattaya United", "Pattaya United vs Trat FC", "Pattaya United vs Bangkok FC", "Bangkok FC vs Phrae Utd", "Trat FC vs Phrae Utd", "Phrae Utd vs Bangkok FC", "Phrae Utd vs Trat FC", "Bangkok FC vs Police Tero", "Trat FC vs Police Tero", "Police Tero vs Bangkok FC", "Police Tero vs Trat FC", "Sisaket Utd vs Bangkok FC", "Sisaket Utd vs Trat FC", "Bangkok FC vs Sisaket Utd", "Trat FC vs Sisaket Utd", "Suphanburi vs Trat FC", "Suphanburi vs Bangkok FC", "Trat FC vs Suphanburi", "Bangkok FC vs Suphanburi"], "teams_affected": ["Phrae Utd", "Lampang", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Ayutthaya Utd", "Pattaya United", "Kanchanaburi", "Chainat", "Chiangmai Utd", "<PERSON><PERSON><PERSON><PERSON>", "Chonburi", "<PERSON><PERSON><PERSON>", "Police Tero", "Trat FC", "Bangkok FC", "Nakhon Si", "Sisaket Utd", "<PERSON><PERSON><PERSON><PERSON>"], "total_matches": 480, "valid_matches": 420, "skipped_matches": 60}, "GERMANY_OBERLIGA_WESTFALEN": {"warning_count": 57, "missing_matches": ["Bochum B vs Finnentrop / B.", "Bochum B vs ASC Dortmund", "Finnentrop / B. vs Bochum B", "Finnentrop / B. vs <PERSON><PERSON>", "ASC Dortmund vs C. <PERSON>", "<PERSON><PERSON> vs Finnentrop / B.", "<PERSON><PERSON> vs ASC Dortmund", "Finnentrop / B. vs <PERSON><PERSON>", "ASC Dortmund vs <PERSON>. <PERSON>", "<PERSON><PERSON> vs Finnentrop / B.", "<PERSON><PERSON> vs ASC Dortmund", "Finnentrop / B. vs Ennepetal", "ASC Dortmund vs Ennepetal", "Ennepetal vs Finnentrop / B.", "Ennepetal vs ASC Dortmund", "Finnentrop / B. vs Erkenschwick", "ASC Dortmund vs Erkenschwick", "Erkenschwick vs Finnentrop / B.", "Erkenschwick vs ASC Dortmund", "Finnentrop / B. vs Gievenbeck", "ASC Dortmund vs Gievenbeck", "Gievenbeck vs Finnentrop / B.", "Finnentrop / B. vs Lippstadt", "ASC Dortmund vs Lippstadt", "P. Munster B vs Finnentrop / B.", "P. Munster B vs ASC Dortmund", "Finnentrop / B. vs P. Munster B", "ASC Dortmund vs P. Munster B", "R<PERSON> vs ASC Dortmund", "Rot <PERSON> vs Finnentrop / B.", "<PERSON><PERSON> vs Finnentrop / B.", "<PERSON><PERSON> vs ASC Dortmund", "Finnentrop / B. vs <PERSON><PERSON>n", "ASC Dortmund vs S. Siegen", "<PERSON><PERSON>mbeck vs Finnentrop / B.", "<PERSON><PERSON><PERSON><PERSON> vs ASC Dortmund", "Finnentrop / B. vs Schermbeck", "ASC Dortmund vs Schermbeck", "<PERSON><PERSON> vs ASC Dortmund", "<PERSON><PERSON> vs Finnentrop / B.", "ASC Dortmund vs <PERSON><PERSON>", "Finnentrop / B. vs Verl B", "ASC Dortmund vs Verl B", "Verl B vs Finnentrop / B.", "Verl B vs ASC Dortmund", "Vreden vs Finnentrop / B.", "Vreden vs ASC Dortmund", "Finnentrop / B. vs Vreden", "ASC Dortmund vs Vreden", "<PERSON><PERSON> vs Finnentrop / B.", "<PERSON><PERSON> vs ASC Dortmund", "Finnentrop / B. vs <PERSON><PERSON>", "ASC Dortmund vs <PERSON><PERSON>", "Finnentrop / B. vs Wattenscheid 09", "Wattenscheid 09 vs ASC Dortmund", "Wattenscheid 09 vs Finnentrop / B.", "ASC Dortmund vs Wattenscheid 09"], "teams_affected": ["Lippstadt", "Verl B", "Erkenschwick", "Wattenscheid 09", "<PERSON><PERSON>", "Bochum B", "P. <PERSON> B", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "V<PERSON><PERSON>", "<PERSON><PERSON>", "Finnentrop / B.", "ASC Dortmund", "<PERSON><PERSON>", "<PERSON><PERSON>", "Ennepetal", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "total_matches": 485, "valid_matches": 428, "skipped_matches": 57}, "SWEDEN_ALLSVENSKAN_WOMEN": {"warning_count": 3, "missing_matches": ["AIK W vs Linkoping W", "Brommapojk. W vs Linkoping W", "Alingsas W vs Linkoping W"], "teams_affected": ["Linkoping W", "AIK W", "Brommapojk. W", "Alingsas W"], "total_matches": 9, "valid_matches": 6, "skipped_matches": 3}, "GERMANY_OBERLIGA_BAYERN_NORD": {"warning_count": 61, "missing_matches": ["Abtswind vs Wurzburger FV", "Abtswind vs ATSV Erlangen", "Wurzburger FV vs Abtswind", "ATSV Erlangen vs Abtswind", "ATSV Erlangen vs Ammerthal", "Wurzburger FV vs Ammerthal", "Ammerthal vs ATSV Erlangen", "Bayern Hof vs Wurzburger FV", "Bayern Hof vs ATSV Erlangen", "Wurzburger FV vs Bayern Hof", "ATSV Erlangen vs Bayern Hof", "Wurzburger FV vs Cham", "ATSV Erlangen vs Cham", "Cham vs Wurzburger FV", "Cham vs ATSV Erlangen", "Eichstatt vs Wurzburger FV", "Eichstatt vs ATSV Erlangen", "Wurzburger FV vs Eichstatt", "ATSV Erlangen vs Eichstatt", "Wurzburger FV vs Eltersdorf", "ATSV Erlangen vs Eltersdorf", "Eltersdorf vs Wurzburger FV", "Eltersdorf vs ATSV Erlangen", "F. Regensburg vs Wurzburger FV", "F. Regensburg vs ATSV Erlangen", "Wurzburger FV vs F. Regensburg", "ATSV Erlangen vs F. Regensburg", "Gebenbach vs Wurzburger FV", "Gebenbach vs ATSV Erlangen", "Wurzburger FV vs Gebenbach", "ATSV Erlangen vs Gebenbach", "Wurzburger FV vs Ingolstadt B", "ATSV Erlangen vs Ingolstadt B", "Ingolstadt B vs Wurzburger FV", "Ingolstadt B vs ATSV Erlangen", "J. Regensburg B vs ATSV Erlangen", "Wurzburger FV vs J. Regensburg B", "J. Regensburg B vs Wurzburger FV", "ATSV Erlangen vs J. Regensburg B", "Karlburg vs Wurzburger FV", "Karlburg vs ATSV Erlangen", "Wurzburger FV vs Karlburg", "ATSV Erlangen vs Karlburg", "Wurzburger FV vs Kornburg", "ATSV Erlangen vs Kornburg", "Kornburg vs Wurzburger FV", "Kornburg vs ATSV Erlangen", "Munchberg vs Wurzburger FV", "<PERSON><PERSON><PERSON> vs ATSV Erlangen", "Wurzburger FV vs Munchberg", "Wurzburger FV vs Neudrossenfeld", "ATSV Erlangen vs Neudrossenfeld", "Neudrossenfeld vs Wurzburger FV", "Neudrossenfeld vs ATSV Erlangen", "Neumarkt vs ATSV Erlangen", "Neumarkt vs Wurzburger FV", "ATSV Erlangen vs Neumarkt", "Wurzburger FV vs Weiden", "ATSV Erlangen vs Weiden", "Weiden vs Wurzburger FV", "Weiden vs ATSV Erlangen"], "teams_affected": ["<PERSON><PERSON>", "Kornburg", "Bayern Hof", "J. Regensburg B", "Ingolstadt B", "<PERSON><PERSON>", "Ammerthal", "Neudrossenfeld", "Abtswind", "Neumarkt", "ATSV Erlangen", "Karlburg", "<PERSON><PERSON><PERSON>", "F. Regensburg", "Wurzburger FV", "Gebenbach", "Eichstatt", "Eltersdorf"], "total_matches": 527, "valid_matches": 466, "skipped_matches": 61}, "ITALY_SERIE_D_GROUP_D": {"warning_count": 64, "missing_matches": ["Corticella vs San Marino C.", "Corticella vs Cittadella Vis", "San Marino C. vs Corticella", "Cittadella Vis vs Corticella", "San Marino C. vs Fiorenzuola", "Cittadella Vis vs Fiorenzuola", "Fiorenzuola vs San Marino C.", "Fiorenzuola vs Cittadella Vis", "Cittadella Vis vs Forli", "Forli vs San Marino C.", "Forli vs Cittadella Vis", "San Marino C. vs Forli", "Cittadella Vis vs Imolese", "Imolese vs San Marino C.", "Imolese vs Cittadella Vis", "San Marino C. vs Imolese", "San Marino C. vs Lentigione", "Cittadella Vis vs Lentigione", "Lentigione vs San Marino C.", "Lentigione vs Cittadella Vis", "Cittadella Vis vs Piacenza", "San Marino C. vs Piacenza", "Piacenza vs Cittadella Vis", "Piacenza vs San Marino C.", "Pistoiese vs Cittadella Vis", "Pistoiese vs San Marino C.", "Cittadella Vis vs Pistoiese", "San Marino C. vs Pistoiese", "San Marino C. vs Prato", "Cittadella Vis vs Prato", "Prato vs San Marino C.", "Prato vs Cittadella Vis", "Progresso vs San Marino C.", "Progresso vs Cittadella Vis", "San Marino C. vs Progresso", "Cittadella Vis vs Progresso", "Ravenna vs San Marino C.", "Ravenna vs Cittadella Vis", "San Marino C. vs Ravenna", "Cittadella Vis vs Ravenna", "Sammaurese vs Cittadella Vis", "San Marino C. vs Sammaurese", "Cittadella Vis vs Sammaurese", "Sammaurese vs San Marino C.", "San Marino C. vs Sasso Marconi", "Cittadella Vis vs Sasso <PERSON>", "Sasso Marconi vs San Marino C.", "Sasso Marconi vs Cittadella Vis", "Tau Altopascio vs San Marino C.", "Cittadella Vis vs Tau Altopascio", "San Marino C. vs Tau Altopascio", "Tau Altopascio vs Cittadella Vis", "Tuttocuoio vs San Marino C.", "Tuttocuoio vs Cittadella Vis", "San Marino C. vs Tuttocuoio", "Cittadella Vis vs Tuttocuoio", "United Riccione vs Cittadella Vis", "San Marino C. vs United Riccione", "Cittadella Vis vs United Riccione", "United Riccione vs San Marino C.", "San Marino C. vs Zenith Prato", "Zenith Prato vs Cittadella Vis", "Zenith Prato vs San Marino C.", "Cittadella Vis vs Zenith Prato"], "teams_affected": ["Piacenza", "Prato", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Corticella", "<PERSON><PERSON><PERSON><PERSON>", "Progresso", "<PERSON><PERSON>", "Pistoiese", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "United Riccione", "Lentigione", "San Marino C.", "<PERSON><PERSON>", "Cittadella Vis", "Tau Altopascio", "Imolese"], "total_matches": 542, "valid_matches": 478, "skipped_matches": 64}, "SWEDEN_ALLSVENSKAN": {"warning_count": 8, "missing_matches": ["Djurgarden vs Oster", "Elfsborg vs GAIS", "Halmstad vs Degerfors", "Mjallby vs GAIS", "Mjallby vs Degerfors", "Mjallby vs IFK Goteborg", "AIK vs Mjallby", "Varnamo vs AIK"], "teams_affected": ["Elfsborg", "<PERSON><PERSON><PERSON><PERSON>", "AIK", "<PERSON><PERSON><PERSON>", "Djurgarden", "M<PERSON>ll<PERSON>", "IFK Goteborg", "GAIS", "<PERSON><PERSON>", "Varnamo"], "total_matches": 14, "valid_matches": 6, "skipped_matches": 8}, "SWEDEN_DIV_2_SODRA_SVEALAND": {"warning_count": 7, "missing_matches": ["Far<PERSON> vs Atvidaberg", "Forward vs S<PERSON><PERSON>ner", "Arameiska-S. vs Smedby", "<PERSON><PERSON><PERSON>-S<PERSON> vs <PERSON>", "Forward vs Sylvia", "Sylvia vs Eker Orebro", "<PERSON><PERSON> vs Sylvia"], "teams_affected": ["S<PERSON><PERSON>", "Farsta", "<PERSON><PERSON><PERSON><PERSON>", "Arameiska-S.", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Forward", "<PERSON>"], "total_matches": 12, "valid_matches": 5, "skipped_matches": 7}, "SPAIN_SEGUNDA_RFEF_GROUP_2": {"warning_count": 64, "missing_matches": ["Alaves B vs UD Logrones", "Alaves B vs SD Logrones", "UD Logrones vs Alaves B", "SD Logrones vs Alaves B", "Alfaro vs SD Logrones", "Alfaro vs UD Logrones", "SD Logrones vs Alfaro", "UD Logrones vs Alfaro", "Anguiano vs SD Logrones", "Anguiano vs UD Logrones", "SD Logrones vs Anguiano", "UD Logrones vs Anguiano", "SD Logrones vs Arenas Club", "UD Logrones vs Arenas Club", "Arenas Club vs SD Logrones", "Arenas Club vs UD Logrones", "SD Logrones vs Barbastro", "UD Logrones vs Barbastro", "Barbastro vs SD Logrones", "Barbastro vs UD Logrones", "Calahorra vs SD Logrones", "UD Logrones vs Calahorra", "SD Logrones vs Calahorra", "Calahorra vs UD Logrones", "UD Logrones vs Eibar B", "Eibar B vs SD Logrones", "Eibar B vs UD Logrones", "SD Logrones vs Eibar B", "UD Logrones vs Ejea", "Ejea vs SD Logrones", "Ejea vs UD Logrones", "SD Logrones vs Ejea", "UD Logrones vs Gernika", "Gernika vs SD Logrones", "Gernika vs UD Logrones", "SD Logrones vs Gernika", "Izarra vs UD Logrones", "SD Logrones vs Izarra", "UD Logrones vs Izarra", "Izarra vs SD Logrones", "SD Logrones vs Real Sociedad C", "UD Logrones vs Real Sociedad C", "Real Sociedad C vs SD Logrones", "Real Sociedad C vs UD Logrones", "UD Logrones vs Real Zaragoza B", "SD Logrones vs Real Zaragoza B", "Real Zaragoza B vs UD Logrones", "Real Zaragoza B vs SD Logrones", "Subiza vs SD Logrones", "Subiza vs UD Logrones", "SD Logrones vs Subiza", "UD Logrones vs Subiza", "SD Logrones vs Teruel", "Teruel vs UD Logrones", "Teruel vs SD Logrones", "UD Logrones vs Teruel", "Tudelano vs UD Logrones", "SD Logrones vs Tudelano", "UD Logrones vs Tudelano", "Tudelano vs SD Logrones", "Utebo vs UD Logrones", "SD Logrones vs Utebo", "UD Logrones vs Utebo", "Utebo vs SD Logrones"], "teams_affected": ["Barbas<PERSON>", "Real Zaragoza B", "Calahorra", "Alaves B", "Real Sociedad C", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Eibar B", "U<PERSON>bo", "UD Logrones", "<PERSON><PERSON><PERSON>", "Arenas Club", "SD Logrones", "Subiza", "<PERSON><PERSON><PERSON>", "Teruel"], "total_matches": 544, "valid_matches": 480, "skipped_matches": 64}, "RUSSIA_PREMIER_LEAGUE": {"warning_count": 50, "missing_matches": ["Akhmat Grozny vs FC Krasnodar", "CSKA Moscow vs Akhmat Grozny", "FC Krasnodar vs Akhmat Grozny", "CSKA Moscow vs Akron", "Akron vs FC Krasnodar", "Akron vs CSKA Moscow", "FC Krasnodar vs Akron", "Dinamo Moscow vs FC Krasnodar", "Dinamo Moscow vs CSKA Moscow", "CSKA Moscow vs Dinamo Moscow", "Fakel Voronezh vs FC Krasnodar", "CSKA Moscow vs Fakel Voronezh", "Fakel Voronezh vs CSKA Moscow", "FC Krasnodar vs Fakel Voronezh", "Khimki vs CSKA Moscow", "FC Krasnodar vs Khimki", "Khimki vs FC Krasnodar", "CSKA Moscow vs Khimki", "Krylya Sovetov vs FC Krasnodar", "Krylya Sovetov vs CSKA Moscow", "FC Krasnodar vs Krylya Sovetov", "CSKA Moscow vs Krylya Sovetov", "CSKA Moscow vs Lokomotiv M.", "Lokomotiv M. vs FC Krasnodar", "FC Krasnodar vs Lokomotiv M.", "FC Krasnodar vs Makhachkala", "Makhachkala vs CSKA Moscow", "CSKA Moscow vs Makhachkala", "Makhachkala vs FC Krasnodar", "Nizhny Novgorod vs CSKA Moscow", "FC Krasnodar vs Nizhny Novgorod", "Nizhny Novgorod vs FC Krasnodar", "CSKA Moscow vs Orenburg", "FC Krasnodar vs Orenburg", "Orenburg vs CSKA Moscow", "Rostov vs CSKA Moscow", "FC Krasnodar vs Rostov", "CSKA Moscow vs Rostov", "Rostov vs FC Krasnodar", "Rubin Kazan vs CSKA Moscow", "Rubin Kazan vs FC Krasnodar", "CSKA Moscow vs Rubin Kazan", "Spartak Moscow vs FC Krasnodar", "CSKA Moscow vs Spartak Moscow", "FC Krasnodar vs Spartak Moscow", "Spartak Moscow vs CSKA Moscow", "CSKA Moscow vs Zenit", "FC Krasnodar vs Zenit", "Zenit vs CSKA Moscow", "Zenit vs FC Krasnodar"], "teams_affected": ["Ma<PERSON><PERSON><PERSON><PERSON>", "Spartak Moscow", "Rubin Kazan", "Fakel Voronezh", "<PERSON><PERSON><PERSON>", "Akron", "Lokomotiv M.", "CSKA Moscow", "Orenburg", "<PERSON><PERSON><PERSON>", "FC Krasnodar", "Dinamo Moscow", "<PERSON><PERSON><PERSON>", "Nizhny Novgorod", "Kryl<PERSON> Sovetov", "Zenit"], "total_matches": 364, "valid_matches": 314, "skipped_matches": 50}, "SCOTLAND_LEAGUE_TWO": {"warning_count": 36, "missing_matches": ["Bonnyrigg Rose vs Clyde", "Clyde vs Bonnyrigg Rose", "Bonnyrigg Rose vs Clyde", "Clyde vs Bonnyrigg Rose", "East Fife vs Bonnyrigg Rose", "Bonnyrigg Rose vs East Fife", "East Fife vs Bonnyrigg Rose", "Bonnyrigg Rose vs East Fife", "Edinburgh City vs Bonnyrigg Rose", "Bonnyrigg Rose vs Edinburgh City", "Edinburgh City vs Bonnyrigg Rose", "Bonnyrigg Rose vs Edinburgh City", "Elgin City vs Bonnyrigg Rose", "Bonnyrigg Rose vs Elgin City", "Elgin City vs Bonnyrigg Rose", "Bonnyrigg Rose vs Elgin City", "Bonnyr<PERSON><PERSON> vs Forfar Athletic", "Forfar Athletic vs Bonnyrigg Rose", "Bonnyr<PERSON><PERSON> vs Forfar Athletic", "Forfar Athletic vs Bonnyrigg Rose", "Bonn<PERSON><PERSON><PERSON> vs Peterhead", "Peterhead vs Bonnyrigg Rose", "Bonn<PERSON><PERSON><PERSON> vs Peterhead", "Peterhead vs Bonnyrigg Rose", "Spartans vs Bonnyrigg Rose", "Bonnyrigg Rose vs Spartans", "Spartans vs Bonnyrigg Rose", "Bonnyrigg Rose vs Spartans", "Stirling Albion vs Bonnyrigg Rose", "Bonnyrigg Rose vs Stirling Albion", "Stirling Albion vs Bonnyrigg Rose", "Bonnyrigg Rose vs Stirling Albion", "Bonnyrigg Rose vs Stranraer", "Stranraer vs Bonnyrigg Rose", "Bonnyrigg Rose vs Stranraer", "Stranraer vs Bonnyrigg Rose"], "teams_affected": ["Spartans", "Stirling Albion", "Forfar Athletic", "Peterhead", "Edinburgh City", "<PERSON><PERSON><PERSON><PERSON>", "Clyde", "East Fife", "Stranraer", "Elgin City"], "total_matches": 324, "valid_matches": 288, "skipped_matches": 36}, "SWEDEN_DIV_2_NORRLAND": {"warning_count": 4, "missing_matches": ["Boden City vs Bergnasets", "IFK Ostersund vs Lucksta", "Lucksta vs Umea FC", "Gottne IF vs Lucksta"], "teams_affected": ["Bergnasets", "Umea FC", "Boden City", "Gottne IF", "IFK Ostersund", "<PERSON><PERSON>"], "total_matches": 5, "valid_matches": 1, "skipped_matches": 4}, "AUSTRALIA_SOUTH_AUSTRALIA_NPL": {"warning_count": 4, "missing_matches": ["AC Raiders vs Adelaide Comets", "Para Hills vs Adelaide Utd B", "West Torrens vs Campbelltown", "Croydon Kings vs Playford P."], "teams_affected": ["Campbelltown", "Playford P.", "AC Raiders", "Adelaide Utd B", "Croydon Kings", "Adelaide Comets", "West Torrens", "Para Hills"], "total_matches": 6, "valid_matches": 2, "skipped_matches": 4}, "AUSTRALIA_CAPITAL_TERRITORY_NPL": {"warning_count": 1, "missing_matches": ["Monaro Panthers vs OConnor Knight"], "teams_affected": ["Monaro Panthers", "<PERSON><PERSON><PERSON><PERSON>"], "total_matches": 7, "valid_matches": 6, "skipped_matches": 1}, "POLAND_I_LIGA": {"warning_count": 32, "missing_matches": ["Arka Gdynia vs LKS Lodz", "LKS Lodz vs Arka Gdynia", "LKS Lodz vs Chrobry Glogow", "Chrobry Glogow vs LKS Lodz", "LKS Lodz vs Gornik Leczna", "Gornik Leczna vs LKS Lodz", "LKS Lodz vs K. Kolobrzeg", "<PERSON><PERSON> vs LKS Lodz", "<PERSON><PERSON>z Legnica vs LKS Lodz", "LKS Lodz vs Miedz Legnica", "Odra Opole vs LKS Lodz", "LKS Lodz vs Odra Opole", "LKS Lodz vs Pogon Siedlce", "Pogon Siedlce vs LKS Lodz", "LKS Lodz vs Polonia Warszaw", "LKS Lodz vs Ruch Chorzow", "Ruch Chorzow vs LKS Lodz", "Stal Rzeszow vs LKS Lodz", "LKS Lodz vs Stal Rzeszow", "LKS Lodz vs Stalowa Wola", "Stalowa Wola vs LKS Lodz", "<PERSON><PERSON> vs LKS Lodz", "LKS Lodz vs T. <PERSON>", "Tychy vs LKS Lodz", "LKS Lodz vs Tychy", "Warta Poznan vs LKS Lodz", "LKS Lodz vs Warta Poznan", "LKS Lodz vs Wisla Krakow", "<PERSON><PERSON>la Krakow vs LKS Lodz", "LKS Lodz vs <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> vs LKS Lodz", "Znicz Pruszkow vs LKS Lodz"], "teams_affected": ["Gornik Leczna", "Polonia Warszaw", "<PERSON><PERSON><PERSON> Krakow", "Znicz Pruszkow", "Ruch Chorzow", "Tychy", "<PERSON><PERSON><PERSON>", "<PERSON>dra <PERSON>", "Pogon Siedlce", "Warta Poznan", "<PERSON><PERSON>", "Stal Rzeszow", "<PERSON><PERSON><PERSON>", "Arka Gdynia", "Stalowa Wola", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "LKS Lodz"], "total_matches": 542, "valid_matches": 510, "skipped_matches": 32}, "NETHERLANDS_EREDIVISIE_WOMEN": {"warning_count": 67, "missing_matches": ["Ajax W vs PEC Zwolle W", "AZ W vs Ajax W", "ADO Den Haag W vs Ajax W", "Ajax W vs FC Twente W", "Ajax W vs PSV Eindhoven W", "Ajax W vs AZ W", "PEC Zwolle W vs Ajax W", "PSV Eindhoven W vs Ajax W", "Ajax W vs ADO Den Haag W", "FC Twente W vs Ajax W", "FC Twente W vs Excelsior W", "AZ W vs Excelsior W", "Excelsior W vs PSV Eindhoven W", "Excelsior W vs PEC Zwolle W", "ADO Den Haag W vs Excelsior W", "PSV Eindhoven W vs Excelsior W", "PEC Zwolle W vs Excelsior W", "Excelsior W vs ADO Den Haag W", "Excelsior W vs FC Twente W", "Excelsior W vs AZ W", "Feyenoord W vs AZ W", "ADO Den Haag W vs Feyenoord W", "Feyenoord W vs PEC Zwolle W", "FC Twente W vs Feyenoord W", "Feyenoord W vs PSV Eindhoven W", "AZ W vs Feyenoord W", "Feyenoord W vs ADO Den Haag W", "Feyenoord W vs FC Twente W", "PEC Zwolle W vs Feyenoord W", "Fortuna S. W vs FC Twente W", "PEC Zwolle W vs Fortuna S. W", "Fortuna S. W vs AZ W", "PSV Eindhoven W vs Fortuna S. W", "Fortuna S. W vs ADO Den Haag W", "FC Twente W vs Fortuna S. W", "Fortuna S. W vs PEC Zwolle W", "AZ W vs Fortuna S. W", "Fortuna S. W vs PSV Eindhoven W", "Heerenveen W vs ADO Den Haag W", "Heerenveen W vs PSV Eindhoven W", "AZ W vs Heerenveen W", "Heerenveen W vs FC Twente W", "PEC Zwolle W vs Heerenveen W", "ADO Den Haag W vs Heerenveen W", "FC Twente W vs Heerenveen W", "Heerenveen W vs AZ W", "Heerenveen W vs PEC Zwolle W", "PSV Eindhoven W vs Heerenveen W", "PSV Eindhoven W vs Telstar W", "PEC Zwolle W vs Telstar W", "Telstar W vs FC Twente W", "AZ W vs Telstar W", "Telstar W vs ADO Den Haag W", "Telstar W vs PEC Zwolle W", "FC Twente W vs Telstar W", "Telstar W vs PSV Eindhoven W", "Telstar W vs AZ W", "ADO Den Haag W vs Telstar W", "PEC Zwolle W vs Utrecht W", "Utrecht W vs ADO Den Haag W", "FC Twente W vs Utrecht W", "Utrecht W vs PSV Eindhoven W", "AZ W vs Utrecht W", "ADO Den Haag W vs Utrecht W", "Utrecht W vs FC Twente W", "PSV Eindhoven W vs Utrecht W", "Utrecht W vs AZ W"], "teams_affected": ["AZ W", "Feyenoord W", "Ajax W", "Utrecht W", "Telstar W", "Excelsior W", "FC Twente W", "PEC Zwolle W", "PSV Eindhoven W", "Heerenveen W", "ADO Den Haag W", "Fortuna S. W"], "total_matches": 147, "valid_matches": 80, "skipped_matches": 67}, "ITALY_SERIE_C_GROUP_B": {"warning_count": 70, "missing_matches": ["Arezzo vs SPAL", "AC Milan B vs Arezzo", "SPAL vs Arezzo", "Arezzo vs AC Milan B", "SPAL vs Ascoli", "AC Milan B vs Ascoli", "Ascoli vs SPAL", "Ascoli vs AC Milan B", "AC Milan B vs Athletic Carpi", "SPAL vs Athletic Carpi", "Athletic Carpi vs AC Milan B", "Athletic Carpi vs SPAL", "Campobasso vs SPAL", "Campobasso vs AC Milan B", "SPAL vs Campobasso", "AC Milan B vs Campobasso", "Entella vs AC Milan B", "SPAL vs Entella", "AC Milan B vs Entella", "Entella vs SPAL", "AC Milan B vs Gubbio", "Gubbio vs SPAL", "Gubbio vs AC Milan B", "AC Milan B vs Legnago Salus", "Legnago <PERSON> vs SPAL", "Legnago Sal<PERSON> vs AC Milan B", "SPAL vs Legnago Salus", "SPAL vs Lucchese", "Lucchese vs AC Milan B", "Lucchese vs SPAL", "AC Milan B vs Lucchese", "Perugia vs SPAL", "Perugia vs AC Milan B", "SPAL vs Perugia", "AC Milan B vs Perugia", "SPAL vs Pescara", "Pescara vs AC Milan B", "AC Milan B vs Pescara", "Pescara vs SPAL", "AC Milan B vs Pianese", "SPAL vs Pianese", "Pianese vs AC Milan B", "Pianese vs SPAL", "AC Milan B vs Pineto", "SPAL vs Pineto", "Pineto vs AC Milan B", "Pineto vs SPAL", "Pontedera vs AC Milan B", "Pontedera vs SPAL", "AC Milan B vs Pontedera", "SPAL vs Pontedera", "Rimini vs AC Milan B", "R<PERSON>ini vs SPAL", "AC Milan B vs Rimini", "SPAL vs Rimini", "Sestri Levante vs SPAL", "AC Milan B vs Sestri Levante", "SPAL vs Sestri Levante", "Sestri Levante vs AC Milan B", "Ternana vs SPAL", "Ternana vs AC Milan B", "SPAL vs Ternana", "AC Milan B vs Ternana", "<PERSON> vs AC Milan B", "SPAL vs Torres", "AC Milan B vs Torres", "<PERSON> vs SPAL", "SPAL vs Vis P<PERSON>aro", "<PERSON><PERSON> vs AC Milan B", "<PERSON><PERSON> vs SPAL"], "teams_affected": ["<PERSON><PERSON><PERSON>", "Pontedera", "Pianese", "Pescara", "Ternana", "Athletic Carpi", "SPAL", "Campobasso", "Arezzo", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Sestri Levante", "<PERSON>", "<PERSON><PERSON><PERSON>", "Pineto", "Perugia", "AC Milan B"], "total_matches": 666, "valid_matches": 596, "skipped_matches": 70}, "JAPAN_NADESHIKO_LEAGUE_1": {"warning_count": 4, "missing_matches": ["NGU Nagoya W vs Ehime W", "<PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> Ku<PERSON>ichi W", "NGU Nagoya W vs Iga Kunoichi W"], "teams_affected": ["<PERSON><PERSON><PERSON>", "NGU Nagoya W", "Iga Kunoichi W", "<PERSON><PERSON>"], "total_matches": 20, "valid_matches": 16, "skipped_matches": 4}, "GERMANY_3_LIGA": {"warning_count": 95, "missing_matches": ["VfB Stuttgart B vs 1860 Munchen", "FC Ingolstadt vs 1860 Munchen", "R<PERSON> vs 1860 Munchen", "1860 Munchen vs VfB Stuttgart B", "1860 Munchen vs FC Ingolstadt", "R<PERSON> vs Alemannia A.", "Alemannia A. vs FC Ingolstadt", "Alemannia A. vs VfB Stuttgart B", "Alemannia A. vs RW <PERSON>", "FC Ingolstadt vs Alemannia A.", "R<PERSON> vs Bielefeld", "VfB Stuttgart B vs Bielefeld", "Bielefeld vs FC Ingolstadt", "Bielefeld vs RW Essen", "Bielefeld vs VfB Stuttgart B", "FC Ingolstadt vs Bielefeld", "RW E<PERSON> vs Dortmund B", "FC Ingolstadt vs Dortmund B", "VfB Stuttgart B vs Dortmund B", "Dortmund B vs RW Essen", "Dortmund B vs FC Ingolstadt", "Dynamo Dresden vs VfB Stuttgart B", "Dynamo Dresden vs RW Essen", "FC Ingolstadt vs Dynamo Dresden", "VfB Stuttgart B vs Dynamo Dresden", "RW Essen vs Dynamo Dresden", "Dynamo Dresden vs FC Ingolstadt", "Energie Cottbus vs VfB Stuttgart B", "R<PERSON> vs Energie Cottbus", "FC Ingolstadt vs Energie Cottbus", "VfB Stuttgart B vs Energie Cottbus", "Energie Cottbus vs RW Essen", "VfB Stuttgart B vs Erzgebirge Aue", "Erzgebirge Aue vs RW Essen", "FC Ingolstadt vs Erzgebirge Aue", "Erzgebirge Aue vs VfB Stuttgart B", "RW E<PERSON> vs Erzgebirge Aue", "Hannover 96 B vs RW Essen", "Hannover 96 B vs VfB Stuttgart B", "Hannover 96 B vs FC Ingolstadt", "RW <PERSON> vs Hannover 96 B", "VfB Stuttgart B vs Hannover 96 B", "FC Ingolstadt vs Hannover 96 B", "Hansa Rostock vs VfB Stuttgart B", "FC Ingolstadt vs Hansa Rostock", "Hans<PERSON> Rostock vs RW Essen", "VfB Stuttgart B vs Hansa Rostock", "Hansa Rostock vs FC Ingolstadt", "<PERSON><PERSON> vs Hansa Rostock", "FC Ingolstadt vs Mannheim", "Mannheim vs RW Essen", "VfB Stuttgart B vs Mannheim", "Mannheim vs FC Ingolstadt", "R<PERSON> vs Mannheim", "Mannheim vs VfB Stuttgart B", "Osnabruck vs VfB Stuttgart B", "FC Ingolstadt vs Osnabruck", "Osnabruck vs RW Essen", "VfB Stuttgart B vs Osnabruck", "Osnabruck vs FC Ingolstadt", "Saarbrucken vs FC Ingolstadt", "VfB Stuttgart B vs Saarbrucken", "Saarbrucken vs RW Essen", "FC Ingolstadt vs Saarbrucken", "Saarbrucken vs VfB Stuttgart B", "R<PERSON> vs Saarbrucken", "Sandhausen vs FC Ingolstadt", "Sandhausen vs VfB Stuttgart B", "RW Essen vs Sandhausen", "FC Ingolstadt vs Sandhausen", "VfB Stuttgart B vs Sandhausen", "Sandhausen vs RW Essen", "Unterhaching vs FC Ingolstadt", "<PERSON><PERSON><PERSON><PERSON> vs RW Essen", "VfB Stuttgart B vs Unterhaching", "FC Ingolstadt vs Unterhaching", "RW <PERSON> vs Unterhaching", "Unterhaching vs VfB Stuttgart B", "FC Ingolstadt vs Verl", "<PERSON><PERSON> vs Verl", "Verl vs VfB Stuttgart B", "Verl vs FC Ingolstadt", "<PERSON><PERSON><PERSON> vs RW <PERSON>ssen", "VfB Stuttgart B vs Verl", "<PERSON><PERSON> vs Viktoria <PERSON>", "Viktoria Koln vs FC Ingolstadt", "Viktoria Koln vs VfB Stuttgart B", "<PERSON><PERSON> vs RW Essen", "FC Ingolstadt vs Viktoria Koln", "VfB Stuttgart B vs Viktoria Koln", "VfB Stuttgart B vs Wehen Wiesbaden", "<PERSON><PERSON> vs <PERSON>hen Wiesbaden", "Wehen Wiesbaden vs FC Ingolstadt", "<PERSON>hen Wiesbaden vs VfB Stuttgart B", "<PERSON><PERSON> vs R<PERSON>"], "teams_affected": ["Osnabruck", "Energie Cottbus", "R<PERSON>", "Sandhausen", "Alemannia A.", "1860 Munchen", "Hannover 96 B", "Dortmund B", "<PERSON><PERSON><PERSON>", "Erzgebirge Aue", "Saarbrucken", "FC Ingolstadt", "Bielefeld", "VfB Stuttgart B", "<PERSON><PERSON>", "Dynamo Dresden", "Hansa Rostock", "Mannheim", "<PERSON>hen Wiesbaden", "Unterhaching"], "total_matches": 593, "valid_matches": 498, "skipped_matches": 95}, "MALAYSIA_SUPER_LEAGUE": {"warning_count": 24, "missing_matches": ["PDRM vs <PERSON><PERSON>", "<PERSON><PERSON> vs PDRM", "PDRM vs Kedah", "Kedah vs PDRM", "Kelantan United vs PDRM", "PDRM vs Kelantan United", "PDRM vs Kuala Lumpur", "Kuala Lumpur vs PDRM", "<PERSON><PERSON> vs PDRM", "PDRM vs Kuching", "<PERSON><PERSON><PERSON> vs PDRM", "PDRM vs Negeri Sembilan", "<PERSON><PERSON> vs PDRM", "PDRM vs Pahang", "Perak vs PDRM", "PDRM vs Perak", "PDRM vs <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> vs PDRM", "PDRM vs Sabah", "Sabah vs PDRM", "PDRM vs Selangor", "Selangor vs PDRM", "Terengganu vs PDRM", "PDRM vs Terengganu"], "teams_affected": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Terengganu", "Kelantan United", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Kuala Lumpur", "<PERSON><PERSON>", "PDRM", "Selangor", "Sabah", "Kedah"], "total_matches": 288, "valid_matches": 264, "skipped_matches": 24}, "FAROE_ISLANDS_PREMIER_LEAGUE": {"warning_count": 3, "missing_matches": ["07 Vestur vs HB Torshavn", "TB Tvoroyri vs B68 Toftir", "Su<PERSON>roy vs Klaksvik"], "teams_affected": ["B68 Toftir", "TB Tvoroyri", "07 Vestur", "HB Torshavn", "<PERSON><PERSON><PERSON>", "Klaksvik"], "total_matches": 6, "valid_matches": 3, "skipped_matches": 3}, "ECUADOR_SERIE_A": {"warning_count": 2, "missing_matches": ["Macara vs Barcelona SC", "Mu<PERSON><PERSON> vs LDU Quito"], "teams_affected": ["<PERSON><PERSON><PERSON>", "LDU Quito", "Barcelona SC", "<PERSON><PERSON>"], "total_matches": 9, "valid_matches": 7, "skipped_matches": 2}, "BELARUS_FIRST_LEAGUE": {"warning_count": 1, "missing_matches": ["<PERSON><PERSON><PERSON>vichy vs Bumprom"], "teams_affected": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "total_matches": 13, "valid_matches": 12, "skipped_matches": 1}, "INDIA_SUPER_LEAGUE": {"warning_count": 44, "missing_matches": ["Bengaluru vs NorthEast Utd", "Odisha FC vs Bengaluru", "Bengaluru vs Odisha FC", "NorthEast Utd vs Bengaluru", "Odisha FC vs Chennaiyin", "NorthEast Utd vs Chennaiyin", "Chennaiyin vs Odisha FC", "Chennaiyin vs NorthEast Utd", "Odisha FC vs East Bengal", "East Bengal vs NorthEast Utd", "East Bengal vs Odisha FC", "NorthEast Utd vs East Bengal", "Goa vs NorthEast Utd", "Odisha FC vs Goa", "NorthEast Utd vs Goa", "Goa vs Odisha FC", "Hyderabad vs Odisha FC", "Hyderabad vs NorthEast Utd", "NorthEast Utd vs Hyderabad", "Odisha FC vs Hyderabad", "Odisha FC vs Jamshedpur", "NorthEast Utd vs Jamshedpur", "Jamshedpur vs NorthEast Utd", "Jamshedpur vs Odisha FC", "NorthEast Utd vs Kerala Blasters", "Odisha FC vs Kerala Blasters", "Kerala Blasters vs Odisha FC", "Kerala Blasters vs NorthEast Utd", "Mohammedan vs NorthEast Utd", "Mohammedan vs Odisha FC", "NorthEast Utd vs Mohammedan", "Odisha FC vs Mohammedan", "<PERSON><PERSON> vs NorthEast Utd", "Odisha FC vs Mohun Bagan", "NorthEast Utd vs Mohun Bagan", "Mohun Bagan vs Odisha FC", "Mumbai City vs Odisha FC", "Odisha FC vs Mumbai City", "Mumbai City vs NorthEast Utd", "NorthEast Utd vs Mumbai City", "Punjab vs Odisha FC", "Punjab vs NorthEast Utd", "NorthEast Utd vs Punjab", "Odisha FC vs Punjab"], "teams_affected": ["<PERSON><PERSON>", "Goa", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "East Bengal", "Mumbai City", "Bengaluru", "NorthEast Utd", "Jamshedpur", "Punjab", "Kerala Blasters", "Odisha FC", "Hyderabad"], "total_matches": 264, "valid_matches": 220, "skipped_matches": 44}, "SCOTLAND_PREMIERSHIP": {"warning_count": 36, "missing_matches": ["Dundee FC vs Aberdeen", "Aberdeen vs Dundee FC", "Dundee FC vs Aberdeen", "Celtic vs Dundee FC", "Dundee FC vs Celtic", "Celtic vs Dundee FC", "Dundee Utd vs Dundee FC", "Dundee FC vs Dundee Utd", "Dundee Utd vs Dundee FC", "Dundee FC vs Hearts", "Hearts vs Dundee FC", "Dundee FC vs Hearts", "Hearts vs Dundee FC", "Hibernian vs Dundee FC", "Dundee FC vs Hibernian", "Hibernian vs Dundee FC", "Dundee FC vs Kilmarnock", "Dundee FC vs Kilmarnock", "Kilmarnock vs Dundee FC", "Kilmarnock vs Dundee FC", "Motherwell vs Dundee FC", "Dundee FC vs Motherwell", "Motherwell vs Dundee FC", "Dundee FC vs Motherwell", "Rangers vs Dundee FC", "Dundee FC vs Rangers", "Dundee FC vs Rangers", "Ross County vs Dundee FC", "Dundee FC vs Ross County", "Ross County vs Dundee FC", "Dundee FC vs St. Johnstone", "St. Johnstone vs Dundee FC", "Dundee FC vs St. Johnstone", "Dundee FC vs St. Mirren", "St. Mirren vs Dundee FC", "Dundee FC vs St. Mirren"], "teams_affected": ["Dundee FC", "Hibernian", "St<PERSON> Johnstone", "St. Mirren", "Celtic", "<PERSON><PERSON><PERSON><PERSON>", "Rangers", "Ross County", "Dundee Utd", "<PERSON><PERSON>", "Aberdeen", "Hearts"], "total_matches": 396, "valid_matches": 360, "skipped_matches": 36}, "HONG_KONG_PREMIER_LEAGUE": {"warning_count": 126, "missing_matches": ["Eastern vs HK Rangers", "LM Warriors vs Eastern", "Eastern vs Hong Kong FC", "Hong Kong FC vs Eastern", "Eastern vs LM Warriors", "HK Rangers vs Eastern", "Eastern vs Hong Kong FC", "LM Warriors vs Eastern", "HK Rangers vs Eastern", "Eastern vs HK Rangers", "Hong Kong FC vs HK Rangers", "Kitchee vs HK Rangers", "LM Warriors vs HK Rangers", "Kowloon City vs HK Rangers", "HK Rangers vs Southern D.", "Tai Po vs HK Rangers", "North District vs HK Rangers", "HK Rangers vs Tai Po", "North District vs HK Rangers", "HK Rangers vs LM Warriors", "HK Rangers vs Hong Kong FC", "HK Rangers vs Kitchee", "HK Rangers vs Kowloon City", "HK Rangers vs North District", "HK Rangers vs Eastern", "Southern D. vs HK Rangers", "Hong Kong FC vs HK Rangers", "Southern D. vs HK Rangers", "Kowloon City vs HK Rangers", "HK Rangers vs Kitchee", "HK Rangers vs Tai Po", "HK Rangers vs Eastern", "HK Rangers vs LM Warriors", "Hong Kong FC vs HK Rangers", "LM Warriors vs Hong Kong FC", "Hong Kong FC vs Kowloon City", "Kitchee vs Hong Kong FC", "Eastern vs Hong Kong FC", "Hong Kong FC vs North District", "Hong Kong FC vs Southern D.", "Tai Po vs Hong Kong FC", "Hong Kong FC vs Southern D.", "Hong Kong FC vs Tai Po", "Hong Kong FC vs Eastern", "Hong Kong FC vs Kowloon City", "HK Rangers vs Hong Kong FC", "Hong Kong FC vs Tai Po", "Hong Kong FC vs Kitchee", "LM Warriors vs Hong Kong FC", "North District vs Hong Kong FC", "Eastern vs Hong Kong FC", "Hong Kong FC vs HK Rangers", "North District vs Hong Kong FC", "Kitchee vs Hong Kong FC", "Hong Kong FC vs LM Warriors", "Southern D. vs Hong Kong FC", "Kowloon City vs Hong Kong FC", "Kitchee vs HK Rangers", "Kitchee vs Hong Kong FC", "Kitchee vs LM Warriors", "HK Rangers vs Kitchee", "Hong Kong FC vs Kitchee", "Kitchee vs LM Warriors", "LM Warriors vs Kitchee", "Kitchee vs Hong Kong FC", "HK Rangers vs Kitchee", "Hong Kong FC vs Kowloon City", "Kowloon City vs HK Rangers", "LM Warriors vs Kowloon City", "Hong Kong FC vs Kowloon City", "LM Warriors vs Kowloon City", "HK Rangers vs Kowloon City", "Kowloon City vs HK Rangers", "Kowloon City vs LM Warriors", "Kowloon City vs Hong Kong FC", "Southern D. vs LM Warriors", "Tai Po vs LM Warriors", "LM Warriors vs Hong Kong FC", "LM Warriors vs HK Rangers", "LM Warriors vs Eastern", "LM Warriors vs North District", "Kitchee vs LM Warriors", "LM Warriors vs Kowloon City", "Tai Po vs LM Warriors", "HK Rangers vs LM Warriors", "LM Warriors vs North District", "LM Warriors vs Kowloon City", "Eastern vs LM Warriors", "LM Warriors vs Hong Kong FC", "LM Warriors vs Southern D.", "Kitchee vs LM Warriors", "LM Warriors vs Tai Po", "Southern D. vs LM Warriors", "LM Warriors vs Kitchee", "LM Warriors vs Eastern", "Hong Kong FC vs LM Warriors", "Kowloon City vs LM Warriors", "North District vs LM Warriors", "HK Rangers vs LM Warriors", "LM Warriors vs North District", "Hong Kong FC vs North District", "North District vs HK Rangers", "North District vs HK Rangers", "LM Warriors vs North District", "HK Rangers vs North District", "North District vs Hong Kong FC", "North District vs Hong Kong FC", "North District vs LM Warriors", "Southern D. vs LM Warriors", "HK Rangers vs Southern D.", "Hong Kong FC vs Southern D.", "Hong Kong FC vs Southern D.", "LM Warriors vs Southern D.", "Southern D. vs HK Rangers", "Southern D. vs LM Warriors", "Southern D. vs HK Rangers", "Southern D. vs Hong Kong FC", "Tai Po vs LM Warriors", "Tai Po vs HK Rangers", "Tai Po vs Hong Kong FC", "HK Rangers vs Tai Po", "Hong Kong FC vs Tai Po", "Tai Po vs LM Warriors", "Hong Kong FC vs Tai Po", "LM Warriors vs Tai Po", "HK Rangers vs Tai Po"], "teams_affected": ["Kowloon City", "Hong Kong FC", "Tai Po", "HK Rangers", "Southern D.", "North District", "Kit<PERSON><PERSON>", "Eastern", "LM Warriors"], "total_matches": 216, "valid_matches": 90, "skipped_matches": 126}, "JAPAN_J2_LEAGUE": {"warning_count": 8, "missing_matches": ["Iwaki vs JEF Utd Chiba", "<PERSON><PERSON> vs C. Sa<PERSON>oro", "<PERSON>ita <PERSON> vs Fujieda MYFC", "Ehime FC vs Oita Trinita", "Omiya Ardija vs <PERSON>ita <PERSON>", "JEF Utd Chiba vs Oita Tri<PERSON>ta", "<PERSON><PERSON><PERSON> Iwata vs <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>"], "teams_affected": ["<PERSON><PERSON>", "V-<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "C. <PERSON>", "JEF Utd Chiba", "Omiya Ardija", "<PERSON><PERSON><PERSON>", "Fujieda MYFC", "Ehime FC", "<PERSON><PERSON>"], "total_matches": 15, "valid_matches": 7, "skipped_matches": 8}, "BRAZIL_SERIE_B": {"warning_count": 1, "missing_matches": ["<PERSON><PERSON><PERSON> vs Ava<PERSON>"], "teams_affected": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "total_matches": 5, "valid_matches": 4, "skipped_matches": 1}, "AUSTRIA_2_LIGA": {"warning_count": 26, "missing_matches": ["SV Horn vs <PERSON><PERSON>", "<PERSON><PERSON> vs SV Horn", "SV Horn vs Admira Wacker", "SV Horn vs Amstetten", "Amstetten vs SV Horn", "Bregenz vs SV Horn", "SV Horn vs Bregenz", "SV Horn vs First Vienna", "First Vienna vs SV Horn", "SV Horn vs Floridsdorfer", "Kapfenberger vs SV Horn", "Lafnitz vs SV Horn", "Liefering vs SV Horn", "SV Horn vs Liefering", "SV Horn vs Rapid Wien B", "Rapid Wien B vs SV Horn", "<PERSON><PERSON> vs SV Horn", "SV Horn vs Ried", "SV Horn vs St. Polten", "St<PERSON> Polten vs SV Horn", "Stripfing vs SV Horn", "SV Horn vs Stripfing", "SV Horn vs Sturm Graz B", "Sturm Graz B vs SV Horn", "Voitsberg vs SV Horn", "SV Horn vs Voitsberg"], "teams_affected": ["Rapid Wien B", "Voitsberg", "Bregenz", "Amstetten", "<PERSON><PERSON><PERSON>", "Floridsdorfer", "Liefering", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Stripfing", "St. <PERSON>", "Sturm Graz B", "<PERSON><PERSON><PERSON><PERSON>", "SV Horn", "<PERSON><PERSON>", "First Vienna"], "total_matches": 386, "valid_matches": 360, "skipped_matches": 26}, "TURKEY_3_LIG_GROUP_3": {"warning_count": 96, "missing_matches": ["Alanya 1221 vs 1923 M.", "Alanya 1221 vs 1922 Konyaspor", "Aliaga FAS vs Alanya 1221", "Alanya 1221 vs Cankaya FK", "1923 <PERSON><PERSON> vs Alanya 1221", "Alanya 1221 vs Aliaga FAS", "Cankaya FK vs Alanya 1221", "1922 Konyaspor vs Alanya 1221", "Ayvalikgucu vs Aliaga FAS", "Cankaya FK vs Ayvalikgucu", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs 1923 M.", "1922 Konyaspor vs Ayvalikgucu", "Aliaga FAS vs Ayvalikgucu", "Ayvalikgucu vs Cankaya FK", "1923 <PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ik<PERSON><PERSON> vs 1922 Konyaspor", "<PERSON><PERSON><PERSON> vs 1923 M.", "Bayburt vs 1922 Konyaspor", "Aliaga FAS vs Bayburt", "Bayburt vs Cankaya FK", "1922 Konyaspor vs Bayburt", "1923 <PERSON><PERSON> vs Bayburt", "Bayburt vs Aliaga FAS", "Cankaya FK vs Bayburt", "1923 M. vs Corluspor", "1922 Konyaspor vs Corluspor", "Corluspor vs Aliaga FAS", "Cankaya FK vs Corluspor", "Corluspor vs 1923 M.", "Corluspor vs 1922 Konyaspor", "Aliaga FAS vs Corluspor", "Corluspor vs Cankaya FK", "Efeler 09 vs Aliaga FAS", "Cankaya FK vs Efeler 09", "Efeler 09 vs 1923 M.", "1922 Konyaspor vs Efeler 09", "Aliaga FAS vs Efeler 09", "Efeler 09 vs Cankaya FK", "1923 M. vs Efeler 09", "Efeler 09 vs 1922 Konyaspor", "1922 Konyaspor vs <PERSON><PERSON>", "Aliaga FAS vs K. <PERSON>", "<PERSON><PERSON> vs 1923 M.", "Cankaya FK vs K. Idmanyurdu", "<PERSON><PERSON> vs 1922 Konyaspor", "<PERSON><PERSON> vs Aliaga FAS", "1923 <PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs Cankaya FK", "Aliaga FAS vs Kucukcekmece", "Kucukcekmece vs Cankaya FK", "1923 M. vs Kucukcekmece", "Kucukcekmece vs 1922 Konyaspor", "Kucukcekmece vs Aliaga FAS", "Cankaya FK vs Kucukcekmece", "Kucukcekmece vs 1923 M.", "1922 Konyaspor vs Kucukcekmece", "Orduspor vs 1923 M.", "Orduspor vs 1922 Konyaspor", "Aliaga FAS vs Orduspor", "Orduspor vs Cankaya FK", "1923 M. vs Orduspor", "1922 Konyaspor vs Orduspor", "Orduspor vs Aliaga FAS", "Cankaya FK vs Orduspor", "1923 M. vs Osmaniyespor", "1922 Konyaspor vs Osmaniyespor", "Osmaniyespor vs Aliaga FAS", "Cankaya FK vs Osmaniyespor", "Osmaniyespor vs 1923 M.", "Osmaniyespor vs 1922 Konyaspor", "Aliaga FAS vs Osmaniyespor", "Osmaniyespor vs Cankaya FK", "1922 Konyaspor vs Pazarspor", "Aliaga FAS vs Pazarspor", "Pazarspor vs Cankaya FK", "1923 M. vs Pazarspor", "Pazarspor vs 1922 Konyaspor", "Cankaya FK vs Pazarspor", "Pazarspor vs Aliaga FAS", "Pazarspor vs 1923 M.", "Viransehir vs 1922 Konyaspor", "Viransehir vs Aliaga FAS", "Cankaya FK vs Viransehir", "Viransehir vs 1923 M.", "1922 Konyaspor vs Viransehir", "Aliaga FAS vs Viransehir", "Viransehir vs Cankaya FK", "1923 M. vs Viransehir", "1923 M. vs Yozgat Bld Bozo", "1922 Konyaspor vs Yozgat Bld Bozo", "Yozgat Bld Bozo vs Aliaga FAS", "Cankaya FK vs Yozgat Bld Bozo", "Yozgat Bld Bozo vs 1923 M.", "Yozgat Bld Bozo vs 1922 Konyaspor", "Aliaga FAS vs Yozgat Bld Bozo", "Yozgat Bld Bozo vs Cankaya FK"], "teams_affected": ["Corluspor", "<PERSON><PERSON>", "Orduspor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Pazarspor", "Efeler 09", "Kucukcekmece", "Osmaniyespor", "1922 Konyaspor", "1923 M.", "Aliaga FAS", "Yozgat Bld Bozo", "Viransehir", "Alanya 1221", "Bayburt", "Cankaya FK"], "total_matches": 360, "valid_matches": 264, "skipped_matches": 96}, "FRANCE_NATIONAL_2_GROUP_A": {"warning_count": 71, "missing_matches": ["GFA Rumilly vs Andrezieux", "Saint-<PERSON> vs Andrezieux", "Frejus SR vs Andrezieux", "Andrezieux vs GFA Rumilly", "Andrezieux vs Saint-Priest", "Andrezieux vs Frejus SR", "Frejus SR vs Anglet Genets", "Anglet Genets vs GFA Rumilly", "Anglet Genets vs Saint-Priest", "Anglet Genets vs Frejus SR", "GFA Rumilly vs Anglet Genets", "Saint<PERSON><PERSON> vs Anglet Genets", "Frejus SR vs Angouleme", "Angouleme vs GFA Rumilly", "Angouleme vs Saint-Priest", "Angouleme vs Frejus SR", "GFA Rumilly vs Angouleme", "<PERSON><PERSON> vs Saint-Priest", "<PERSON><PERSON> vs Frejus SR", "Bergerac vs GFA Rumilly", "Saint<PERSON><PERSON> vs Bergerac", "Frejus SR vs Bergerac", "Cannes vs GFA Rumilly", "Cannes vs Saint-Priest", "Cannes vs Frejus SR", "GFA Rumilly vs Cannes", "Saint-Priest vs Cannes", "Frejus SR vs Cannes", "G. O. A. L. vs Saint-Priest", "G. O. A. L. vs Frejus SR", "G. O. A. L. vs GFA Rumilly", "Frejus SR vs G. O. A. L.", "Saint-<PERSON> vs G. O. A. L.", "GFA Rumilly vs G. O. A. L.", "GFA Rumilly vs Grasse", "Grasse vs Frejus SR", "Saint<PERSON><PERSON> vs Grasse", "Grasse vs GFA Rumilly", "Frejus SR vs Grasse", "Grasse vs Saint-Priest", "<PERSON><PERSON><PERSON> vs Frejus SR", "GFA Rumilly vs Hyeres", "Saint<PERSON><PERSON> vs Hyer<PERSON>", "Frejus SR vs Hyeres", "Hyeres vs GFA Rumilly", "Saint-<PERSON> vs Istres", "Frejus SR vs Istres", "GFA Rumilly vs Istres", "Istres vs Saint-Priest", "Istres vs Frejus SR", "GFA Rumilly vs Jura Sud Foot", "Jura Sud Foot vs Saint-Priest", "Jura Sud Foot vs Frejus SR", "Jura Sud Foot vs GFA Rumilly", "<PERSON><PERSON><PERSON> vs Jura Sud Foot", "Le Puy vs GFA Rumilly", "<PERSON><PERSON><PERSON> vs <PERSON> Puy", "Frejus SR vs Le Puy", "GFA Rumilly vs Le Puy", "<PERSON> P<PERSON> vs Saint-Priest", "<PERSON> vs Frejus SR", "Marignane G. vs GFA Rumilly", "Saint-<PERSON> vs Marignane G.", "Frejus SR vs Marignane G.", "GFA Rumilly vs Marignane G.", "<PERSON><PERSON><PERSON> G. vs Saint-<PERSON>", "GFA Rumilly vs Toulon", "Saint-<PERSON> vs Toulon", "Frejus SR vs Toulon", "Toulon vs Saint-Priest", "Toulon vs Frejus SR"], "teams_affected": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "GFA Rumilly", "G. O. A. L.", "Istres", "Marignane G.", "Jura Sud Foot", "<PERSON><PERSON><PERSON>", "Anglet Genets", "Cannes", "Grasse", "Toulon", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "total_matches": 351, "valid_matches": 280, "skipped_matches": 71}, "TURKEY_1_LIG": {"warning_count": 102, "missing_matches": ["Manisa BB vs Adanaspor", "Erzurum BB vs Adanaspor", "Adanaspor vs Amed SK", "Adanaspor vs Manisa BB", "Adanaspor vs Erzurum BB", "Amed SK vs Adanaspor", "Erzurum BB vs Ankaragucu", "<PERSON>gu<PERSON> vs Manisa BB", "Amed SK vs Ankaragucu", "<PERSON><PERSON><PERSON> vs Erzurum BB", "Manisa BB vs Ankaragucu", "<PERSON>gu<PERSON> vs Amed SK", "Bandirmaspor vs Erzurum BB", "Bandirmaspor vs Amed SK", "Bandirmaspor vs Manisa BB", "Erzurum BB vs Bandirmaspor", "Amed SK vs Bandirmaspor", "Manisa BB vs Bandirmaspor", "Amed SK vs Boluspor", "Boluspor vs Manisa BB", "Erzurum BB vs Boluspor", "Boluspor vs Amed SK", "Manisa BB vs Boluspor", "Boluspor vs Erzurum BB", "Corum vs Erzurum BB", "Manisa BB vs Corum", "Corum vs Amed SK", "Erzurum BB vs Corum", "Corum vs Manisa BB", "Amed SK vs Corum", "Erzurum BB vs Erokspor", "Amed SK vs Erokspor", "Manisa BB vs Erokspor", "Erokspor vs Erzurum BB", "Erokspor vs Amed SK", "Erokspor vs Manisa BB", "<PERSON><PERSON> vs Amed SK", "<PERSON><PERSON> vs Manisa BB", "Erzurum BB vs <PERSON><PERSON>", "<PERSON>ed SK vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs Erzurum BB", "Genclerbirligi vs Erzurum BB", "Manisa BB vs Genclerbirligi", "Genclerb<PERSON>ligi vs Amed SK", "Erzurum BB vs Genclerbirligi", "Genclerbirligi vs Manisa BB", "Amed SK vs Genclerbirligi", "<PERSON><PERSON><PERSON> vs Erzurum BB", "<PERSON><PERSON><PERSON> vs Manisa BB", "<PERSON><PERSON><PERSON> vs Amed SK", "Erzurum BB vs Igdir", "Manisa BB vs Igdir", "Amed SK vs Igdir", "Istanbulspor vs Manisa BB", "Amed SK vs Istanbulspor", "Istanbulspor vs Erzurum BB", "Manisa BB vs Istanbulspor", "Istanbulspor vs Amed SK", "Erzurum BB vs Istanbulspor", "Erzurum BB vs Ke<PERSON>ren<PERSON><PERSON>", "Amed SK vs Keciorengucu", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs Manisa BB", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs Erzurum BB", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs Amed SK", "Manisa BB vs <PERSON><PERSON>ren<PERSON><PERSON>", "Kocaelispor vs Erzurum BB", "Amed SK vs Kocaelispor", "Kocaelispor vs Manisa BB", "Erzurum BB vs Kocaelispor", "Kocaelispor vs Amed SK", "Manisa BB vs Kocaelispor", "Pendikspor vs Erzurum BB", "Amed SK vs Pendikspor", "Pendikspor vs Manisa BB", "Erzurum BB vs Pendikspor", "Pendikspor vs Amed SK", "Manisa BB vs Pendikspor", "Manisa BB vs Sakaryaspor", "Sakaryaspor vs Amed SK", "Erzurum BB vs Sakaryaspor", "Sakaryaspor vs Manisa BB", "Amed SK vs Sakaryaspor", "Sakaryaspor vs Erzurum BB", "Amed SK vs Sanliurfaspor", "Manisa BB vs Sanliurfaspor", "Erzurum BB vs Sanliurfaspor", "Sanliurfaspor vs Amed SK", "Sanliurfaspor vs Manisa BB", "Sanliurfaspor vs Erzurum BB", "Amed SK vs Umraniyespor", "Umraniyespor vs Erzurum BB", "Manisa BB vs Umraniyespor", "Umraniyespor vs Amed SK", "Erzurum BB vs Umraniyespor", "Umraniyespor vs Manisa BB", "Erzurum BB vs Y. Malatyaspor", "Manisa BB vs Y. Malatyaspor", "Y. Malatyaspor vs Amed SK", "Y. Malatyaspor vs Erzurum BB", "Y. Malatyaspor vs Manisa BB", "Amed SK vs Y. Malatyaspor"], "teams_affected": ["Erzurum BB", "Kocaelispor", "Genclerbirligi", "Boluspor", "Y. Malatyaspor", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Sakaryaspor", "Istanbulspor", "Adanaspor", "Manisa BB", "Pendikspor", "Erokspor", "Sanliurfaspor", "Umraniyespor", "Amed SK", "<PERSON><PERSON>", "Bandirmaspor", "<PERSON><PERSON><PERSON>", "Corum", "<PERSON><PERSON><PERSON>"], "total_matches": 644, "valid_matches": 542, "skipped_matches": 102}, "GERMANY_OBERLIGA_BREMEN": {"warning_count": 96, "missing_matches": ["<PERSON><PERSON><PERSON><PERSON> vs OSC Bremerhaven", "Blumenthaler vs TuRa Bremen", "<PERSON><PERSON><PERSON><PERSON> vs Aumund-Vegesack", "<PERSON><PERSON><PERSON><PERSON><PERSON>diek vs Blument<PERSON><PERSON>", "Blumenthaler vs BTS Neustadt", "OSC Bremerhaven vs Blumenthaler", "TuRa Bremen vs Blumenthaler", "<PERSON><PERSON><PERSON><PERSON> vs Blumenthaler", "Brinkumer vs BTS Neustadt", "<PERSON><PERSON><PERSON><PERSON> vs OSC Bremerhaven", "Brinkumer vs TuRa Bremen", "<PERSON><PERSON><PERSON><PERSON> vs Aumund-Vegesack", "<PERSON><PERSON>r<PERSON>Blockdiek vs Brinkumer", "BTS Neustadt vs Brinkumer", "OSC Bremerhaven vs Brinkumer", "TuRa Bremen vs Brinkumer", "<PERSON><PERSON> vs Aumund-Vegesack", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs BTS Neustadt", "<PERSON><PERSON> vs OSC Bremerhaven", "TuRa Bremen vs Eiche Horn", "<PERSON><PERSON> vs Vahr-Blockdiek", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "OSC Bremerhaven vs Eiche Horn", "<PERSON><PERSON> vs TuRa Bremen", "Gee<PERSON><PERSON>e vs Aumund-Vegesack", "<PERSON><PERSON>r<PERSON>Blockdiek vs Geestemunde", "Geestemunde vs BTS Neustadt", "Geestemunde vs OSC Bremerhaven", "TuRa Bremen vs Geestemunde", "<PERSON><PERSON><PERSON><PERSON> vs Geestemunde", "BTS Neustadt vs Geestemunde", "<PERSON><PERSON><PERSON><PERSON> vs Vahr-Blockdiek", "OSC Bremerhaven vs Geestemunde", "TuRa Bremen vs Habenhauser", "<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> vs Vahr-Blockdiek", "BTS Neustadt vs Habenhauser", "OSC Bremerhaven vs Habenhauser", "Habenhauser vs TuRa Bremen", "<PERSON><PERSON><PERSON><PERSON> vs Aumund-Vegesack", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON>", "Habenhauser vs BTS Neustadt", "Aumund-Vegesack vs Hemelingen", "Hemelingen vs Vahr-Blockdiek", "BTS Neustadt vs Hemelingen", "OSC Bremerhaven vs Hemelingen", "Hemelingen vs TuRa Bremen", "Hemelingen vs Aumund-Vegesack", "Vahr-Blockdiek vs Hemelingen", "Hemelingen vs BTS Neustadt", "Hemelingen vs OSC Bremerhaven", "<PERSON><PERSON><PERSON> vs Oberneuland", "Vahr<PERSON>Blockdiek vs Oberneuland", "BTS Neustadt vs Oberneuland", "Oberneuland vs TuRa Bremen", "OSC Bremerhaven vs Oberneuland", "Oberneuland vs Aumund-Vegesack", "Oberneuland vs Vahr-Blockdiek", "Oberneuland vs BTS Neustadt", "TuRa Bremen vs Oberneuland", "Oberneuland vs OSC Bremerhaven", "TuRa Bremen vs Union Bremen", "Union Bremen vs Aumund-Vegesack", "Union Bremen vs Vahr-Blockdiek", "BTS Neustadt vs Union Bremen", "OSC Bremerhaven vs Union Bremen", "Union Bremen vs TuRa Bremen", "Aumund-Vegesack vs Union Bremen", "Vahr-Blockdiek vs Union Bremen", "TuRa Bremen vs Vatan Sport", "Aumund-Vegesack vs Vatan Sport", "Vatan Sport vs Vahr-Blockdiek", "BTS Neustadt vs Vatan Sport", "Vatan Sport vs OSC Bremerhaven", "Vatan Sport vs TuRa Bremen", "Vatan Sport vs Aumund-Vegesack", "Vahr-Blockdiek vs Vatan Sport", "Vatan Sport vs BTS Neustadt", "OSC Bremerhaven vs Vatan Sport", "Werder Bremen C vs TuRa Bremen", "Werder Bremen C vs Aumund-Vegesack", "Vahr-Blockdiek vs Werder Bremen C", "Werder Bremen C vs BTS Neustadt", "Werder Bremen C vs OSC Bremerhaven", "TuRa Bremen vs Werder Bremen C", "Aumund-Vegesack vs Werder Bremen C", "Werder Bremen C vs Vahr-Blockdiek", "BTS Neustadt vs Werder Bremen C", "OSC Bremerhaven vs Woltmershausen", "TuRa Bremen vs Woltmershausen", "Aumund-Vegesack vs Woltmershausen", "Woltmershausen vs Vahr-Blockdiek", "BTS Neustadt vs Woltmershausen", "Woltmershausen vs OSC Bremerhaven", "Woltmershausen vs TuRa Bremen"], "teams_affected": ["Union Bremen", "Werder Bremen C", "<PERSON><PERSON><PERSON><PERSON>", "Oberneuland", "Woltmershausen", "TuRa Bremen", "Vahr-Blockdiek", "<PERSON><PERSON><PERSON><PERSON>", "Aumund-<PERSON>", "OSC Bremerhaven", "Hemelingen", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Vatan Sport", "<PERSON><PERSON><PERSON><PERSON>", "BTS Neustadt"], "total_matches": 296, "valid_matches": 200, "skipped_matches": 96}, "GERMANY_BUNDESLIGA_WOMEN": {"warning_count": 64, "missing_matches": ["Bayern Munich W vs RB Leipzig W", "Bayern Munich W vs FC Koln W", "Bayern Munich W vs CZ Jena W", "SGS Essen W vs Bayern Munich W", "RB Leipzig W vs Bayern Munich W", "FC Koln W vs Bayern Munich W", "CZ Jena W vs Bayern Munich W", "Bayern Munich W vs SGS Essen W", "E. Frankfurt W vs CZ Jena W", "SGS Essen W vs E. Frankfurt W", "E. Frankfurt W vs FC Koln W", "E. Frankfurt W vs RB Leipzig W", "CZ Jena W vs E. Frankfurt W", "<PERSON>. <PERSON> W vs SGS Essen W", "FC Koln W vs E. Frankfurt W", "RB Leipzig W vs E. Frankfurt W", "Freiburg W vs CZ Jena W", "FC Koln W vs Freiburg W", "Freiburg W vs RB Leipzig W", "Freiburg W vs SGS Essen W", "Freiburg W vs FC Koln W", "CZ Jena W vs Freiburg W", "RB Leipzig W vs Freiburg W", "SGS Essen W vs Freiburg W", "SGS Essen W vs Hoffenheim W", "FC Koln W vs Hoffenheim W", "RB Leipzig W vs Hoffenheim W", "Hoffenheim W vs SGS Essen W", "CZ Jena W vs Hoffenheim W", "Hoffenheim W vs FC Koln W", "Hoffenheim W vs RB Leipzig W", "Hoffenheim W vs CZ Jena W", "SGS Essen W vs Leverkusen W", "Leverkusen W vs CZ Jena W", "FC Koln W vs Leverkusen W", "RB Leipzig W vs Leverkusen W", "Leverkusen W vs SGS Essen W", "CZ Jena W vs Leverkusen W", "Leverkusen W vs FC Koln W", "Leverkusen W vs RB Leipzig W", "T. Potsdam W vs RB Leipzig W", "T. Potsdam W vs SGS Essen W", "T. Potsdam W vs CZ Jena W", "T. Potsdam W vs FC Koln W", "RB Leipzig W vs T. Potsdam W", "SGS Essen W vs T. Potsdam W", "CZ Jena W vs T. Potsdam W", "FC Koln W vs T. Potsdam W", "RB Leipzig W vs Werder Bremen W", "Werder Bremen W vs SGS Essen W", "CZ Jena W vs Werder Bremen W", "FC Koln W vs Werder Bremen W", "Werder Bremen W vs RB Leipzig W", "SGS Essen W vs Werder Bremen W", "Werder Bremen W vs CZ Jena W", "Werder Bremen W vs FC Koln W", "CZ Jena W vs Wolfsburg W", "Wolfsburg W vs FC Koln W", "Wolfsburg W vs RB Leipzig W", "SGS Essen W vs Wolfsburg W", "Wolfsburg W vs CZ Jena W", "FC Koln W vs Wolfsburg W", "RB Leipzig W vs Wolfsburg W", "Wolfsburg W vs SGS Essen W"], "teams_affected": ["RB Leipzig W", "CZ Jena W", "Freiburg W", "Leverkusen W", "Bayern Munich W", "SGS Essen W", "Werder Bremen W", "Wolfsburg W", "E. Frankfurt W", "T. Potsdam W", "Hoffenheim W", "FC Koln W"], "total_matches": 176, "valid_matches": 112, "skipped_matches": 64}, "URUGUAY_PRIMERA_DIVISION": {"warning_count": 7, "missing_matches": ["Boston River vs Montevideo City", "Boston River vs Racing CM", "Plaza Colonia vs Boston River", "Juventud vs Boston River", "<PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "Progreso vs Racing CM", "Plaza Colonia vs Wanderers"], "teams_affected": ["Wanderers", "Boston River", "Montevideo City", "Plaza Colonia", "Progreso", "Racing CM", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "total_matches": 25, "valid_matches": 18, "skipped_matches": 7}, "PANAMA_LIGA_PANAMENA_DE_FUTBOL": {"warning_count": 10, "missing_matches": ["Costa del Este vs Arabe Unido", "Arabe Unido vs Costa del Este", "UMECIT vs Herrera", "Herrera vs Costa del Este", "Herrera vs UMECIT", "Independiente vs UMECIT", "UMECIT vs Independiente", "UMECIT vs Veraguas", "Veraguas vs Costa del Este", "Veraguas vs UMECIT"], "teams_affected": ["UMECIT", "Costa del Este", "Independiente", "Arabe Unido", "<PERSON>", "Veraguas"], "total_matches": 45, "valid_matches": 35, "skipped_matches": 10}, "ESTONIA_MEISTRILIIGA": {"warning_count": 2, "missing_matches": ["Harju JK vs Narva Trans", "Nomme Kalju vs Harju JK"], "teams_affected": ["<PERSON><PERSON>ju <PERSON>", "Narva Trans", "Nomme Kalju"], "total_matches": 27, "valid_matches": 25, "skipped_matches": 2}, "GERMANY_OBERLIGA_HESSEN": {"warning_count": 127, "missing_matches": ["Hunfelder SV vs Bayern Alzenau", "Bayern Alzenau vs KSV Baunatal", "Bayern Alzenau vs RW Walldorf", "Bayern Alzenau vs Hanauer SC", "Bayern Alzenau vs SV Steinbach", "Bayern Alzenau vs Hunfelder SV", "KSV Baunatal vs Bayern Alzenau", "RW Walldorf vs Bayern Alzenau", "Darmstadt B vs Hunfelder SV", "KSV Baunatal vs Darmstadt B", "RW Walldorf vs Darmstadt B", "Hanauer SC vs Darmstadt B", "SV Steinbach vs Darmstadt B", "Darmstadt B vs KSV Baunatal", "Hunfelder SV vs Darmstadt B", "Darmstadt B vs RW Walldorf", "Darmstadt B vs Hanauer SC", "SV Steinbach vs Eddersheim", "Hunfelder SV vs Eddersheim", "Eddersheim vs KSV Baunatal", "RW Walldorf vs Eddersheim", "Eddersheim vs SV Steinbach", "Eddersheim vs Hanauer SC", "Eddersheim vs Hunfelder SV", "KSV Baunatal vs Eddersheim", "Hanauer SC vs Eddersheim", "<PERSON><PERSON><PERSON> vs Hanauer SC", "Fernwald vs SV Steinbach", "RW <PERSON> vs Fernwald", "Fernwald vs Hunfelder SV", "KSV Baunatal vs Fernwald", "Fernwald vs RW Walldorf", "Hanauer SC vs Fernwald", "SV Steinbach vs Fernwald", "Hunfelder SV vs Fernwald", "Fernwald vs KSV Baunatal", "Friedberg vs KSV Baunatal", "<PERSON><PERSON><PERSON> vs RW Walldorf", "Hunfelder SV vs Friedberg", "Friedberg vs Hanauer SC", "Friedberg vs SV Steinbach", "KSV Baunatal vs Friedberg", "Friedberg vs Hunfelder SV", "RW Walldorf vs Friedberg", "Hanauer SC vs Friedberg", "SV Steinbach vs Friedberg", "<PERSON><PERSON> vs Hanauer SC", "Hanau vs Hunfelder SV", "Hanauer SC vs Hanau", "Hanau vs KSV Baunatal", "Hanau vs SV Steinbach", "<PERSON><PERSON> vs R<PERSON>", "SV Steinbach vs Hanau", "Hunfelder SV vs Hanau", "KSV Baunatal vs Hanau", "R<PERSON> vs Hornau", "<PERSON><PERSON> vs Hanauer SC", "Hornau vs SV Steinbach", "Hornau vs Hunfelder SV", "KSV Baunatal vs Hornau", "<PERSON><PERSON> vs RW <PERSON>dorf", "Hanauer SC vs Hornau", "SV Steinbach vs Hornau", "Marburg vs Hunfelder SV", "KSV Baunatal vs Marburg", "RW Walldorf vs Marburg", "Hanauer SC vs Marburg", "Marburg vs SV Steinbach", "Hunfelder SV vs Marburg", "Marburg vs KSV Baunatal", "Marburg vs RW Walldorf", "Stadtallendorf vs RW Walldorf", "Hanauer SC vs Stadtallendorf", "SV Steinbach vs Stadtallendorf", "Hunfelder SV vs Stadtallendorf", "Stadtallendorf vs KSV Baunatal", "RW Walldorf vs Stadtallendorf", "Stadtallendorf vs Hanauer SC", "Stadtallendorf vs SV Steinbach", "Stadtallendorf vs Hunfelder SV", "RW Walldorf vs Steinbach H. B", "Hanauer SC vs Steinbach H. B", "KSV Baunatal vs Steinbach H. B", "SV Steinbach vs Steinbach H. B", "Steinbach H. B vs Hunfelder SV", "Steinbach H. B vs KSV Baunatal", "Hunfelder SV vs Steinbach H. B", "Steinbach H. B vs RW Walldorf", "Steinbach H. B vs Hanauer SC", "Steinbach H. B vs SV Steinbach", "U. Flockenbach vs Hunfelder SV", "U. Flockenbach vs KSV Baunatal", "U. Flockenbach vs Hanauer SC", "SV Steinbach vs U. Flockenbach", "<PERSON><PERSON> vs U. Flockenbach", "U<PERSON> Flockenbach vs RW Walldorf", "Hunfelder SV vs U. Flockenbach", "KSV Baunatal vs U. Flockenbach", "Hanauer SC vs U. Flockenbach", "U. Flockenbach vs SV Steinbach", "Hanauer SC vs Waldgirmes", "SV Steinbach vs Waldgirmes", "Hunfelder SV vs Waldgirmes", "Waldgirmes vs KSV Baunatal", "R<PERSON> vs Waldgirmes", "Waldgirmes vs SV Steinbach", "Waldgirmes vs Hanauer SC", "Waldgirmes vs Hunfelder SV", "KSV Baunatal vs Waldgirmes", "RW Walldorf vs Weidenhausen", "Weidenhausen vs KSV Baunatal", "Hanauer SC vs Weidenhausen", "Weidenhausen vs SV Steinbach", "KSV Baunatal vs Weidenhausen", "Weidenhausen vs Hunfelder SV", "Weidenhausen vs RW Walldorf", "Weidenhausen vs Hanauer SC", "SV Steinbach vs Weidenhausen", "Wolfhagen vs SV Steinbach", "Wolfhagen vs Hunfelder SV", "KSV Baunatal vs Wolfhagen", "<PERSON><PERSON><PERSON> vs Hanauer SC", "<PERSON><PERSON><PERSON> vs R<PERSON>", "SV Steinbach vs Wolfhagen", "<PERSON>auer SC vs Wolfhagen", "Hunfelder SV vs Wolfhagen", "Wolfhagen vs KSV Baunatal"], "teams_affected": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Steinbach H. B", "<PERSON>auer SC", "Darmstadt B", "Marburg", "Weidenhausen", "Eddersheim", "Hunfelder SV", "Stadtallendorf", "<PERSON><PERSON><PERSON>", "SV Steinbach", "Bayern Alzenau", "KSV Baunatal", "Fernwald", "<PERSON><PERSON>", "RW <PERSON>", "<PERSON><PERSON><PERSON>"], "total_matches": 457, "valid_matches": 330, "skipped_matches": 127}, "SOUTH_KOREA_WK_LEAGUE_WOMEN": {"warning_count": 1, "missing_matches": ["Incheon W vs Suwon FMC W"], "teams_affected": ["Suwon FMC W", "Incheon W"], "total_matches": 10, "valid_matches": 9, "skipped_matches": 1}, "ITALY_SERIE_C_GROUP_A": {"warning_count": 70, "missing_matches": ["FeralpiSalo vs Alcione", "Alcione vs AlbinoLeffe", "Alcione vs FeralpiSalo", "AlbinoLeffe vs Alcione", "Arzignano vs AlbinoLeffe", "FeralpiSalo vs Arzignano", "AlbinoLeffe vs Arzignano", "AlbinoLeffe vs Atalanta B", "FeralpiSalo vs Atalanta B", "Atalanta B vs AlbinoLeffe", "Atalanta B vs FeralpiSalo", "AlbinoLeffe vs Caldiero Terme", "FeralpiSalo vs Caldiero Terme", "Caldiero Terme vs AlbinoLeffe", "Caldiero Terme vs FeralpiSalo", "Clodiense vs FeralpiSalo", "AlbinoLeffe vs Clodiense", "FeralpiSalo vs Clodiense", "Clodiense vs AlbinoLeffe", "Giana E<PERSON>inio vs AlbinoLeffe", "FeralpiSalo vs Giana <PERSON>o", "AlbinoLeffe vs Giana Erminio", "Giana <PERSON> vs FeralpiSalo", "FeralpiSalo vs Lecco", "AlbinoLeffe vs Lecco", "Lecco vs FeralpiSalo", "Lecco vs AlbinoLeffe", "Lumezzane vs AlbinoLeffe", "Lumezzane vs FeralpiSalo", "AlbinoLeffe vs Lumezzane", "FeralpiSalo vs Lumezzane", "FeralpiSalo vs Novara", "Novara vs AlbinoLeffe", "Novara vs FeralpiSalo", "AlbinoLeffe vs Novara", "Padova vs FeralpiSalo", "AlbinoLeffe vs Padova", "FeralpiSalo vs Padova", "Padova vs AlbinoLeffe", "AlbinoLeffe vs Pergolettese", "Pergolettese vs FeralpiSalo", "Pergolettese vs AlbinoLeffe", "FeralpiSalo vs Pergolettese", "Pro Patria vs FeralpiSalo", "AlbinoLeffe vs Pro Patria", "FeralpiSalo vs Pro Patria", "Pro Patria vs AlbinoLeffe", "Pro Vercelli vs AlbinoLeffe", "Pro Vercelli vs FeralpiSalo", "AlbinoLeffe vs Pro Vercelli", "FeralpiSalo vs Pro Vercelli", "Renate vs FeralpiSalo", "Renate vs AlbinoLeffe", "Feralpi<PERSON>alo vs Renate", "AlbinoLeffe vs Renate", "Trento vs FeralpiSalo", "AlbinoLeffe vs Trento", "FeralpiSalo vs Trento", "Trento vs AlbinoLeffe", "Triestina vs AlbinoLeffe", "FeralpiSalo vs Triestina", "AlbinoLeffe vs Triestina", "Triestina vs FeralpiSalo", "AlbinoLeffe vs Vicenza", "Vicenza vs FeralpiSalo", "Vicenza vs AlbinoLeffe", "FeralpiSalo vs Vicenza", "FeralpiSalo vs Virtus Verona", "AlbinoLeffe vs Virtus Verona", "Virtus Verona vs FeralpiSalo"], "teams_affected": ["Lumezzane", "Alcione", "Pergolettese", "<PERSON><PERSON>", "<PERSON><PERSON>", "Trento", "Atalanta B", "Caldiero Terme", "Pro Patria", "Novara", "FeralpiSalo", "AlbinoLeffe", "Renate", "<PERSON><PERSON><PERSON>", "Pro Vercelli", "<PERSON><PERSON><PERSON><PERSON>", "Triestina", "Vicenza", "Clodiense", "Virtus Verona"], "total_matches": 666, "valid_matches": 596, "skipped_matches": 70}, "GERMANY_OBERLIGA_BAYERN_SUD": {"warning_count": 31, "missing_matches": ["1860 Munchen B vs Rain/Lech", "Rain/Lech vs Deisenhofen", "Deisenhofen vs Rain/Lech", "Rain/Lech vs Erlbach", "Erlbach vs Rain/Lech", "<PERSON><PERSON><PERSON> vs Rain/Lech", "Rain/Lech vs Grun<PERSON>", "Heimstetten vs Rain/Lech", "Rain/Lech vs Heimstetten", "Rain/Lech vs Ismaning", "Ismaning vs Rain/Lech", "Kirchanschoring vs Rain/Lech", "Rain/Lech vs Kirchanschoring", "<PERSON><PERSON><PERSON> vs Rain/Lech", "<PERSON>/<PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "Landsberg vs Rain/Lech", "Rain/Lech vs Landsberg", "Rain/Lech vs Memmingen", "Memmingen vs Rain/Lech", "Nordlingen vs Rain/Lech", "Rain/Lech vs Nordlingen", "Pipinsried vs Rain/Lech", "Rain/Le<PERSON> vs Pi<PERSON>sried", "Rain/Lech vs Schalding", "Schalding vs Rain/Lech", "Sonthofen vs Rain/Lech", "Rain/Lech vs Sonthofen", "T. Augsburg vs Rain/Lech", "<PERSON>/<PERSON> vs T. Augsburg", "Rain/Lech vs Unterhaching B", "Unterhaching B vs Rain/Lech"], "teams_affected": ["Kirchanschoring", "Schalding", "Heimstetten", "T. <PERSON>", "Erlbach", "<PERSON><PERSON><PERSON>", "Deisenhofen", "Ismaning", "1860 Munchen B", "Rain/Lech", "Sonthofen", "Unterhaching B", "Nordlingen", "Memmingen", "<PERSON><PERSON><PERSON>", "Pipinsried", "Landsberg"], "total_matches": 497, "valid_matches": 466, "skipped_matches": 31}, "NETHERLANDS_EERSTE_DIVISIE": {"warning_count": 192, "missing_matches": ["MVV Maastricht vs Cambuur", "TOP Oss vs Cambuur", "FC Dordrecht vs Cambuur", "Cambuur vs Roda JC", "Cambuur vs FC Emmen", "VVV vs Cambuur", "Cambuur vs FC Volendam", "Cambuur vs FC Eindhoven", "Jong AZ vs Cambuur", "Cambuur vs Jong PSV", "Cambuur vs FC Den Bosch", "Cambuur vs ADO Den Haag", "Cambuur vs Jong AZ", "Roda JC vs Cambuur", "FC Eindhoven vs Cambuur", "Jong PSV vs Cambuur", "Cambuur vs TOP Oss", "Cambuur vs VVV", "FC Den Bosch vs Cambuur", "Cambuur vs FC Dordrecht", "FC Volendam vs Cambuur", "FC Emmen vs Cambuur", "ADO Den Haag vs Cambuur", "Cambuur vs MVV Maastricht", "De Graafschap vs FC Volendam", "Roda JC vs De Graafschap", "De Graafschap vs FC Eindhoven", "Jong AZ vs De Graafschap", "FC Den Bosch vs De Graafschap", "De Graafschap vs Jong PSV", "ADO Den Haag vs De Graafschap", "De Graafschap vs FC Dordrecht", "MVV Maastricht vs De Graafschap", "<PERSON> G<PERSON>sch<PERSON> vs TOP Oss", "FC Emmen vs De Graafschap", "De Graafschap vs VVV", "FC Volendam vs De Graafschap", "De Graafschap vs FC Emmen", "FC Dordrecht vs De Graafschap", "<PERSON> G<PERSON>sch<PERSON> vs ADO Den Haag", "De Graafschap vs FC Den Bosch", "<PERSON> Graafschap vs Jong AZ", "Jong PSV vs De Graafschap", "TOP Oss vs De Graafschap", "De Graafschap vs MVV Maastricht", "<PERSON> vs Roda JC", "FC Eindhoven vs De Graafschap", "VVV vs De Graafschap", "TOP Oss vs Excelsior", "ADO Den Haag vs Excelsior", "Excelsior vs VVV", "FC Dordrecht vs Excelsior", "Excelsior vs Jong AZ", "Roda JC vs Excelsior", "Excelsior vs MVV Maastricht", "Jong PSV vs Excelsior", "Excelsior vs FC Eindhoven", "FC Den Bosch vs Excelsior", "FC Emmen vs Excelsior", "Excelsior vs FC Volendam", "VVV vs Excelsior", "Excelsior vs FC Dordrecht", "Excelsior vs ADO Den Haag", "Excelsior vs Roda JC", "Excelsior vs FC Den Bosch", "FC Eindhoven vs Excelsior", "MVV Maastricht vs Excelsior", "Excelsior vs TOP Oss", "FC Volendam vs Excelsior", "Excelsior vs FC Emmen", "Excelsior vs Jong PSV", "Jong AZ vs Excelsior", "FC Emmen vs Helmond Sport", "Helmond Sport vs VVV", "Helmond Sport vs TOP Oss", "Helmond Sport vs FC Volendam", "Jong PSV vs Helmond Sport", "Jong AZ vs Helmond Sport", "Helmond Sport vs Roda JC", "MVV Maastricht vs Helmond Sport", "FC Dordrecht vs Helmond Sport", "Helmond Sport vs FC Eindhoven", "Helmond Sport vs ADO Den Haag", "Helmond Sport vs FC Den Bosch", "ADO Den Haag vs Helmond Sport", "Helmond Sport vs Jong AZ", "TOP Oss vs Helmond Sport", "Helmond Sport vs FC Dordrecht", "Helmond Sport vs FC Emmen", "FC Volendam vs Helmond Sport", "Helmond Sport vs MVV Maastricht", "Roda JC vs Helmond Sport", "Helmond Sport vs Jong PSV", "FC Eindhoven vs Helmond Sport", "VVV vs Helmond Sport", "FC Den Bosch vs Helmond Sport", "Jong PSV vs Jong Ajax", "Jong Ajax vs MVV Maastricht", "FC Eindhoven vs Jong Ajax", "Jong Ajax vs FC Den Bosch", "Jong Ajax vs Jong AZ", "Jong Ajax vs FC Dordrecht", "Jong Ajax vs ADO Den Haag", "Roda JC vs Jong Ajax", "Jong Ajax vs VVV", "FC Emmen vs Jong Ajax", "FC Volendam vs Jong Ajax", "TOP Oss vs Jong Ajax", "Jong Ajax vs Roda JC", "Jong AZ vs Jong Ajax", "Jong Ajax vs Jong PSV", "ADO Den Haag vs Jong Ajax", "Jong Ajax vs FC Eindhoven", "VVV vs Jong Ajax", "Jong Ajax vs FC Emmen", "FC Den Bosch vs Jong Ajax", "Jong Ajax vs FC Volendam", "FC Dordrecht vs Jong Ajax", "MVV Maastricht vs Jong Ajax", "Jong Ajax vs TOP Oss", "Jong Utrecht vs Jong PSV", "Jong Utrecht vs FC Emmen", "VVV vs Jong Utrecht", "Jong Utrecht vs FC Den Bosch", "ADO Den Haag vs Jong Utrecht", "Jong Utrecht vs Jong AZ", "FC Dordrecht vs Jong Utrecht", "Jong Utrecht vs MVV Maastricht", "FC Volendam vs Jong Utrecht", "Jong Utrecht vs Roda JC", "TOP Oss vs Jong Utrecht", "Jong Utrecht vs FC Eindhoven", "Roda JC vs Jong Utrecht", "FC Den Bosch vs Jong Utrecht", "FC Eindhoven vs Jong Utrecht", "Jong Utrecht vs TOP Oss", "Jong AZ vs Jong Utrecht", "FC Emmen vs Jong Utrecht", "Jong Utrecht vs VVV", "Jong Utrecht vs ADO Den Haag", "MVV Maastricht vs Jong Utrecht", "Jong Utrecht vs FC Volendam", "Jong Utrecht vs FC Dordrecht", "Jong PSV vs Jong Utrecht", "Telstar vs FC Eindhoven", "Jong PSV vs Telstar", "Telstar vs Jong AZ", "Telstar vs MVV Maastricht", "ADO Den Haag vs Telstar", "Telstar vs FC Den Bosch", "TOP Oss vs Telstar", "FC Emmen vs Telstar", "Telstar vs FC Volendam", "Roda JC vs Telstar", "Telstar vs VVV", "FC Dordrecht vs Telstar", "MVV Maastricht vs Telstar", "Telstar vs Jong PSV", "VVV vs Telstar", "FC Den Bosch vs Telstar", "Jong AZ vs Telstar", "Telstar vs FC Dordrecht", "FC Volendam vs Telstar", "Telstar vs Roda JC", "Telstar vs ADO Den Haag", "FC Eindhoven vs Telstar", "Telstar vs TOP Oss", "Telstar vs FC Emmen", "VVV vs Vitesse Arnhem", "Vitesse Arnhem vs FC Eindhoven", "Jong AZ vs Vitesse Arnhem", "FC Emmen vs Vitesse Arnhem", "Vitesse Arnhem vs FC Volendam", "FC Den Bosch vs Vitesse Arnhem", "FC Dordrecht vs Vitesse Arnhem", "Vitesse Arnhem vs Jong PSV", "Vitesse Arnhem vs TOP Oss", "ADO Den Haag vs Vitesse Arnhem", "Roda JC vs Vitesse Arnhem", "MVV Maastricht vs Vitesse Arnhem", "Jong PSV vs Vitesse Arnhem", "Vitesse Arnhem vs VVV", "FC Volendam vs Vitesse Arnhem", "Vitesse Arnhem vs FC Dordrecht", "Vitesse Arnhem vs FC Emmen", "Vitesse Arnhem vs MVV Maastricht", "FC Eindhoven vs Vitesse Arnhem", "Vitesse Arnhem vs ADO Den Haag", "Vitesse Arnhem vs Jong AZ", "Vitesse Arnhem vs Roda JC", "TOP Oss vs Vitesse Arnhem", "Vitesse Arnhem vs FC Den Bosch"], "teams_affected": ["FC Dordrecht", "Helmond Sport", "FC Eindhoven", "Excelsior", "Cambuur", "ADO Den Ha<PERSON>", "Telstar", "FC Den Bosch", "FC Emmen", "MVV Maastricht", "Vitesse Arnhem", "Jong AZ", "Jong Ajax", "Roda JC", "FC Volendam", "TOP Oss", "De G<PERSON>schap", "Jong PSV", "VVV", "Jong Utrecht"], "total_matches": 304, "valid_matches": 112, "skipped_matches": 192}, "SCOTLAND_LEAGUE_ONE": {"warning_count": 35, "missing_matches": ["Alloa Athletic vs Queen of South", "Queen of South vs Alloa Athletic", "Alloa Athletic vs Queen of South", "Queen of South vs Alloa Athletic", "Queen of South vs Annan Athletic", "Annan Athletic vs Queen of South", "Queen of South vs Annan Athletic", "Annan Athletic vs Queen of South", "Queen of South vs Arbroath", "Arbroath vs Queen of South", "Queen of South vs Arbroath", "Queen of South vs Cove Rangers", "Cove Rangers vs Queen of South", "Queen of South vs Cove Rangers", "Cove Rangers vs Queen of South", "Queen of South vs Dumbarton", "Dumbarton vs Queen of South", "Queen of South vs Dumbarton", "Dumbarton vs Queen of South", "Inverness vs Queen of South", "Queen of South vs Inverness", "Inverness vs Queen of South", "Queen of South vs Inverness", "Queen of South vs Kelty Hearts", "Kelty Hearts vs Queen of South", "Queen of South vs Kelty Hearts", "Kelty Hearts vs Queen of South", "<PERSON><PERSON> vs Queen of South", "Queen of South vs Montrose", "<PERSON><PERSON> vs Queen of South", "Queen of South vs Montrose", "Stenhousemuir vs Queen of South", "Queen of South vs Stenhousemuir", "Stenhousemuir vs Queen of South", "Queen of South vs Stenhousemuir"], "teams_affected": ["Annan Athletic", "Inverness", "Arbroath", "Cove Rangers", "Kelty Hearts", "<PERSON><PERSON>", "Stenhousemuir", "Alloa Athletic", "Queen of South", "Dumbarton"], "total_matches": 315, "valid_matches": 280, "skipped_matches": 35}, "NETHERLANDS_TWEEDE_DIVISIE": {"warning_count": 144, "missing_matches": ["Barendrecht vs HHC", "Barendrecht vs ACV Assen", "Barendrecht vs ADO 20", "AFC vs Barendrecht", "Barendrecht vs Koninklijke HFC", "Barendrecht vs GVVV", "RKAV Volendam vs Barendrecht", "HHC vs Barendrecht", "ACV Assen vs Barendrecht", "ADO 20 vs Barendrecht", "Barendrecht vs AFC", "Koninklijke HFC vs Barendrecht", "<PERSON> vs AFC", "<PERSON> Treffers vs Koninklijke HFC", "GVVV vs De Treffers", "RKAV Volendam vs De Treffers", "HHC vs De Treffers", "<PERSON> vs ADO 20", "<PERSON><PERSON> vs <PERSON>", "ADO 20 vs <PERSON>", "AFC vs De <PERSON>", "Koninklijke HFC vs De Treffers", "<PERSON> vs GVVV", "<PERSON> vs RKAV Volendam", "<PERSON> vs HHC", "Koninklijke HFC vs E. <PERSON>", "<PERSON><PERSON> vs GVVV", "RKAV <PERSON>endam vs <PERSON><PERSON>", "<PERSON><PERSON> vs HHC", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs AFC", "<PERSON><PERSON> vs Koninklijke HFC", "ADO 20 vs <PERSON><PERSON>", "GVVV vs <PERSON><PERSON>", "<PERSON><PERSON> vs RKAV Volendam", "HHC vs <PERSON><PERSON>", "GVVV vs Jong Almere C.", "Jong Almere C. vs RKAV Volendam", "HHC vs Jong Almere C.", "ACV Assen vs Jong Almere C.", "ADO 20 vs Jong Almere C.", "Jong Almere C. vs AFC", "Koninklijke HFC vs Jong Almere C.", "Jong Almere C. vs GVVV", "RKAV Volendam vs Jong Almere C.", "Jong Almere C. vs HHC", "Jong Almere C. vs ACV Assen", "Jong Almere C. vs ADO 20", "AFC vs Jong Almere C.", "Jong Sparta vs GVVV", "RKAV Volendam vs Jong Sparta", "Jong Sparta vs HHC", "Jong Sparta vs ACV Assen", "Jong Sparta vs ADO 20", "AFC vs Jong Sparta", "Jong Sparta vs Koninklijke HFC", "GVVV vs Jong Sparta", "Jong Sparta vs RKAV Volendam", "HHC vs Jong Sparta", "ACV Assen vs Jong Sparta", "ADO 20 vs Jong Sparta", "Jong Sparta vs AFC", "Koninklijke HFC vs Jong Sparta", "ADO 20 vs Katwijk", "Katwijk vs AFC", "Koninklijke HFC vs Katwijk", "Katwijk vs GVVV", "RKAV Volendam vs Katwijk", "Katwijk vs HHC", "Katwijk vs ACV Assen", "Katwijk vs ADO 20", "AFC vs Katwijk", "Katwijk vs Koninklijke HFC", "GVVV vs Katwijk", "Katwijk vs RKAV Volendam", "Noordwijk vs GVVV", "RKAV Volendam vs Noordwijk", "Noordwijk vs HHC", "Noordwijk vs ACV Assen", "Noordwijk vs ADO 20", "AFC vs Noordwijk", "Noordwijk vs Koninklijke HFC", "GVVV vs Noordwijk", "Noordwijk vs RKAV Volendam", "HHC vs Noordwijk", "ACV Assen vs Noordwijk", "ADO 20 vs Noordwijk", "Noordwijk vs AFC", "Koninklijke HFC vs Noordwijk", "GVVV vs Quick Boys", "Quick Boys vs RKAV Volendam", "HHC vs Quick Boys", "ACV Assen vs Quick Boys", "ADO 20 vs Quick Boys", "Quick Boys vs AFC", "Koninklijke HFC vs Quick Boys", "Quick Boys vs GVVV", "RKAV Volendam vs Quick Boys", "Quick Boys vs HHC", "Quick Boys vs ACV Assen", "Quick Boys vs ADO 20", "AFC vs Quick Boys", "Quick Boys vs Koninklijke HFC", "GVVV vs Rijnsburgse B.", "Rijnsburgse B. vs RKAV Volendam", "HHC vs Rijnsburgse B.", "ACV Assen vs Rijnsburgse B.", "ADO 20 vs Rijnsburgse B.", "Rijnsburgse B. vs AFC", "Koninklijke HFC vs Rijnsburgse B.", "Rijnsburgse B. vs GVVV", "RKAV Volendam vs Rijnsburgse B.", "Rijnsburgse B. vs HHC", "Rijnsburgse B. vs ACV Assen", "Rijnsburgse B. vs ADO 20", "AFC vs Rijnsburgse B.", "Rijnsburgse B. vs Koninklijke HFC", "Scheveningen vs GVVV", "RKAV Volendam vs Scheveningen", "Scheveningen vs HHC", "Scheveningen vs ACV Assen", "Scheveningen vs ADO 20", "AFC vs Scheveningen", "Scheveningen vs Koninklijke HFC", "GVVV vs Scheveningen", "Scheveningen vs RKAV Volendam", "HHC vs Scheveningen", "ACV Assen vs Scheveningen", "ADO 20 vs Scheveningen", "Scheveningen vs AFC", "Koninklijke HFC vs Scheveningen", "Spakenburg vs RKAV Volendam", "HHC vs Spakenburg", "ACV Assen vs Spakenburg", "ADO 20 vs Spakenburg", "Spakenburg vs AFC", "Koninklijke HFC vs Spakenburg", "GVVV vs Spakenburg", "RKAV Volendam vs Spakenburg", "Spakenburg vs HHC", "Spakenburg vs ACV Assen", "Spakenburg vs ADO 20", "AFC vs Spakenburg", "Spakenburg vs Koninklijke HFC"], "teams_affected": ["Rijnsburgse B.", "Koninklijke HFC", "Jong Sparta", "Spakenburg", "GVVV", "<PERSON>", "<PERSON><PERSON>", "Quick Boys", "Scheveningen", "<PERSON><PERSON><PERSON>", "ACV Assen", "Jong Almere C.", "Noordwijk", "RKAV Volendam", "Barendrecht", "HHC", "ADO 20", "AFC"], "total_matches": 330, "valid_matches": 186, "skipped_matches": 144}, "GERMANY_OBERLIGA_SCHLESWIG_HOLSTEIN": {"warning_count": 107, "missing_matches": ["<PERSON><PERSON> vs VfR Neumunster", "<PERSON><PERSON> vs <PERSON><PERSON>", "Heider SV vs D. Lubeck", "PSV Neumunster vs <PERSON><PERSON>", "Eutiner SV vs D. Lubeck", "VfR Neumunster vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs Heider SV", "<PERSON><PERSON> vs PSV Neumunster", "Eutiner SV vs Eckernforder", "VfR Neumunster vs Eckernforder", "<PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON><PERSON>", "Heider SV vs Eckernforder", "PSV Neumunster vs Eckernforder", "Eckernforder vs Eutiner SV", "E<PERSON><PERSON>forder vs VfR Neumunster", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "Eckernforder vs Heider SV", "Eichede vs Eutiner SV", "VfR Neumunster vs Eichede", "<PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "Eichede vs Heider SV", "Eichede vs PSV Neumunster", "Eutiner SV vs Eichede", "E<PERSON>de vs VfR Neumunster", "<PERSON><PERSON> vs E<PERSON><PERSON>", "Heider SV vs Eichede", "PSV Neumunster vs Eichede", "Flensburg vs PSV Neumunster", "Flensburg vs Eutiner SV", "Flensburg vs VfR Neumunster", "Flensburg vs <PERSON><PERSON>", "Flensburg vs Heider SV", "PSV Neumunster vs Flensburg", "Eutiner SV vs Flensburg", "VfR Neumunster vs Flensburg", "<PERSON><PERSON> vs Flensburg", "Heider SV vs Flensburg", "Heider SV vs Hohenwestedt", "PSV Neumunster vs Hohenwestedt", "Hohenwestedt vs VfR Neumunster", "<PERSON>henwestedt vs <PERSON><PERSON>", "Hohenwestedt vs Eutiner SV", "Hohenwestedt vs PSV Neumunster", "VfR Neumunster vs Hohenwestedt", "Hohenwestedt vs Heider SV", "<PERSON><PERSON> vs Hohenwestedt", "Eutiner SV vs Hohenwestedt", "Kilia Kiel vs VfR Neumunster", "<PERSON><PERSON> vs Kilia Kiel", "Heider SV vs Kilia Kiel", "PSV Neumunster vs Kilia Kiel", "Kilia Kiel vs Eutiner SV", "VfR Neumunster vs Kilia Kiel", "<PERSON><PERSON> vs <PERSON><PERSON>", "Kilia Kiel vs Heider SV", "Kilia Kiel vs PSV Neumunster", "Eutiner SV vs Kilia Kiel", "<PERSON><PERSON> vs Lubeck B", "Heider SV vs Lubeck B", "Lubeck B vs PSV Neumunster", "Eutiner SV vs Lubeck B", "VfR Neumunster vs Lubeck B", "Lubeck B vs <PERSON><PERSON>", "Lubeck B vs Heider SV", "PSV Neumunster vs Lubeck B", "Lubeck B vs Eutiner SV", "Lubeck B vs VfR Neumunster", "VfR Neumunster vs Nordmark Satrup", "Nordmark Satrup vs <PERSON><PERSON>", "Nordmark Satrup vs Heider SV", "Nordmark Satrup vs PSV Neumunster", "Eutiner SV vs Nordmark Satrup", "Nordmark Satrup vs VfR Neumunster", "<PERSON><PERSON> vs Nordmark Satrup", "Heider SV vs Nordmark Satrup", "PSV Neumunster vs Nordmark Satrup", "Nordmark Satrup vs Eutiner SV", "Oldenburger vs PSV Neumunster", "Oldenburger vs Eutiner SV", "Oldenburger vs VfR Neumunster", "Oldenburger vs Heider SV", "Oldenburger vs <PERSON><PERSON>", "Heider SV vs Oldenburger", "PSV Neumunster vs Oldenburger", "Eutiner SV vs Oldenburger", "VfR Neumunster vs Oldenburger", "<PERSON><PERSON> vs Oldenburger", "Eutiner SV vs P. Reinfel", "<PERSON><PERSON> vs VfR Neumunster", "<PERSON><PERSON> vs <PERSON><PERSON>", "Heider SV vs P. Reinfel", "PSV Neumunster vs P. <PERSON>", "<PERSON><PERSON> vs Eutiner SV", "VfR Neum<PERSON>ter vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs Heider SV", "<PERSON><PERSON> vs PSV Neumunster", "<PERSON><PERSON> vs Rotenhof", "Heider SV vs Rotenhof", "PSV Neumunster vs Rotenhof", "Rotenhof vs Eutiner SV", "Rotenhof vs VfR Neumunster", "Rotenhof vs Heider SV", "<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "Rotenhof vs PSV Neumunster", "Eutiner SV vs Rotenhof"], "teams_affected": ["Eutiner SV", "Oldenburger", "Rotenhof", "<PERSON><PERSON>", "<PERSON><PERSON>", "Lubeck B", "<PERSON><PERSON>", "Flensburg", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Hohenwestedt", "Nordmark Satrup", "VfR Neumunster", "<PERSON><PERSON>", "PSV Neumunster", "Heider SV", "<PERSON><PERSON><PERSON>"], "total_matches": 319, "valid_matches": 212, "skipped_matches": 107}, "ITALY_SERIE_C_GROUP_C": {"warning_count": 34, "missing_matches": ["<PERSON><PERSON> vs ACR Messina", "ACR Messina vs <PERSON><PERSON>", "Altamura vs ACR Messina", "ACR Messina vs Altamura", "Avellino vs ACR Messina", "ACR Messina vs Avellino", "ACR Messina vs Benevento", "Benevento vs ACR Messina", "ACR Messina vs Casertana", "Casertana vs ACR Messina", "Catania vs ACR Messina", "ACR Messina vs Catania", "ACR Messina vs Cavese", "Cavese vs ACR Messina", "Crotone vs ACR Messina", "ACR Messina vs Crotone", "ACR Messina vs Foggia", "Foggia vs ACR Messina", "ACR Messina vs Giugliano", "Giugliano vs ACR Messina", "Juventus U23 vs ACR Messina", "ACR Messina vs Juventus U23", "Latina vs ACR Messina", "ACR Messina vs Latina", "ACR Messina vs Monopoli", "Monopoli vs ACR Messina", "Picerno vs ACR Messina", "ACR Messina vs Picerno", "ACR Messina vs Potenza", "Potenza vs ACR Messina", "ACR Messina vs Sorrento", "Sorrento vs ACR Messina", "<PERSON><PERSON><PERSON> vs ACR Messina", "ACR Messina vs Trapani"], "teams_affected": ["Juventus U23", "Latina", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Giugliano", "Benevento", "Crotone", "ACR Messina", "Casertana", "Monopoli", "Potenza", "<PERSON><PERSON><PERSON>", "Cavese", "<PERSON><PERSON>", "Catania", "<PERSON><PERSON><PERSON>", "Foggia"], "total_matches": 578, "valid_matches": 544, "skipped_matches": 34}, "SPAIN_PRIMERA_F_WOMEN": {"warning_count": 66, "missing_matches": ["Atletico B W vs Alaves W", "DUX Logrono W vs Alaves W", "Alaves W vs AEM W", "Alaves W vs Atletico B W", "Alaves W vs DUX Logrono W", "AEM W vs Alaves W", "DUX Logrono W vs Albacete W", "Albacete W vs Atletico B W", "AEM W vs Albacete W", "Albacete W vs DUX Logrono W", "Atletico B W vs Albacete W", "Albacete W vs AEM W", "AEM W vs Alhama W", "Alhama W vs Atletico B W", "Alhama W vs DUX Logrono W", "Atletico B W vs Alhama W", "DUX Logrono W vs Alhama W", "Alhama W vs AEM W", "Baleares W vs AEM W", "Atletico B W vs Baleares W", "Baleares W vs DUX Logrono W", "AEM W vs Baleares W", "DUX Logrono W vs Baleares W", "Baleares W vs Atletico B W", "Barcelona B W vs AEM W", "DUX Logrono W vs Barcelona B W", "Atletico B W vs Barcelona B W", "Barcelona B W vs DUX Logrono W", "AEM W vs Barcelona B W", "Barcelona B W vs Atletico B W", "DUX Logrono W vs Caceres W", "Caceres W vs Atletico B W", "Caceres W vs AEM W", "Atletico B W vs Caceres W", "AEM W vs Caceres W", "Caceres W vs DUX Logrono W", "DUX Logrono W vs Getafe W", "Getafe W vs Atletico B W", "AEM W vs Getafe W", "Getafe W vs DUX Logrono W", "Getafe W vs AEM W", "Atletico B W vs Getafe W", "Huelva W vs DUX Logrono W", "Atletico B W vs Huelva W", "Huelva W vs AEM W", "Huelva W vs Atletico B W", "AEM W vs Huelva W", "DUX Logrono W vs Huelva W", "Osasuna W vs DUX Logrono W", "AEM W vs Osasuna W", "Osasuna W vs Atletico B W", "DUX Logrono W vs Osasuna W", "<PERSON><PERSON><PERSON><PERSON> vs AEM W", "Atletico B W vs Osasuna W", "DUX Logrono W vs Real Madrid B W", "Atletico B W vs Real Madrid B W", "Real Madrid B W vs AEM W", "AEM W vs Real Madrid B W", "Real Madrid B W vs Atletico B W", "Real Madrid B W vs DUX Logrono W", "Villarreal W vs AEM W", "Atletico B W vs Villarreal W", "Villarreal W vs DUX Logrono W", "Villarreal W vs Atletico B W", "DUX Logrono W vs Villarreal W", "AEM W vs Villarreal W"], "teams_affected": ["Atletico B W", "DUX Logrono W", "Alhama W", "Alaves W", "Caceres W", "<PERSON><PERSON><PERSON><PERSON>", "Getafe W", "Villarreal W", "Barcelona B W", "Albacete W", "<PERSON><PERSON><PERSON> W", "Balea<PERSON> W", "Real Madrid B W", "AEM W"], "total_matches": 286, "valid_matches": 220, "skipped_matches": 66}, "RUSSIA_FNL": {"warning_count": 60, "missing_matches": ["SKA Khabarovsk vs A. Vladikavkaz", "<PERSON><PERSON> vs PFC Sochi", "PFC Sochi vs A. Vladikavkaz", "<PERSON><PERSON> vs SKA Khabarovsk", "Arsenal Tula vs SKA Khabarovsk", "Arsenal Tula vs PFC Sochi", "SKA Khabarovsk vs Arsenal Tula", "PFC Sochi vs Arsenal Tula", "PFC Sochi vs Baltika", "Baltika vs SKA Khabarovsk", "SKA Khabarovsk vs Baltika", "Baltika vs PFC Sochi", "SKA Khabarovsk vs Chayka", "PFC Sochi vs Chayka", "Chayka vs SKA Khabarovsk", "Chayka vs PFC Sochi", "PFC Sochi vs Chernomorets", "SKA Khabarovsk vs Chernomorets", "Chernomorets vs SKA Khabarovsk", "Kamaz vs SKA Khabarovsk", "Kamaz vs PFC Sochi", "PFC Sochi vs Kamaz", "SKA Khabarovsk vs Kamaz", "SKA Khabarovsk vs Neftekhimik", "PFC Sochi vs Neftekhimik", "Neftekhimik vs SKA Khabarovsk", "Neftekhimik vs PFC Sochi", "<PERSON><PERSON> vs SKA Khabarovsk", "PFC Sochi vs Rodina <PERSON>", "<PERSON><PERSON> vs PFC Sochi", "PFC Sochi vs Rotor Volgograd", "Rotor Volgograd vs SKA Khabarovsk", "Rotor Volgograd vs PFC Sochi", "SKA Khabarovsk vs Rotor Volgograd", "Shinnik vs PFC Sochi", "SKA Khabarovsk vs Shinnik", "Shinnik vs SKA Khabarovsk", "PFC Sochi vs Shinnik", "SKA Khabarovsk vs Sokol Saratov", "Sokol Saratov vs PFC Sochi", "Sokol Saratov vs SKA Khabarovsk", "PFC Sochi vs Sokol Saratov", "Torpedo Moscow vs SKA Khabarovsk", "Torpedo Moscow vs PFC Sochi", "SKA Khabarovsk vs Torpedo Moscow", "PFC Sochi vs Tyumen", "SKA Khabarovsk vs Tyumen", "Tyumen vs PFC Sochi", "Tyumen vs SKA Khabarovsk", "Ufa vs PFC Sochi", "Ufa vs SKA Khabarovsk", "SKA Khabarovsk vs Ufa", "PFC Sochi vs Ufa", "Ural vs PFC Sochi", "SKA Khabarovsk vs Ural", "PFC Sochi vs Ural", "Yenisey vs PFC Sochi", "Yenisey vs SKA Khabarovsk", "PFC Sochi vs Yenisey", "SKA Khabarovsk vs Yenisey"], "teams_affected": ["SKA Khabarovsk", "<PERSON><PERSON>", "Torpedo Moscow", "Tyumen", "Yenisey", "Rotor Volgograd", "Ufa", "PFC Sochi", "Chernomorets", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ural", "Baltika", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Arsenal Tula", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Shinnik"], "total_matches": 512, "valid_matches": 452, "skipped_matches": 60}, "KAZAKHSTAN_PREMIER_LEAGUE": {"warning_count": 4, "missing_matches": ["Kaisar K. vs FC Astana", "Tobol vs Ulytau", "Okzhetpes vs Tobol", "FC Astana vs Tobol"], "teams_affected": ["<PERSON>ly<PERSON><PERSON>", "FC Astana", "Okzhetpes", "Tobol", "Kaisar <PERSON>."], "total_matches": 14, "valid_matches": 10, "skipped_matches": 4}, "IRELAND_FIRST_DIVISION": {"warning_count": 5, "missing_matches": ["Dundalk vs Athlone Town", "Dundalk vs Longford Town", "Longford Town vs UC Dublin", "Longford Town vs Dundalk", "UC Dublin vs Treaty Utd"], "teams_affected": ["Longford Town", "UC Dublin", "Athlone Town", "Dundalk", "Treaty Utd"], "total_matches": 22, "valid_matches": 17, "skipped_matches": 5}, "BAHRAIN_PREMIER_LEAGUE": {"warning_count": 54, "missing_matches": ["Al Ahli M. vs Bahrain SC", "<PERSON><PERSON><PERSON> vs Al Ahli M.", "Al Ahli M. vs AAli FC", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs Al Ahli M.", "Al Ahli M. vs Al Shabbab", "Bahrain SC vs Al Ahli M.", "Al Ahli M. vs Al-Najma", "AAli FC vs Al Ahli M.", "<PERSON> vs Al-Najma", "Al Riffa vs AAli FC", "<PERSON> vs Al-Muharraq", "Al Shabbab vs Al Riffa", "Bahrain SC vs Al Riffa", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs Al Riffa", "AAli FC vs Al Riffa", "Bahrain SC vs East Riffa", "East Riffa vs Al-Najma", "East Riffa vs AAli FC", "East Riffa vs Al-Muharraq", "Al Shabbab vs East Riffa", "East Riffa vs Bahrain SC", "Al-Najma vs East Riffa", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs Al Shabbab", "Khalidiya vs Bahrain SC", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "AAli FC vs Khalidiya", "<PERSON><PERSON> vs Al-Muhar<PERSON><PERSON>", "Al Shabbab vs Khalidiya", "Mal<PERSON>ya vs Al-Muharraq", "Al Shabbab vs Malkiya", "AAli FC vs Malkiya", "Malkiya vs Bahrain SC", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs Malkiya", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs Mal<PERSON>ya", "Malkiya vs Al Shabbab", "Malkiya vs AAli FC", "Bahrain SC vs Malkiya", "Malkiya vs Al-Najma", "Manama vs AAli FC", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs Al Shabbab", "Manama vs Bahrain SC", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "AAli FC vs Manama", "<PERSON><PERSON> vs Al-Muhar<PERSON><PERSON>", "Sitra vs AAli FC", "<PERSON><PERSON> vs Al-Muharraq", "Al Shabbab vs Sitra", "Bahrain SC vs Sitra", "<PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON><PERSON>", "AAli FC vs Sitra", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs Al Shabbab"], "teams_affected": ["Malkiya", "Al Ahli M.", "<PERSON><PERSON>", "Bahrain SC", "Al-Muharraq", "Manama", "<PERSON><PERSON>", "Al R<PERSON>a", "Al Shabbab", "AAli FC", "East Riffa", "Al-<PERSON><PERSON><PERSON>"], "total_matches": 126, "valid_matches": 72, "skipped_matches": 54}, "ITALY_SERIE_D_GROUP_I": {"warning_count": 58, "missing_matches": ["Acireale vs CastrumFavara", "Acireale vs CD S. Agata", "CastrumFavara vs Acireale", "CD S. Agata vs Acireale", "<PERSON><PERSON>Favara vs Enna", "CD S. Agata vs Enna", "En<PERSON> vs CastrumFavara", "Enna vs CD S. Agata", "CD S. Agata vs Igea Virtus", "Igea Virtus vs CastrumFavara", "Igea Virtus vs CD S. Agata", "CastrumFavara vs Igea Virtus", "Licata vs CastrumFavara", "Licata vs CD S. Agata", "CastrumFavara vs Licata", "CD S. Agata vs Licata", "CastrumFavara vs Locri 1909", "CD S. Agata vs Locri 1909", "Locri 1909 vs CastrumFavara", "Locri 1909 vs CD S. Agata", "CastrumFavara vs Nissa", "CD S. Agata vs Nissa", "Nissa vs CastrumFavara", "Nissa vs CD S. Agata", "Paterno vs CastrumFavara", "Paterno vs CD S. Agata", "CastrumFavara vs Paterno", "CD S. Agata vs Paterno", "Pompei vs CastrumFavara", "Pompei vs CD S. Agata", "CastrumFavara vs Pompei", "CD S. Agata vs Pompei", "Ragusa vs CD S. Agata", "Ragusa vs CastrumFavara", "CD S. Agata vs Ragusa", "CastrumFavara vs Ragusa", "CD S. Agata vs Reggina", "CastrumFavara vs Reggina", "Reggina vs CD S. Agata", "Sambiase vs CastrumFavara", "Sambiase vs CD S. Agata", "CastrumFavara vs Sambiase", "CD S. Agata vs Sambiase", "Sancataldese vs CastrumFavara", "CD S. Agata vs Sancataldese", "CastrumFavara vs Sancataldese", "Sancataldese vs CD S. Agata", "CastrumFavara vs Scafatese", "Scafatese vs CD S. Agata", "Scafatese vs CastrumFavara", "Siracusa vs CD S. Agata", "CastrumFavara vs Siracusa", "CD S. Agata vs Siracusa", "Siracusa vs CastrumFavara", "CastrumFavara vs Vibonese", "CD S. Agata vs Vibonese", "Vibonese vs CastrumFavara", "Vibonese vs CD S. Agata"], "teams_affected": ["Lo<PERSON>ri 1909", "Paterno", "Pompei", "<PERSON><PERSON><PERSON><PERSON>", "Licata", "Sir<PERSON><PERSON>", "CD S. Agata", "Reggina", "<PERSON><PERSON><PERSON><PERSON>", "Vibonese", "Acireale", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "CastrumFavara", "Igea Virtus", "<PERSON><PERSON>"], "total_matches": 452, "valid_matches": 394, "skipped_matches": 58}, "ESTONIA_ESILIIGA": {"warning_count": 3, "missing_matches": ["Levadia B vs Nomme Utd", "Tallinna K. B vs Nomme Utd", "Nomme Utd vs Viimsi"], "teams_affected": ["Levadia B", "Tallinna K. B", "Viimsi", "Nomme Utd"], "total_matches": 25, "valid_matches": 22, "skipped_matches": 3}, "CHILE_PRIMERA_DIVISION": {"warning_count": 3, "missing_matches": ["Audax Italiano vs OHiggins", "La Serena vs Everton", "Colo-Colo vs Palestino"], "teams_affected": ["<PERSON><PERSON><PERSON>", "Audax Italiano", "<PERSON><PERSON><PERSON>", "Everton", "Colo-Colo", "La Serena"], "total_matches": 9, "valid_matches": 6, "skipped_matches": 3}, "ISRAEL_NATIONAL_LEAGUE": {"warning_count": 34, "missing_matches": ["<PERSON><PERSON><PERSON> vs <PERSON><PERSON> <PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs B<PERSON><PERSON>", "<PERSON><PERSON> <PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON> <PERSON>", "<PERSON><PERSON> <PERSON><PERSON>Fahm vs Hapoel Afula", "Hapoel Afula vs H. Umm al-Fahm", "Hapoel Afula vs H. Umm al-Fahm", "<PERSON><PERSON> <PERSON>m al-Fahm vs Hapoel Akko", "Hapoel Akko vs H. Umm al-Fahm", "Hapoel Raanana vs H. Umm al-Fahm", "<PERSON><PERSON> <PERSON><PERSON> al-Fahm vs Hapoel Raanana", "<PERSON><PERSON> <PERSON><PERSON> al-Fahm vs Hapoel Tel Aviv", "Hapoel Tel Aviv vs H. Umm al-Fahm", "<PERSON><PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON>", "<PERSON><PERSON> <PERSON> vs <PERSON><PERSON>", "Maccabi Jaffa vs H. Umm al-Fahm", "<PERSON><PERSON> <PERSON><PERSON> al-Fahm vs Maccabi Jaffa", "Maccabi Jaffa vs H. Umm al-Fahm", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>"], "teams_affected": ["<PERSON><PERSON>", "Hapoel Akko", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON>", "Hapoel Tel Aviv", "Maccabi Jaffa", "Hapoel Afula", "<PERSON><PERSON>", "<PERSON><PERSON>", "Hapoel Raanana", "<PERSON><PERSON>", "Bnei <PERSON>", "<PERSON><PERSON><PERSON>"], "total_matches": 504, "valid_matches": 470, "skipped_matches": 34}, "MEXICO_LIGA_MX": {"warning_count": 32, "missing_matches": ["<PERSON>. <PERSON> vs CF America", "<PERSON><PERSON><PERSON> UNAM vs A. San Luis", "Atlas vs Pumas UNAM", "CF America vs Atlas", "<PERSON><PERSON>s UNAM vs Club Leon", "Club Leon vs CF America", "Cruz Azul vs CF America", "Pumas UNAM vs Cruz Azul", "CF America vs Guadalajara", "Guadalajara vs Pumas UNAM", "Juarez vs Pumas UNAM", "Juarez vs CF America", "Mazatlan vs CF America", "Ma<PERSON>tlan vs Pumas UNAM", "Monterrey vs Pumas UNAM", "CF America vs Monterrey", "Necaxa vs Pumas UNAM", "Necaxa vs CF America", "Pumas UNAM vs Pachuca", "CF America vs Pachuca", "CF America vs Puebla", "Pumas UNAM vs Puebla", "CF America vs Queretaro", "Pumas UNAM vs Queretaro", "Santos Laguna vs Pumas UNAM", "CF America vs Santos Laguna", "Tigres vs CF America", "Pumas UNAM vs Tigres", "<PERSON><PERSON>s UNAM vs Tijuana", "Tijuana vs CF America", "Toluca vs Pumas UNAM", "Toluca vs CF America"], "teams_affected": ["P<PERSON>s UNAM", "Ju<PERSON>z", "Necaxa", "Tigres", "A. <PERSON>", "Pachuca", "Toluca", "Monterrey", "Mazatlan", "Tijuana", "Guadalajara", "Que<PERSON>ro", "Cruz Azul", "Puebla", "Club Leon", "Atlas", "CF America", "Santos Laguna"], "total_matches": 272, "valid_matches": 240, "skipped_matches": 32}, "HONDURAS_LIGA_NACIONAL": {"warning_count": 18, "missing_matches": ["Genesis vs UPNFM", "UPNFM vs Genesis", "UPNFM vs Juticalpa", "Juticalpa vs UPNFM", "UPNFM vs Marathon", "Marathon vs UPNFM", "Motagua vs UPNFM", "UPNFM vs Motagua", "UPNFM vs Olancho", "<PERSON><PERSON><PERSON> vs UPNFM", "Olimpia vs UPNFM", "UPNFM vs Olimpia", "UPNFM vs Real Espana", "Real Espana vs UPNFM", "Real Sociedad vs UPNFM", "UPNFM vs Real Sociedad", "Victoria vs UPNFM", "UPNFM vs Victoria"], "teams_affected": ["Genesis", "Real Espana", "Motagua", "<PERSON><PERSON><PERSON>", "Olimpia", "UPNFM", "<PERSON><PERSON><PERSON>", "Victoria", "Marathon", "Real Sociedad"], "total_matches": 162, "valid_matches": 144, "skipped_matches": 18}, "SLOVAKIA_FORTUNA_LIGA": {"warning_count": 50, "missing_matches": ["Banska Bystrica vs DAC Streda", "Banska Bystrica vs FC Kosice", "DAC Streda vs Banska Bystrica", "FC Kosice vs Banska Bystrica", "DAC Streda vs Komarno", "FC Kosice vs Komarno", "Komarno vs DAC Streda", "Komarno vs FC Kosice", "FC Kosice vs Podbrezova", "DAC Streda vs Podbrezova", "Podbrezova vs FC Kosice", "Podbrezova vs DAC Streda", "Podbrezova vs FC Kosice", "DAC Streda vs Podbrezova", "FC Kosice vs Podbrezova", "FC Kosice vs Ruzomberok", "Ruzomberok vs DAC Streda", "Ruzomberok vs FC Kosice", "DAC Streda vs Ruzomberok", "S. Bratislava vs FC Kosice", "DAC Streda vs S. Bratislava", "FC Kosice vs S. Bratislava", "S. Bratislava vs DAC Streda", "FC Kosice vs S. Bratislava", "S. Bratislava vs DAC Streda", "DAC Streda vs S. Bratislava", "Skalica vs FC Kosice", "Skalica vs DAC Streda", "FC Kosice vs Skalica", "DAC Streda vs Skalica", "FC Kosice vs Spartak Trnava", "DAC Streda vs Spartak Trnava", "Spartak Trnava vs FC Kosice", "Spartak Trnava vs DAC Streda", "Spartak Trnava vs DAC Streda", "FC Kosice vs Spartak Trnava", "Trencin vs FC Kosice", "Trencin vs DAC Streda", "FC Kosice vs Trencin", "DAC Streda vs Trencin", "<PERSON><PERSON> vs DAC Streda", "Z. Michalovce vs FC Kosice", "DAC Streda vs <PERSON><PERSON>", "FC Kosice vs Z. Michalovce", "FC Kosice vs Zilina", "DAC Streda vs Zilina", "Zilina vs FC Kosice", "Zilina vs DAC Streda", "DAC Streda vs Zilina", "Zilina vs FC Kosice"], "teams_affected": ["DAC Streda", "Spartak Trnava", "S. Bratislava", "<PERSON><PERSON><PERSON>", "FC Kosice", "Banska Bystrica", "<PERSON><PERSON>", "Podbrezova", "Skalica", "Ruzomberok", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "total_matches": 286, "valid_matches": 236, "skipped_matches": 50}, "NORWAY_ELITESERIEN": {"warning_count": 1, "missing_matches": ["Fredrikstad vs Valerenga"], "teams_affected": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "total_matches": 7, "valid_matches": 6, "skipped_matches": 1}, "PHILIPPINES_FOOTBALL_LEAGUE": {"warning_count": 4, "missing_matches": ["Mendiola vs DH Cebu", "DH Cebu vs Mendiola", "Stallion vs Kaya FC", "Kaya FC vs Stallion"], "teams_affected": ["Mendiola", "DH Cebu", "Kaya FC", "Stallion"], "total_matches": 10, "valid_matches": 6, "skipped_matches": 4}, "SPAIN_SEGUNDA_RFEF_GROUP_4": {"warning_count": 127, "missing_matches": ["Aguilas CF vs Almeria B", "Almeria B vs Xerez CD", "Xerez D. FC vs Almeria B", "Almeria B vs UCAM Murcia", "CD Estepona vs Almeria B", "Almeria B vs Aguilas CF", "Xerez CD vs Almeria B", "Almeria B vs Xerez D. FC", "UCAM Murcia vs Almeria B", "Almeria B vs CD Estepona", "Antoniano vs Xerez CD", "Antoniano vs Xerez D. FC", "UCAM Murcia vs Antoniano", "Antoniano vs CD Estepona", "Antoniano vs Aguilas CF", "Xerez CD vs Antoniano", "Xerez D. FC vs Antoniano", "Antoniano vs UCAM Murcia", "CD Estepona vs Antoniano", "Cadiz B vs Xerez D. FC", "UCAM Murcia vs Cadiz B", "Cadiz B vs CD Estepona", "Xerez CD vs Cadiz B", "Aguilas CF vs Cadiz B", "Xerez D. FC vs Cadiz B", "Cadiz B vs UCAM Murcia", "CD Estepona vs Cadiz B", "Cadiz B vs Xerez CD", "Cadiz B vs Aguilas CF", "Aguilas CF vs D. Minera", "D<PERSON> vs Xerez CD", "Xerez D. FC vs D. Minera", "<PERSON><PERSON> vs UCAM Murcia", "CD Estepona vs D. <PERSON>ra", "D. <PERSON> vs Aguilas CF", "Xerez CD vs D. Minera", "D. Minera vs Xerez D. FC", "UCAM Murcia vs D. <PERSON>ra", "<PERSON><PERSON> vs CD Estepona", "<PERSON> vs Aguilas CF", "<PERSON> vs Xerez CD", "Don <PERSON> vs Xerez D. FC", "UCAM Murcia vs Don Benito", "<PERSON> vs CD Estepona", "Aguilas CF vs Don Benito", "Xerez CD vs Don Benito", "Xerez D. FC vs Don Benito", "<PERSON> vs UCAM Murcia", "Granada B vs Xerez CD", "Granada B vs Xerez D. FC", "UCAM Murcia vs Granada B", "Granada B vs CD Estepona", "Granada B vs Aguilas CF", "Xerez CD vs Granada B", "Xerez D. FC vs Granada B", "Granada B vs UCAM Murcia", "CD Estepona vs Granada B", "Aguilas CF vs Granada B", "La Union vs Xerez CD", "Xerez D. FC vs La Union", "La Union vs UCAM Murcia", "CD Estepona vs La Union", "Aguilas CF vs La Union", "Xerez CD vs La Union", "La Union vs Xerez D. FC", "UCAM Murcia vs La Union", "La Union vs CD Estepona", "La Union vs Aguilas CF", "Xerez D. FC vs Linares", "Linares vs UCAM Murcia", "CD Estepona vs Linares", "Aguilas CF vs Linares", "Linares vs Xerez CD", "Linares vs Xerez D. FC", "UCAM Murcia vs Linares", "Linares vs CD Estepona", "Linares vs Aguilas CF", "Xerez D. FC vs Linense", "Linense vs UCAM Murcia", "CD Estepona vs Linense", "Aguilas CF vs Linense", "Xerez CD vs Linense", "Linense vs Xerez D. FC", "UCAM Murcia vs Linense", "Linense vs CD Estepona", "Linense vs Aguilas CF", "Linense vs Xerez CD", "Xerez D. FC vs Orihuela", "Orihuela vs UCAM Murcia", "CD Estepona vs Orihuela", "Xerez CD vs Orihuela", "Orihuela vs Aguilas CF", "Orihuela vs Xerez D. FC", "UCAM Murcia vs Orihuela", "Orihuela vs CD Estepona", "Orihuela vs Xerez CD", "Aguilas CF vs Orihuela", "San Fernando vs Aguilas CF", "San Fernando vs Xerez CD", "San Fernando vs Xerez D. FC", "UCAM Murcia vs San Fernando", "San Fernando vs CD Estepona", "Aguilas CF vs San Fernando", "Xerez CD vs San Fernando", "Xerez D. FC vs San Fernando", "San Fernando vs UCAM Murcia", "CD Estepona vs San Fernando", "Xerez D. FC vs Torremolinos", "Torremolinos vs UCAM Murcia", "CD Estepona vs Torremolinos", "Xerez CD vs Torremolinos", "Torremolinos vs Aguilas CF", "Torremolinos vs Xerez D. FC", "UCAM Murcia vs Torremolinos", "Torremolinos vs CD Estepona", "Torremolinos vs Xerez CD", "Aguilas CF vs Torremolinos", "Villanovense vs Xerez D. FC", "UCAM Murcia vs Villanovense", "Villanovense vs CD Estepona", "Villanovense vs Aguilas CF", "Xerez CD vs Villanovense", "Xerez D. FC vs Villanovense", "Villanovense vs UCAM Murcia", "CD Estepona vs Villanovense", "Aguilas CF vs Villanovense", "Villanovense vs Xerez CD"], "teams_affected": ["<PERSON>", "Aguilas CF", "Almeria B", "<PERSON><PERSON>", "Xerez CD", "<PERSON><PERSON>", "Granada B", "La Union", "San Fernando", "Linense", "Torremolinos", "Xerez D. FC", "Linares", "Villanovense", "Cadiz B", "UCAM Murcia", "CD Estepona", "Orihuela"], "total_matches": 429, "valid_matches": 302, "skipped_matches": 127}, "ITALY_SERIE_D_GROUP_G": {"warning_count": 34, "missing_matches": ["<PERSON><PERSON> vs CynthiAlbalonga", "CynthiAlbalonga vs <PERSON><PERSON>", "Anzio vs CynthiAlbalonga", "CynthiAlbalonga vs Anzio", "CynthiAlbalonga vs Atletico Uri", "Atletico Uri vs CynthiAlbalonga", "Cassino vs CynthiAlbalonga", "CynthiAlbalonga vs Cassino", "Gelbison vs CynthiAlbalonga", "CynthiAlbalonga vs Gelbison", "CynthiAlbalonga vs Guidonia", "Guidonia vs CynthiAlbalonga", "CynthiAlbalonga vs Ilvamaddalena", "Ilvamaddalena vs CynthiAlbalonga", "Latte Dolce vs CynthiAlbalonga", "CynthiAlbalonga vs Latte Dolce", "CynthiAlbalonga vs Olbia", "Olbia vs CynthiAlbalonga", "CynthiAlbalonga vs Paganese", "Paganese vs CynthiAlbalonga", "Puteolana vs CynthiAlbalonga", "CynthiAlbalonga vs Puteolana", "Cynthi<PERSON><PERSON>balonga vs <PERSON><PERSON>", "<PERSON><PERSON> vs CynthiAlbalonga", "CynthiAlbalonga vs S. <PERSON>", "<PERSON><PERSON> vs CynthiAlbalonga", "CynthiAlbalonga vs Sarnese", "Sarnese vs CynthiAlbalonga", "CynthiAlbalonga vs Savoia", "Savoia vs CynthiAlbalonga", "Terracina vs CynthiAlbalonga", "CynthiAlbalonga vs Terracina", "Trastevere vs CynthiAlbalonga", "CynthiAlbalonga vs Trastevere"], "teams_affected": ["Atletico Uri", "S. <PERSON>", "Anzio", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Savoia", "<PERSON><PERSON><PERSON>", "Paganese", "<PERSON>cin<PERSON>", "<PERSON><PERSON>", "Trastevere", "<PERSON><PERSON><PERSON>", "Cassino", "Guidonia", "Olbia", "CynthiAlbalonga"], "total_matches": 578, "valid_matches": 544, "skipped_matches": 34}, "TURKEY_3_LIG_GROUP_4": {"warning_count": 96, "missing_matches": ["K. Istiklalspor vs Agri 1970", "<PERSON>din BB vs Agri 1970", "Agri 1970 vs Coruhlu", "Erciyes vs Agri 1970", "Agri 1970 vs K. Istiklalspor", "Agri 1970 vs Mardin BB", "Coruhlu vs Agri 1970", "Agri 1970 vs Erciyes", "Denizlispor vs Coruhlu", "Erciyes vs Denizlispor", "Denizlispor vs K. Istiklalspor", "Mardin BB vs Denizlispor", "Coruhlu vs Denizlispor", "Denizlispor vs Erciyes", "K. Istiklalspor vs Denizlispor", "Denizlispor vs Mardin BB", "Edirnespor vs K. Istiklalspor", "Coruhlu vs Edirnespor", "Erciyes vs Edirnespor", "Mardin BB vs Edirnespor", "K. Istiklalspor vs Edirnespor", "Edirnespor vs Coruhlu", "Edirnespor vs Erciyes", "Edirnespor vs Mardin BB", "Kirikkale B. vs K. Istiklalspor", "Mardin BB vs Kirikkale B.", "Kirikkale B. vs Coruhlu", "Erciyes vs Kirikkale B.", "K. Istiklalspor vs Kirikkale B.", "Kirikkale B. vs Mardin BB", "Coruhlu vs Kirikkale B.", "Kirikkale B. vs Erciyes", "Mardin BB vs Nigde Belediyes", "Coruhlu vs Nigde Belediyes", "Nigde Belediyes vs Erciyes", "K. Istiklalspor vs Nigde Belediyes", "Nigde Belediyes vs Mardin BB", "Nigde Belediyes vs Coruhlu", "Erciyes vs Nigde Belediyes", "Nigde Belediyes vs K. Istiklalspor", "Mardin BB vs Nilufer B.", "Nilufer B. vs Coruhlu", "Nilufer B. vs Erciyes", "K. Istiklalspor vs Nilufer B.", "Nilufer B. vs Mardin BB", "Erciyes vs Nilufer B.", "Coruhlu vs Nilufer B.", "Nilufer B. vs K. Istiklalspor", "Orduspor 1967 vs Coruhlu", "Erciyes vs Orduspor 1967", "Orduspor 1967 vs K. Istiklalspor", "Orduspor 1967 vs Mardin BB", "Coruhlu vs Orduspor 1967", "K. Istiklalspor vs Orduspor 1967", "Orduspor 1967 vs Erciyes", "Mardin BB vs Orduspor 1967", "Polatli B. vs Mardin BB", "Coruhlu vs Polatli B.", "Erciyes vs Polatli B.", "Polatli B. vs K. Istiklalspor", "Mardin BB vs Polatli B.", "Polatli B. vs Coruhlu", "Polatli B. vs Erciyes", "K. Istiklalspor vs Polatli B.", "Erciyes vs Sebat G.", "Sebat G. vs K. Istiklalspor", "<PERSON>din BB vs Sebat G.", "<PERSON><PERSON> G. vs Coruhlu", "<PERSON><PERSON> G. vs Erciyes", "K. Istiklalspor vs Sebat G.", "Sebat G. vs Mardin BB", "Coruhlu vs Sebat G.", "Tepecik vs Mardin BB", "Coruhlu vs Tepecik", "Tepecik vs Erciyes", "Tepecik vs K. Istiklalspor", "Tepecik vs Coruhlu", "<PERSON><PERSON> BB vs Tepecik", "Erciyes vs Tepecik", "K. Istiklalspor vs Tepecik", "Turgutluspor vs Erciyes", "K. Istiklalspor vs Turgutluspor", "Turgutluspor vs Mardin BB", "Coruhlu vs Turgutluspor", "Erciyes vs Turgutluspor", "Turgutluspor vs K. Istiklalspor", "Mardin BB vs Turgutluspor", "Turgutluspor vs Coruhlu", "Z. Komurspor vs Erciyes", "K. Istiklalspor vs Z. Komurspor", "Z. Komurspor vs Mardin BB", "Coruhlu vs Z. Komurspor", "Erciyes vs Z. Komurspor", "Z. Komurspor vs K. Istiklalspor", "Mardin BB vs Z. Komurspor", "Z. Komurspor vs Coruhlu"], "teams_affected": ["Denizlispor", "Z. Ko<PERSON>por", "K. Istiklalspor", "Agri 1970", "<PERSON><PERSON><PERSON><PERSON>", "Kirikkale B.", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Nilufer B.", "Polatli B.", "Sebat G.", "Tepecik", "Edirnespor", "Turgutluspor", "<PERSON><PERSON> BB", "Orduspor 1967"], "total_matches": 360, "valid_matches": 264, "skipped_matches": 96}, "PARAGUAY_PRIMERA_DIVISION": {"warning_count": 1, "missing_matches": ["2 de Mayo vs Cerro <PERSON>eno"], "teams_affected": ["2 de Mayo", "<PERSON><PERSON>"], "total_matches": 14, "valid_matches": 13, "skipped_matches": 1}, "ITALY_SERIE_D_GROUP_H": {"warning_count": 84, "missing_matches": ["<PERSON><PERSON> vs USD Palmese", "Citta di Fasano vs <PERSON><PERSON>", "Costa DAmalfi vs <PERSON><PERSON>", "USD Palmese vs Angri <PERSON>", "<PERSON><PERSON> vs Citta di Fasano", "Brindisi vs USD Palmese", "Brindisi vs Costa DAmalfi", "Brindisi vs Citta di Fasano", "USD Palmese vs Brindisi", "Costa DAmalfi vs Brindisi", "Citta di Fasano vs Brindisi", "Casarano vs Costa DAmalfi", "Casarano vs USD Palmese", "Citta di Fasano vs Casarano", "Costa DAmalfi vs Casarano", "USD Palmese vs Casarano", "Casarano vs Citta di Fasano", "Citta di Fasano vs Fidelis Andria", "Costa DAmalfi vs Fidelis Andria", "<PERSON><PERSON>is <PERSON>ria vs USD Palmese", "Fidelis Andria vs Citta di Fasano", "<PERSON>delis Andria vs Costa DAmalfi", "Costa DAmalfi vs Francavilla", "Francavilla vs USD Palmese", "Citta di Fasano vs Francavilla", "Francavilla vs Costa DAmalfi", "USD Palmese vs Francavilla", "Gravina vs USD Palmese", "Citta di Fasano vs Gravina", "Gravina vs Costa DAmalfi", "USD Palmese vs Gravina", "Gravina vs Citta di Fasano", "Costa DAmalfi vs Gravina", "Ischia vs Citta di Fasano", "Costa DAmalfi vs Ischia", "USD Palmese vs Ischia", "Citta di Fasano vs Ischia", "Ischia vs Costa DAmalfi", "Manfredonia vs Citta di Fasano", "Costa DAmalfi vs Manfredonia", "Manfredonia vs USD Palmese", "Citta di Fasano vs Manfredonia", "Manfredonia vs Costa DAmalfi", "USD Palmese vs Manfredonia", "Martina Franca vs Costa DAmalfi", "USD Palmese vs Martina Franca", "Citta di Fasano vs Martina Franca", "Costa DAmalfi vs Martina Franca", "Martina Franca vs USD Palmese", "Martina Franca vs Citta di Fasano", "USD Palmese vs Matera", "Matera vs Citta di Fasano", "Matera vs Costa DAmalfi", "Matera vs USD Palmese", "Citta di Fasano vs Matera", "Costa DAmalfi vs Matera", "Costa DAmalfi vs Nardo", "USD Palmese vs Nardo", "<PERSON><PERSON> vs Citta di Fasano", "<PERSON><PERSON> vs Costa DAmalfi", "<PERSON>rdo vs USD Palmese", "Costa DAmalfi vs Nocerina", "USD Palmese vs Nocerina", "Nocerina vs Citta di Fasano", "Nocerina vs Costa DAmalfi", "Nocerina vs USD Palmese", "Citta di Fasano vs Nocerina", "USD Palmese vs Real Acerrana", "Real Acerrana vs Citta di Fasano", "Real Acerrana vs Costa DAmalfi", "Real Acerrana vs USD Palmese", "Citta di Fasano vs Real Acerrana", "Costa DAmalfi vs Ugento", "Ugento vs USD Palmese", "Ugento vs Citta di Fasano", "Ugento vs Costa DAmalfi", "USD Palmese vs Ugento", "Citta di Fasano vs Ugento", "V. Francavilla vs USD Palmese", "V. Francavilla vs Costa DAmalfi", "Citta di Fasano vs V. Francavilla", "USD Palmese vs V. Francavilla", "Costa DAmalfi vs V. Francavilla", "V. Francavilla vs Citta di Fasano"], "teams_affected": ["Costa DAmalfi", "USD Palmese", "<PERSON><PERSON>", "<PERSON><PERSON>", "Citta di Fasano", "Gravina", "Ischia", "Ugento", "Brindisi", "Nocer<PERSON>", "Manfredonia", "Francavilla", "Real Acerrana", "V. <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "total_matches": 480, "valid_matches": 396, "skipped_matches": 84}, "GERMANY_REGIONALLIGA_NORDOST": {"warning_count": 81, "missing_matches": ["Altglienicke vs BFC Dynamo", "Chemnitzer FC vs Altglienicke", "RW Erfurt vs Altglienicke", "BFC Dynamo vs Altglienicke", "Altglienicke vs Chemnitzer FC", "Babelsberg vs BFC Dynamo", "Chemnitzer FC vs Babelsberg", "RW Erfurt vs Babelsberg", "BFC Dynamo vs Babelsberg", "Babelsberg vs RW Erfurt", "Babelsberg vs Chemnitzer FC", "BFC Dynamo vs Carl Zeiss Jena", "Carl Zeiss Jena vs Chemnitzer FC", "<PERSON> vs R<PERSON>", "<PERSON> vs BFC Dynamo", "Chemnitzer FC vs Carl Zeiss Jena", "<PERSON><PERSON> vs <PERSON>", "Chemie Leipzig vs BFC Dynamo", "Chemnitzer FC vs Chemie Leipzig", "RW Erfurt vs Chemie Leipzig", "BFC Dynamo vs Chemie Leipzig", "Chemie Leipzig vs Chemnitzer FC", "Chemie Leipzig vs RW Erfurt", "RW Erfurt vs Eilenburg", "Eilenburg vs BFC Dynamo", "Chemnitzer FC vs Eilenburg", "Eilenburg vs RW Erfurt", "BFC Dynamo vs Eilenburg", "Greifswald vs BFC Dynamo", "Greifswald vs Chemnitzer FC", "Greifswald vs RW Erfurt", "BFC Dynamo vs Greifswald", "Chemnitzer FC vs Greifswald", "RW Erfurt vs Greifswald", "BFC Dynamo vs H. Zehlendorf", "Chemnitzer FC vs H. Zehlendorf", "<PERSON><PERSON> vs <PERSON><PERSON>", "H. Zehlendorf vs Chemnitzer FC", "<PERSON><PERSON> vs BFC Dynamo", "<PERSON><PERSON> vs RW Erfurt", "Chemnitzer FC vs Hallescher", "RW Erfurt vs Hallescher", "Hallescher vs BFC Dynamo", "Hallescher vs Chemnitzer FC", "<PERSON>scher vs RW Erfurt", "Hertha Berlin B vs Chemnitzer FC", "Hertha Berlin B vs RW Erfurt", "BFC Dynamo vs Hertha Berlin B", "R<PERSON> vs Hertha Berlin B", "Chemnitzer FC vs Hertha Berlin B", "BFC Dynamo vs Lok. Leipzig", "Lok. Leipzig vs Chemnitzer FC", "Lok. Leipzig vs RW Erfurt", "Lok. Leipzig vs BFC Dynamo", "Luckenwalde vs BFC Dynamo", "Luckenwalde vs Chemnitzer FC", "Luckenwalde vs RW Erfurt", "BFC Dynamo vs Luckenwalde", "Chemnitzer FC vs Luckenwalde", "BFC Dynamo vs Meuselwitz", "Meuselwitz vs Chemnitzer FC", "<PERSON><PERSON><PERSON><PERSON> vs RW Erfurt", "Meuselwitz vs BFC Dynamo", "Chemnitzer FC vs Meuselwitz", "RW Erfurt vs Meuselwitz", "Plauen vs BFC Dynamo", "Chemnitzer FC vs Plauen", "<PERSON><PERSON><PERSON> vs RW Erfurt", "BFC Dynamo vs Plauen", "Plauen vs Chemnitzer FC", "RW <PERSON> vs Plauen", "Viktoria Berlin vs RW Erfurt", "BFC Dynamo vs Viktoria Berlin", "RW Erfurt vs Viktoria Berlin", "Viktoria Berlin vs Chemnitzer FC", "Viktoria Berlin vs BFC Dynamo", "Chemnitzer FC vs Zwickau", "RW Erfurt vs Zwickau", "Zwickau vs BFC Dynamo", "Zwickau vs Chemnitzer FC", "Zwickau vs RW Erfurt"], "teams_affected": ["<PERSON><PERSON><PERSON><PERSON>", "Eilenburg", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "BFC Dynamo", "<PERSON><PERSON>", "Hertha Berlin B", "Greifswald", "Viktoria Berlin", "Chemie Leipzig", "RW Erfurt", "<PERSON>", "Lok. Leipzig", "Chemnitzer FC", "Luckenwalde", "Babelsberg", "Zwickau"], "total_matches": 465, "valid_matches": 384, "skipped_matches": 81}, "NORWAY_DIVISION_2_GROUP_1": {"warning_count": 1, "missing_matches": ["Sandviken vs Flekkeroy"], "teams_affected": ["<PERSON><PERSON><PERSON><PERSON>", "Sandviken"], "total_matches": 6, "valid_matches": 5, "skipped_matches": 1}, "NORTHERN_IRELAND_PREMIERSHIP_WOMEN": {"warning_count": 5, "missing_matches": ["Lisburn LFC W vs Glentoran W", "Lisburn LFC W vs Linfield W", "Linfield W vs Mid-Ulster W", "Linfield W vs Lisburn LFC W", "Mid-Ulster W vs Linfield W"], "teams_affected": ["Mid-Ulster W", "Lisburn LFC W", "Linfield W", "Glentoran W"], "total_matches": 18, "valid_matches": 13, "skipped_matches": 5}, "USA_NWSL": {"warning_count": 92, "missing_matches": ["Angel City W vs Gotham FC W", "Bay FC W vs Angel City W", "Angel City W vs Chicago RS W", "KC Current W vs Angel City W", "Utah Royals W vs Bay FC W", "Bay FC W vs R. Louisville W", "Washington S. W vs Bay FC W", "Bay FC W vs Chicago RS W", "N. Carolina W vs Bay FC W", "Bay FC W vs Seattle Reign W", "San Diego W. W vs Bay FC W", "KC Current W vs Bay FC W", "Bay FC W vs Angel City W", "Houston Dash W vs Bay FC W", "Bay FC W vs Portland T. W", "Bay FC W vs Orlando Pride W", "Gotham FC W vs Bay FC W", "Orlando Pride W vs Chicago RS W", "Chicago RS W vs Houston Dash W", "Chicago RS W vs R. Louisville W", "Bay FC W vs Chicago RS W", "Utah Royals W vs Chicago RS W", "Chicago RS W vs San Diego W. W", "Gotham FC W vs Chicago RS W", "Chicago RS W vs Washington S. W", "N. Carolina W vs Chicago RS W", "Chicago RS W vs KC Current W", "Angel City W vs Chicago RS W", "Chicago RS W vs Seattle Reign W", "Portland T. W vs Chicago RS W", "Seattle Reign W vs Gotham FC W", "Gotham FC W vs Orlando Pride W", "Houston Dash W vs Gotham FC W", "Gotham FC W vs N. Carolina W", "Angel City W vs Gotham FC W", "Portland T. W vs Gotham FC W", "Washington S. W vs Gotham FC W", "Gotham FC W vs Chicago RS W", "R. Louisville W vs Gotham FC W", "Gotham FC W vs San Diego W. W", "Gotham FC W vs KC Current W", "Utah Royals W vs Gotham FC W", "Gotham FC W vs Bay FC W", "Chicago RS W vs Houston Dash W", "Houston Dash W vs Gotham FC W", "KC Current W vs Houston Dash W", "Houston Dash W vs Bay FC W", "KC Current W vs Portland T. W", "Washington S. W vs KC Current W", "KC Current W vs Utah Royals W", "San Diego W. W vs KC Current W", "KC Current W vs Houston Dash W", "N. Carolina W vs KC Current W", "Seattle Reign W vs KC Current W", "KC Current W vs Bay FC W", "Orlando Pride W vs KC Current W", "Chicago RS W vs KC Current W", "Gotham FC W vs KC Current W", "KC Current W vs R. Louisville W", "KC Current W vs Angel City W", "Gotham FC W vs N. Carolina W", "N. Carolina W vs Bay FC W", "N. Carolina W vs KC Current W", "N. Carolina W vs Chicago RS W", "Orlando Pride W vs Chicago RS W", "Gotham FC W vs Orlando Pride W", "Orlando Pride W vs KC Current W", "Bay FC W vs Orlando Pride W", "KC Current W vs Portland T. W", "Portland T. W vs Gotham FC W", "Bay FC W vs Portland T. W", "Portland T. W vs Chicago RS W", "Bay FC W vs R. Louisville W", "Chicago RS W vs R. Louisville W", "R. Louisville W vs Gotham FC W", "KC Current W vs R. Louisville W", "San Diego W. W vs KC Current W", "Chicago RS W vs San Diego W. W", "San Diego W. W vs Bay FC W", "Gotham FC W vs San Diego W. W", "Seattle Reign W vs Gotham FC W", "Bay FC W vs Seattle Reign W", "Seattle Reign W vs KC Current W", "Chicago RS W vs Seattle Reign W", "Utah Royals W vs Bay FC W", "KC Current W vs Utah Royals W", "Utah Royals W vs Chicago RS W", "Utah Royals W vs Gotham FC W", "Washington S. W vs KC Current W", "Washington S. W vs Bay FC W", "Washington S. W vs Gotham FC W", "Chicago RS W vs Washington S. W"], "teams_affected": ["Chicago RS W", "R. Louisville W", "Bay FC W", "Portland T. W", "Washington S. W", "N. Carolina W", "San Diego W. W", "Utah Royals W", "Orlando Pride W", "KC Current W", "Angel City W", "Seattle Reign W", "Gotham FC W", "Houston Dash W"], "total_matches": 182, "valid_matches": 90, "skipped_matches": 92}, "GERMANY_OBERLIGA_HAMBURG": {"warning_count": 136, "missing_matches": ["Hamburger SV C vs Alsterbruder", "Alsterbruder vs ETSV Hamburg", "HEBC vs Al<PERSON>bruder", "Alsterbruder vs Halstenbek-R.", "Alsterbruder vs Niendorfer TSV", "Al<PERSON>bruder vs Vorwarts-<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs Al<PERSON>bruder", "Alsterbruder vs Hamburger SV C", "ETSV Hamburg vs Alsterbruder", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs HEBC", "Niendorfer TSV vs Alsterbruder", "Vorwarts-<PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON>er", "Altona vs HEBC", "Niendorfer TSV vs Altona", "Altona vs Vorwarts-<PERSON><PERSON><PERSON>", "Altona vs Halstenbek-R.", "Hamburger SV C vs Altona", "Altona vs ETSV Hamburg", "HEBC vs Altona", "Altona vs Niendorfer TSV", "Vorwarts-<PERSON><PERSON><PERSON> vs Altona", "<PERSON><PERSON>bek-R. vs Altona", "Buchholz vs Halstenbek-R.", "Hamburger SV C vs Buchholz", "Buchholz vs ETSV Hamburg", "HEBC vs Buchholz", "Buchholz vs Niendorfer TSV", "Vorwarts-<PERSON><PERSON><PERSON> vs Buchholz", "Buchholz vs HEBC", "Niendorfer TSV vs Buchholz", "ETSV Hamburg vs Buchholz", "Buchholz vs Vorwarts-<PERSON><PERSON><PERSON>", "Halstenbek-R<PERSON> vs Buchholz", "Vorwarts-<PERSON><PERSON><PERSON> vs Dassendorf", "Dassendorf vs Halstenbek-R.", "Hamburger SV C vs Dassendorf", "Dassendorf vs ETSV Hamburg", "HEBC vs Dassendorf", "Dassendorf vs Niendorfer TSV", "Dassendorf vs Vorwarts-Wacker", "Halstenbek-R. vs Dassendorf", "Dassendorf vs Hamburger SV C", "ETSV Hamburg vs Dassendorf", "Dassendorf vs HEBC", "<PERSON><PERSON><PERSON>-<PERSON><PERSON> vs <PERSON><PERSON><PERSON>tel<PERSON>", "ETSV Hamburg vs Eimsbutteler", "Eimsbutteler vs Hamburger SV C", "Eimsbutteler vs HEBC", "Niendorfer TSV vs Eimsbutteler", "<PERSON><PERSON>butteler vs Vorwarts-<PERSON><PERSON><PERSON>", "Eimsbutteler vs Halstenbek-R.", "Eimsbutteler vs ETSV Hamburg", "HEBC vs Eimsbutteler", "Eimsbutteler vs Niendorfer TSV", "Vorwarts-<PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON><PERSON>", "Hamburger SV C vs Eimsbutteler", "Halstenbek-R. vs Harksheide", "Harksheide vs Hamburger SV C", "ETSV Hamburg vs Harksheide", "Harksheide vs HEBC", "Niendorfer TSV vs Harksheide", "Vorwarts-<PERSON><PERSON><PERSON> vs Harksheide", "Harksheide vs Halstenbek-R.", "Hamburger SV C vs Harksheide", "Harksheide vs ETSV Hamburg", "Harksheide vs Niendorfer TSV", "Harksheide vs Vorwarts-<PERSON><PERSON>er", "HEBC vs Harksheide", "<PERSON><PERSON><PERSON> vs Halstenbek-R.", "Hamburger SV C vs Paloma", "Paloma vs ETSV Hamburg", "HEBC vs Paloma", "Paloma vs Niendorfer TSV", "Vorwarts-<PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "<PERSON><PERSON>bek-R<PERSON> vs Paloma", "Paloma vs Hamburger SV C", "ETSV Hamburg vs Paloma", "Paloma vs HEBC", "Niendorfer TSV vs Paloma", "<PERSON><PERSON> vs Halstenbek-R.", "Hamburger SV C vs Sasel", "Sasel vs ETSV Hamburg", "HEBC vs Sasel", "Sasel vs Niendorfer TSV", "Vorwarts-<PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "Sasel vs Hamburger SV C", "Sasel vs HEBC", "Niendorfer TSV vs Sasel", "Halstenbek-R. vs <PERSON><PERSON>", "ETSV Hamburg vs Sasel", "<PERSON><PERSON> vs Vorwarts-<PERSON><PERSON><PERSON>", "Halstenbek-R. vs Suderelbe", "Suderelbe vs Hamburger SV C", "ETSV Hamburg vs Suderelbe", "Suderelbe vs HEBC", "Niendorfer TSV vs Suderelbe", "Suderelbe vs Vorwarts-<PERSON><PERSON><PERSON>", "Suderelbe vs Halstenbek-R.", "Hamburger SV C vs Suderelbe", "Suderelbe vs ETSV Hamburg", "HEBC vs Suderelbe", "Suderelbe vs Niendorfer TSV", "HEBC vs T. Wilhelmsburg", "T. Wilhelmsburg vs Niendorfer TSV", "Vorwarts-<PERSON><PERSON><PERSON> vs T. Wilhelm<PERSON>", "<PERSON><PERSON><PERSON>-<PERSON><PERSON> vs T<PERSON> Wilhelmsburg", "Hamburger SV C vs T. Wilhelmsburg", "ETSV Hamburg vs T. Wilhelmsburg", "T. Wilhelmsburg vs HEBC", "Niendorfer TSV vs T. Wilhelmsburg", "T. Wilhelmsburg vs Halstenbek-R.", "T. Wilhelmsburg vs Vorwarts-<PERSON><PERSON>er", "T. Wilhelmsburg vs Hamburger SV C", "HEBC vs Victoria H. B", "Niendorfer TSV vs Victoria H. B", "Victoria H. B vs Halstenbek-R.", "Vorwarts-<PERSON><PERSON><PERSON> vs Victoria H. B", "Victoria H. B vs Hamburger SV C", "Victoria H. B vs ETSV Hamburg", "Victoria H. B vs HEBC", "<PERSON><PERSON><PERSON>-<PERSON><PERSON> vs Victoria H. B", "Victoria H. B vs Vorwarts-<PERSON><PERSON><PERSON>", "Hamburger SV C vs Victoria H. B", "ETSV Hamburg vs Victoria H. B", "<PERSON><PERSON> Concordia vs Vorwarts-<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>-<PERSON><PERSON> vs W. Concordia", "W. Concordia vs Hamburger SV C", "ETSV Hamburg vs W. Concordia", "W. Concordia vs HEBC", "Niendorfer TSV vs W. Concordia", "Vorwarts-<PERSON><PERSON><PERSON> vs W. Concordia", "<PERSON><PERSON> Concord<PERSON> vs Halstenbek-R.", "Hamburger SV C vs W. Concordia", "W. Concordia vs ETSV Hamburg", "HEBC vs W. Concordia", "W. Concordia vs Niendorfer TSV"], "teams_affected": ["Niendorfer TSV", "Hamburger SV C", "Sasel", "<PERSON>or<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "ETSV Hamburg", "Suderelbe", "Altona", "Buchholz", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Harksheide", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "T. <PERSON>", "<PERSON><PERSON><PERSON>-R<PERSON>", "W. <PERSON>", "HEBC", "Victoria H. B", "Dassendorf"], "total_matches": 382, "valid_matches": 246, "skipped_matches": 136}, "CHILE_PRIMERA_B": {"warning_count": 108, "missing_matches": ["Antofagasta vs Concepcion", "Cobreloa vs Antofagasta", "Antofagasta vs Copiapo", "CD Santa Cruz vs Antofagasta", "CD Santa Cruz vs Rangers", "Recoleta vs CD Santa Cruz", "San Luis vs CD Santa Cruz", "CD Santa Cruz vs Concepcion", "U. Concepcion vs CD Santa Cruz", "CD Santa Cruz vs S. Morning", "Curico Unido vs CD Santa Cruz", "CD Santa Cruz vs Copiapo", "Magallanes vs CD Santa Cruz", "CD Santa Cruz vs S. Wanderers", "Cobreloa vs CD Santa Cruz", "CD Santa Cruz vs U. San Felipe", "San Marcos vs CD Santa Cruz", "CD Santa Cruz vs Antofagasta", "CD Santa Cruz vs Deportes Temuco", "Cobreloa vs U. San Felipe", "San Marcos vs Cobreloa", "Cobreloa vs U. Concepcion", "Rangers vs Cobreloa", "Cobreloa vs Antofagasta", "Cobreloa vs Magallanes", "Copiapo vs Cobreloa", "San Luis vs Cobreloa", "Cobreloa vs Concepcion", "Recoleta vs Cobreloa", "Cobreloa vs CD Santa Cruz", "Deportes Temuco vs Cobreloa", "S. Wanderers vs Cobreloa", "Cobreloa vs Curico Unido", "Cobreloa vs S. Morning", "Concepcion vs Deportes Temuco", "Antofagasta vs Concepcion", "Concepcion vs Recoleta", "CD Santa Cruz vs Concepcion", "Curico Unido vs Concepcion", "Concepcion vs Copiapo", "U. Concepcion vs Concepcion", "S. Morning vs Concepcion", "Cobreloa vs Concepcion", "Concepcion vs San Marcos", "Magallanes vs Concepcion", "Concepcion vs S. Wanderers", "Concepcion vs Rangers", "U. San Felipe vs Concepcion", "San Luis vs Concepcion", "Copia<PERSON> vs S. Morning", "Curico Unido vs Copiapo", "Copiapo vs Rangers", "U. San Felipe vs Copiapo", "Copiapo vs S. Wanderers", "Concepcion vs Copiapo", "Copiapo vs Cobreloa", "CD Santa Cruz vs Copiapo", "Copiapo vs Deportes Temuco", "Antofagasta vs Copiapo", "Copiapo vs Recoleta", "San Marcos vs Copiapo", "Copiapo vs San Luis", "U. Concepcion vs Copiapo", "Magallanes vs Copiapo", "Curico Unido vs Copiapo", "Curico Unido vs Concepcion", "Curico Unido vs CD Santa Cruz", "Cobreloa vs Curico Unido", "Concepcion vs Deportes Temuco", "Copiapo vs Deportes Temuco", "Deportes Temuco vs Cobreloa", "CD Santa Cruz vs Deportes Temuco", "Cobreloa vs Magallanes", "Magallanes vs CD Santa Cruz", "Magallanes vs Concepcion", "Magallanes vs Copiapo", "CD Santa Cruz vs Rangers", "Copiapo vs Rangers", "Rangers vs Cobreloa", "Concepcion vs Rangers", "Recoleta vs CD Santa Cruz", "Concepcion vs Recoleta", "Recoleta vs Cobreloa", "Copiapo vs Recoleta", "Copia<PERSON> vs S. Morning", "CD Santa Cruz vs S. Morning", "S. Morning vs Concepcion", "Cobreloa vs S. Morning", "Copiapo vs S. Wanderers", "CD Santa Cruz vs S. Wanderers", "Concepcion vs S. Wanderers", "S. Wanderers vs Cobreloa", "San Luis vs CD Santa Cruz", "San Luis vs Cobreloa", "Copiapo vs San Luis", "San Luis vs Concepcion", "San Marcos vs Cobreloa", "Concepcion vs San Marcos", "San Marcos vs Copiapo", "San Marcos vs CD Santa Cruz", "Cobreloa vs U. Concepcion", "U. Concepcion vs CD Santa Cruz", "U. Concepcion vs Concepcion", "U. Concepcion vs Copiapo", "Cobreloa vs U. San Felipe", "U. San Felipe vs Copiapo", "CD Santa Cruz vs U. San Felipe", "U. San Felipe vs Concepcion"], "teams_affected": ["U. Concepcion", "Concepcion", "San Luis", "Curico Unido", "Cobreloa", "S. Wanderers", "<PERSON><PERSON><PERSON>", "S. Morning", "U. San Felipe", "Deportes Temuco", "Rangers", "Recoleta", "San Marcos", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Magallanes", "CD Santa Cruz"], "total_matches": 240, "valid_matches": 132, "skipped_matches": 108}, "GERMANY_REGIONALLIGA_BAYERN": {"warning_count": 31, "missing_matches": ["Hankofen-H. vs Ansbach", "Ansbach vs Hankofen-H.", "Aschaffenburg vs Hankofen-H.", "Hankofen-H. vs Aschaffenburg", "Aubstadt vs Hankofen-H.", "Hankofen-H. vs Augsburg B", "Augsburg B vs Hankofen-H.", "Hankofen-H. vs Bayern Munich B", "Bayern Munich B vs Hankofen-H.", "Bayreuth vs Hankofen-H.", "Hankofen-H. vs Buchbach", "<PERSON><PERSON> vs Hankofen-H.", "Hankofen-<PERSON>. vs <PERSON><PERSON>", "Greuther F. B vs Hankofen-H.", "Hankofen-H. vs Greuther F. B", "Hankofen-H. vs <PERSON><PERSON><PERSON>sen", "<PERSON><PERSON><PERSON><PERSON> vs Hankofen-H.", "Nurnberg B vs Hankofen-H.", "Hankofen-H. vs Nurnberg B", "Hankofen-H. vs Schwaben A.", "Schwaben A. vs Hankofen-H.", "Hankofen-H. vs Schweinfurt", "Schweinfurt vs Hankofen-H.", "Turkgu<PERSON> M. vs Hankofen-H.", "Hankofen-H. vs Turkgucu M.", "Vilzing vs Hankofen-H.", "Hankofen-H. vs Vilzing", "<PERSON><PERSON> vs Hankofen-H.", "Hankofen-H. vs <PERSON><PERSON>", "Hankofen-H. vs Wurzburger K.", "Wurzburger K. vs Hankofen-H."], "teams_affected": ["<PERSON><PERSON>", "V<PERSON><PERSON>", "Buchbach", "Nurnberg B", "<PERSON><PERSON><PERSON><PERSON> M.", "Schwaben A.", "Hankofen-H.", "Bayreuth", "Ansbach", "Aubstadt", "Schweinfurt", "<PERSON><PERSON>", "Augsburg B", "Greuther F. B", "Wurzburger K.", "<PERSON><PERSON><PERSON><PERSON>", "Aschaffenburg", "Bayern Munich B"], "total_matches": 525, "valid_matches": 494, "skipped_matches": 31}, "USA_MAJOR_LEAGUE_SOCCER": {"warning_count": 11, "missing_matches": ["Austin vs Sporting KC", "Los Angeles FC vs Austin", "<PERSON> vs San Diego", "Austin vs LA Galaxy", "SJ Earthquakes vs Colorado Rapids", "Sporting KC vs Minnesota Utd", "Orlando City vs DC United", "San Diego vs St. Louis City", "LA Galaxy vs St. Louis City", "Sporting KC vs St. Louis City", "Vancouver vs LA Galaxy"], "teams_affected": ["Austin", "Orlando City", "San Diego", "Vancouver", "DC United", "Colorado Rapids", "Sporting KC", "LA Galaxy", "SJ Earthquakes", "St. Louis City", "Los Angeles FC", "Minnesota Utd"], "total_matches": 36, "valid_matches": 25, "skipped_matches": 11}, "ZAMBIA_SUPER_LEAGUE": {"warning_count": 87, "missing_matches": ["Atletico Lusaka vs NAPSA Stars", "MUZA vs Atletico Lusaka", "Atletico Lusaka vs ZESCO Utd", "NAPSA Stars vs Atletico Lusaka", "Atletico Lusaka vs MUZA", "Forest Rangers vs MUZA", "Forest Rangers vs ZESCO Utd", "NAPSA Stars vs Forest Rangers", "MUZA vs Forest Rangers", "ZESCO Utd vs Forest Rangers", "Forest Rangers vs NAPSA Stars", "Green Buffaloes vs MUZA", "Green Buffaloes vs ZESCO Utd", "NAPSA Stars vs Green Buffaloes", "MUZA vs Green Buffaloes", "ZESCO Utd vs Green Buffaloes", "Green Buffaloes vs NAPSA Stars", "ZESCO Utd vs Green Eagles", "Green Eagles vs NAPSA Stars", "MUZA vs Green Eagles", "Green Eagles vs ZESCO Utd", "NAPSA Stars vs Green Eagles", "MUZA vs Indeni", "ZESCO Utd vs Indeni", "Indeni vs NAPSA Stars", "Indeni vs MUZA", "Indeni vs ZESCO Utd", "NAPSA Stars vs Indeni", "Kabwe Warriors vs MUZA", "ZESCO Utd vs Kabwe Warriors", "Kabwe Warriors vs NAPSA Stars", "MUZA vs Kabwe Warriors", "Kabwe Warriors vs ZESCO Utd", "NAPSA Stars vs Kabwe Warriors", "MUZA vs L. Radiants", "L. Radiants vs ZESCO Utd", "NAPSA Stars vs L. Radiants", "L. Ra<PERSON> vs MUZA", "ZESCO Utd vs L. Radiants", "<PERSON>. Radiants vs NAPSA Stars", "MUZA vs Mufulira", "ZESCO Utd vs Mufulira", "<PERSON><PERSON><PERSON> vs NAPSA Stars", "<PERSON><PERSON>ira vs MUZA", "Mufulira vs ZESCO Utd", "NAPSA Stars vs Mufulira", "Mutondo Stars vs MUZA", "ZESCO Utd vs Mutondo Stars", "Mutondo Stars vs NAPSA Stars", "MUZA vs Mutondo Stars", "Mutondo Stars vs ZESCO Utd", "Nchanga Rangers vs MUZA", "Nchanga Rangers vs ZESCO Utd", "NAPSA Stars vs Nchanga Rangers", "MUZA vs Nchanga Rangers", "ZESCO Utd vs Nchanga Rangers", "Nchanga Rangers vs NAPSA Stars", "Nkana vs ZESCO Utd", "NAPSA Stars vs Nkana", "Nkana vs MUZA", "ZESCO Utd vs Nkana", "Nkana vs NAPSA Stars", "MUZA vs Nkana", "Nkwazi vs NAPSA Stars", "ZESCO Utd vs Nkwazi", "MUZA vs Nkwazi", "Nkwazi vs ZESCO Utd", "NAPSA Stars vs Nkwazi", "Nkwazi vs MUZA", "MUZA vs Power Dynamos", "ZESCO Utd vs Power Dynamos", "Power Dynamos vs NAPSA Stars", "Power Dynamos vs MUZA", "Power Dynamos vs ZESCO Utd", "NAPSA Stars vs Power Dynamos", "Red Arrows vs MUZA", "Red Arrows vs ZESCO Utd", "NAPSA Stars vs Red Arrows", "MUZA vs Red Arrows", "ZESCO Utd vs Red Arrows", "Red Arrows vs NAPSA Stars", "MUZA vs Zanaco", "ZESCO Utd vs Zanaco", "Zanaco vs NAPSA Stars", "Zanaco vs MUZA", "Zanaco vs ZESCO Utd", "NAPSA Stars vs Zanaco"], "teams_affected": ["Green Buffaloes", "Indeni", "Forest Rangers", "<PERSON><PERSON><PERSON>", "Zanaco", "Kabwe Warriors", "Power Dynamos", "<PERSON><PERSON>", "MUZA", "<PERSON><PERSON><PERSON>", "Mutondo Stars", "Atletico Lusaka", "Nchanga Rangers", "ZESCO Utd", "Red Arrows", "NAPSA Stars", "Green Eagles", "Nkwazi"], "total_matches": 495, "valid_matches": 408, "skipped_matches": 87}, "PANAMA_LIGA_PROM": {"warning_count": 30, "missing_matches": ["Alianza vs Costa del Este", "UMECIT vs Alianza", "Costa del Este vs Alianza", "Costa del Este vs Arabe Unido", "Arabe Unido vs UMECIT", "Arabe Unido vs Costa del Este", "UMECIT vs Herrera", "Costa del Este vs Herrera", "Herrera vs UMECIT", "UMECIT vs Independiente", "Costa del Este vs Independiente", "Independiente vs UMECIT", "Plaza Amador vs Costa del Este", "UMECIT vs Plaza Amador", "Costa del Este vs Plaza Amador", "UMECIT vs San Francisco", "San Francisco vs Costa del Este", "San Francisco vs UMECIT", "Costa del Este vs San Miguelito", "San Miguelito vs UMECIT", "San Miguelito vs Costa del Este", "<PERSON><PERSON> vs Costa del Este", "Tauro vs UMECIT", "Costa del Este vs Tauro", "UMECIT vs Universitario", "Costa del Este vs Universitario", "Universitario vs UMECIT", "Veraguas vs UMECIT", "Veraguas vs Costa del Este", "UMECIT vs Veraguas"], "teams_affected": ["UMECIT", "Costa del Este", "Independiente", "San Miguelito", "<PERSON><PERSON>", "Arabe Unido", "Alianza", "San Francisco", "<PERSON>", "Veraguas", "Plaza Amador", "Universitario"], "total_matches": 160, "valid_matches": 130, "skipped_matches": 30}, "SWEDEN_DIV_2_VASTRA_GOTALAND": {"warning_count": 4, "missing_matches": ["As<PERSON>o vs Hestrafor", "Bo<PERSON>jan vs Bergdalens", "Astorps FF vs Jonsered", "Landvetter vs Vastra Frolunda"], "teams_affected": ["Jonsered", "Hestrafor", "Astorps FF", "Bergdalens", "Landvetter", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "total_matches": 6, "valid_matches": 2, "skipped_matches": 4}, "DENMARK_1ST_DIVISION": {"warning_count": 86, "missing_matches": ["B 93 vs Kolding IF", "B 93 vs Odense BK", "HB Koge vs B 93", "B 93 vs AC Horsens", "AC Horsens vs B 93", "Odense BK vs B 93", "Kolding IF vs B 93", "B 93 vs HB Koge", "B 93 vs HB Koge", "Esbjerg vs Odense BK", "E<PERSON>bjerg vs AC Horsens", "Kolding IF vs Esbjerg", "Esbjerg vs HB Koge", "Odense BK vs Esbjerg", "Esbjerg vs Kolding IF", "HB Koge vs Esbjerg", "AC Horsens vs Esbjerg", "Esbjerg vs Kolding IF", "Esbjerg vs Odense BK", "AC Horsens vs Esbjerg", "E<PERSON>bjerg vs AC Horsens", "Kolding IF vs Esbjerg", "AC Horsens vs Fredericia", "Kolding IF vs Fredericia", "Odense BK vs Fredericia", "HB Koge vs Fredericia", "Fredericia vs AC Horsens", "Fredericia vs Odense BK", "Fredericia vs Kolding IF", "Fredericia vs HB Koge", "Kolding IF vs Fredericia", "Fredericia vs Odense BK", "Fredericia vs AC Horsens", "Odense BK vs Fredericia", "AC Horsens vs Hillerod", "Odense BK vs Hillerod", "Hillerod vs HB Koge", "Hillerod vs Kolding IF", "Kolding IF vs Hillerod", "Hillerod vs AC Horsens", "HB Koge vs Hillerod", "Hillerod vs Odense BK", "HB Koge vs Hillerod", "Hillerod vs HB Koge", "Hobro vs HB Koge", "Hobro vs Odense BK", "Hobro vs Kolding IF", "AC Horsens vs Hobro", "HB Koge vs Hobro", "Odense BK vs Hobro", "Hobro vs AC Horsens", "Kolding IF vs Hobro", "HB Koge vs Hobro", "Hvidovre vs HB Koge", "Kolding IF vs Hvidovre", "Odense BK vs Hvidovre", "H<PERSON>ovre vs AC Horsens", "Hvidovre vs Kolding IF", "AC Horsens vs Hvidovre", "HB Koge vs Hvidovre", "Hvidovre vs Odense BK", "AC Horsens vs Hvidovre", "Kolding IF vs Hvidovre", "Hvidovre vs Odense BK", "Odense BK vs Hvidovre", "Hvidovre vs Kolding IF", "Roskilde vs AC Horsens", "Kolding IF vs Roskilde", "Odense BK vs Roskilde", "Roskilde vs HB Koge", "Roskilde vs Odense BK", "Roskilde vs Kolding IF", "AC Horsens vs Roskilde", "HB Koge vs Roskilde", "Roskilde vs HB Koge", "HB Koge vs Roskilde", "Vendsyssel vs Kolding IF", "AC Horsens vs Vendsyssel", "Odense BK vs Vendsyssel", "HB Koge vs Vendsyssel", "Vendsyssel vs HB Koge", "Kolding IF vs Vendsyssel", "Vendsyssel vs Odense BK", "Vendsyssel vs AC Horsens", "HB Koge vs Vendsyssel", "Vendsyssel vs HB Koge"], "teams_affected": ["Roskilde", "Vendsyssel", "Kolding IF", "HB Koge", "Odense BK", "Hillerod", "B 93", "Hobro", "Fredericia", "Esbjerg", "<PERSON><PERSON><PERSON><PERSON>", "AC Horsens"], "total_matches": 240, "valid_matches": 154, "skipped_matches": 86}, "SPAIN_SEGUNDA_RFEF_GROUP_1": {"warning_count": 64, "missing_matches": ["Salamanca CF vs Bergantinos", "Bergantinos vs Marino de L.", "Bergantinos vs Salamanca CF", "Marino de L. vs Bergantinos", "Marino de L. vs Compostela", "Salamanca CF vs Compostela", "Compostela vs Marino de L.", "Compostela vs Salamanca CF", "Coruxo vs Salamanca CF", "Marino de L. vs Coruxo", "Salamanca CF vs Coruxo", "Coruxo vs Marino de L.", "Marino de L. vs D. Fabril", "Salamanca CF vs D. Fabril", "D. Fabril vs Marino de L.", "D. Fabril vs Salamanca CF", "Escobedo vs Salamanca CF", "Marino de L. vs Escobedo", "Salamanca CF vs Escobedo", "Escobedo vs Marino de L.", "Guijuelo vs Salamanca CF", "Marino de L. vs Guijuelo", "Salamanca CF vs Guijuelo", "Guijuelo vs Marino de L.", "Langreo vs Marino de L.", "Langreo vs Salamanca CF", "Marino de L. vs Langreo", "Salamanca CF vs Langreo", "Marino de L. vs Laredo", "Salamanca CF vs Laredo", "Laredo vs Marino de L.", "Laredo vs Salamanca CF", "Llanera vs Salamanca CF", "Marino de L. vs Llanera", "Salamanca CF vs Llanera", "Llanera vs Marino de L.", "Salamanca CF vs Numancia", "Numancia vs Marino de L.", "Numancia vs Salamanca CF", "Marino de L. vs Numancia", "Pontevedra vs Marino de L.", "Pontevedra vs Salamanca CF", "Marino de L. vs Pontevedra", "Salamanca CF vs Pontevedra", "Salamanca CF vs Rayo Cantabria", "Rayo Cantabria vs Marino de L.", "Rayo Cantabria vs Salamanca CF", "Marino de L. vs Rayo Cantabria", "Marino de L. vs Real Avila", "Salamanca CF vs Real Avila", "Real Avila vs Marino de L.", "Real Avila vs Salamanca CF", "Salamanca CF vs Real Aviles", "Real Aviles vs Marino de L.", "Real Aviles vs Salamanca CF", "Marino de L. vs Real Aviles", "Torrelavega vs Marino de L.", "Torrelavega vs Salamanca CF", "Marino de L. vs Torrelavega", "Salamanca CF vs Torrelavega", "Valladolid B vs Marino de L.", "Valladolid B vs Salamanca CF", "Marino de L. vs Valladolid B", "Salamanca CF vs Valladolid B"], "teams_affected": ["Coruxo", "Compostela", "Rayo Cantabria", "<PERSON><PERSON><PERSON>", "Valladolid B", "<PERSON><PERSON><PERSON><PERSON>", "Real Avila", "Langreo", "Real Aviles", "Llanera", "Numancia", "<PERSON><PERSON><PERSON>", "Laredo", "Torrelavega", "<PERSON><PERSON>", "Escobedo", "Salamanca CF", "Marino de L."], "total_matches": 544, "valid_matches": 480, "skipped_matches": 64}, "AUSTRIA_BUNDESLIGA": {"warning_count": 64, "missing_matches": ["Wolfsberger AC vs A. Klagenfurt", "<PERSON><PERSON> vs BW Linz", "Grazer AK vs A. Klagenfurt", "LASK Linz vs A. Klagenfurt", "<PERSON><PERSON> vs Wolfsberger AC", "BW Linz vs A. Klagenfurt", "<PERSON><PERSON> vs Grazer AK", "<PERSON><PERSON> vs LASK Linz", "LASK Linz vs Altach", "Altach vs Wolfsberger AC", "Grazer AK vs Altach", "Altach vs BW Linz", "Altach vs LASK Linz", "Wolfsberger AC vs Altach", "Altach vs Grazer AK", "BW Linz vs Altach", "BW Linz vs Austria Wien", "Austria Wien vs Wolfsberger AC", "Austria Wien vs LASK Linz", "Austria Wien vs Grazer AK", "Austria Wien vs BW Linz", "Wolfsberger AC vs Austria Wien", "LASK Linz vs Austria Wien", "Grazer AK vs Austria Wien", "<PERSON><PERSON> vs LASK Linz", "<PERSON><PERSON> vs Grazer AK", "Wolfsberger AC vs Hartberg", "<PERSON><PERSON> vs BW Linz", "LASK Linz vs Hartberg", "Grazer AK vs Hartberg", "Hartberg vs Wolfsberger AC", "BW Linz vs Hartberg", "BW Linz vs Rapid Wien", "Wolfsberger AC vs Rapid Wien", "Rapid Wien vs LASK Linz", "Grazer AK vs Rapid Wien", "Rapid Wien vs BW Linz", "Rapid Wien vs Wolfsberger AC", "LASK Linz vs Rapid Wien", "Rapid Wien vs Grazer AK", "Grazer AK vs Salzburg", "Salzburg vs BW Linz", "LASK Linz vs Salzburg", "Wolfsberger AC vs Salzburg", "Salzburg vs Grazer AK", "BW Linz vs Salzburg", "Salzburg vs LASK Linz", "Salzburg vs Wolfsberger AC", "Sturm Graz vs Wolfsberger AC", "BW Linz vs Sturm Graz", "Sturm Graz vs Grazer AK", "LASK Linz vs Sturm Graz", "Wolfsberger AC vs Sturm Graz", "Sturm Graz vs BW Linz", "Grazer AK vs Sturm Graz", "Sturm Graz vs LASK Linz", "Tirol vs Grazer AK", "Wolfsberger AC vs Tirol", "Tirol vs BW Linz", "Tirol vs LASK Linz", "Grazer AK vs Tirol", "Tirol vs Wolfsberger AC", "BW Linz vs Tirol", "LASK Linz vs Tirol"], "teams_affected": ["Wolfsberger AC", "Grazer AK", "<PERSON><PERSON>", "Tirol", "Altach", "Sturm Graz", "Rapid Wien", "Austria Wien", "BW Linz", "Salzburg", "LASK Linz", "<PERSON><PERSON>"], "total_matches": 176, "valid_matches": 112, "skipped_matches": 64}, "THAILAND_THAI_LEAGUE_1": {"warning_count": 30, "missing_matches": ["Bangkok Utd vs Port FC", "Port FC vs Bangkok Utd", "Port FC vs Buriram Utd", "B<PERSON>ram Utd vs Port FC", "Port FC vs Chiangrai Utd", "Chiangrai Utd vs Port FC", "<PERSON><PERSON> vs Port FC", "Port FC vs Khon Kaen U<PERSON>d", "Port FC vs Lamphun Warrior", "Lamphun Warrior vs Port FC", "Port FC vs Muang Thong Utd", "<PERSON>ang Thong Utd vs Port FC", "Port FC vs N. Bua Pitchaya", "N. Bua Pitchaya vs Port FC", "N. Ratcha<PERSON> vs Port FC", "Port FC vs N. Ratchasima", "Port FC vs Nakhon Pathom", "Nakhon Pathom vs Port FC", "Port FC vs Pathum Utd", "Pathum Utd vs Port FC", "Prachuap vs Port FC", "Port FC vs Prachuap", "Ratchaburi vs Port FC", "Port FC vs Ratchaburi", "Rayong vs Port FC", "Port FC vs Rayong", "Port FC vs Sukhothai", "Sukhothai vs Port FC", "Uthai Thani vs Port FC", "Port FC vs Uthai Thani"], "teams_affected": ["Nakhon Pathom", "Sukhothai", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Port FC", "<PERSON><PERSON>", "Bangkok Utd", "<PERSON><PERSON> Utd", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Chiangrai Utd", "Ratchaburi", "<PERSON><PERSON><PERSON>", "Pathum Utd", "<PERSON><PERSON>d"], "total_matches": 450, "valid_matches": 420, "skipped_matches": 30}, "MYANMAR_NATIONAL_LEAGUE": {"warning_count": 22, "missing_matches": ["Ispe FC vs Ayeyawady", "Ayeyawady vs Ispe FC", "Dagon vs Ispe FC", "Ispe FC vs Dagon", "Dagon Port vs Ispe FC", "Ispe FC vs Dagon Port", "Hantharwady vs Ispe FC", "Ispe FC vs Hantharwady", "Ispe FC vs Myawady", "Ispe FC vs Myawady", "Ispe FC vs Rakhine Utd", "<PERSON><PERSON>ine Utd vs Ispe FC", "Ispe FC vs Sagaing Utd", "Sagaing Utd vs Ispe FC", "Shan Utd vs Ispe FC", "Ispe FC vs Shan Utd", "Thitsar Arman vs Ispe FC", "Ispe FC vs Thitsar Arman", "Yadanarbon vs Ispe FC", "Ispe FC vs Yadanarbon", "Ispe FC vs Yangon Utd", "Yangon Utd vs Ispe FC"], "teams_affected": ["Myawady", "Yadanarbon", "Dagon Port", "Shan Utd", "Hantharwady", "Yangon Utd", "Ayeyawady", "<PERSON><PERSON><PERSON>", "Ispe FC", "Dagon", "Sagaing Utd", "<PERSON><PERSON><PERSON>"], "total_matches": 242, "valid_matches": 220, "skipped_matches": 22}, "SWEDEN_DIV_1_NORRA": {"warning_count": 5, "missing_matches": ["<PERSON><PERSON> vs Karlberg", "Arlanda vs United Nordic", "United Nordic vs Gefle", "AFC Eskilstuna vs United Nordic", "United Nordic vs Team TG"], "teams_affected": ["Karlberg", "<PERSON><PERSON><PERSON>", "Haninge", "AFC Eskilstuna", "Team TG", "Arlanda", "United Nordic"], "total_matches": 10, "valid_matches": 5, "skipped_matches": 5}, "FINLAND_KAKKONEN_GROUP_B": {"warning_count": 13, "missing_matches": ["Ilves B vs NJS", "Poxyt vs HJS Akatemia", "Ilves-Kissat vs Poxyt", "VJS vs Poxyt", "Poxyt vs EBK", "Poxyt vs TPV", "P-Iirot vs Poxyt", "Poxyt vs Ilves-Kissat", "HJS Akatemia vs Poxyt", "Poxyt vs VJS", "EBK vs Poxyt", "TPV vs Poxyt", "Poxyt vs P-Iirot"], "teams_affected": ["EBK", "Ilves-Kissat", "P-<PERSON><PERSON><PERSON>", "VJS", "TPV", "HJS Akatemia", "Poxyt", "NJS", "Ilves B"], "total_matches": 21, "valid_matches": 8, "skipped_matches": 13}, "DENMARK_2ND_DIVISION": {"warning_count": 54, "missing_matches": ["AB Copenhagen vs Aarhus Fremad", "Aarhus Fremad vs Hellerup IK", "Aarhus Fremad vs BK Frem", "BK Frem vs Aarhus Fremad", "<PERSON><PERSON><PERSON> IK vs Aarhus Fremad", "Aarhus Fremad vs AB Copenhagen", "<PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> vs BK Frem", "<PERSON><PERSON><PERSON> vs AB Copenhagen", "AB Copenhagen vs Fremad Amager", "<PERSON><PERSON><PERSON> vs Hell<PERSON>up IK", "BK Frem vs <PERSON><PERSON><PERSON>", "AB Copenhagen vs Helsingor", "BK Frem vs Helsingor", "Helsingor vs Hellerup IK", "Helsingor vs BK Frem", "<PERSON><PERSON><PERSON> IK vs Helsingor", "Helsingor vs AB Copenhagen", "<PERSON><PERSON>up IK vs <PERSON><PERSON>j", "BK Frem vs Ishoj", "Ishoj vs AB Copenhagen", "<PERSON><PERSON><PERSON> vs Hellerup IK", "AB Copenhagen vs Ishoj", "<PERSON><PERSON>j vs BK Frem", "BK Frem vs Middelfart", "<PERSON><PERSON>up IK vs Middelfart", "AB Copenhagen vs Middelfart", "Middelfart vs AB Copenhagen", "Middelfart vs BK Frem", "Middelfart vs Hellerup IK", "AB Copenhagen vs Naestved", "Naestved vs Hellerup IK", "BK Frem vs Naestved", "<PERSON><PERSON><PERSON> IK vs Naestved", "Naestved vs AB Copenhagen", "Naestved vs BK Frem", "Nykobing vs AB Copenhagen", "<PERSON><PERSON><PERSON><PERSON> vs BK Frem", "<PERSON><PERSON><PERSON> IK vs Nyk<PERSON>ing", "BK Frem vs Nykobing", "<PERSON><PERSON><PERSON><PERSON> vs Hellerup IK", "AB Copenhagen vs Nykobing", "BK Frem vs Skive", "<PERSON>ve vs Hellerup IK", "AB Copenhagen vs Skive", "Skive vs AB Copenhagen", "Skive vs BK Frem", "<PERSON><PERSON>up IK vs Skive", "AB Copenhagen vs Thisted", "BK Frem vs Thisted", "Thisted vs Hellerup IK", "Thisted vs AB Copenhagen", "<PERSON><PERSON><PERSON> IK vs Thisted", "Thisted vs BK Frem"], "teams_affected": ["Middelfart", "AB Copenhagen", "<PERSON><PERSON><PERSON>", "Skive", "<PERSON><PERSON><PERSON> IK", "<PERSON><PERSON><PERSON>", "Thisted", "BK Frem", "<PERSON><PERSON><PERSON><PERSON>", "Naestved", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "total_matches": 198, "valid_matches": 144, "skipped_matches": 54}, "ECUADOR_SERIE_B": {"warning_count": 3, "missing_matches": ["Chacaritas vs Imbabura", "<PERSON><PERSON><PERSON> vs Cumbaya", "22 <PERSON> vs <PERSON> Torres"], "teams_affected": ["<PERSON>", "22 de <PERSON>", "I<PERSON>bur<PERSON>", "Cumbaya", "Chacaritas", "<PERSON><PERSON><PERSON>"], "total_matches": 7, "valid_matches": 4, "skipped_matches": 3}, "GERMANY_BUNDESLIGA_2": {"warning_count": 102, "missing_matches": ["FC Koln vs Braunschweig", "Braunschweig vs Karlsruher SC", "Braunschweig vs Hamburger SV", "FC Nurnberg vs Braunschweig", "Braunschweig vs FC Koln", "Karlsruher SC vs Braunschweig", "Hamburger SV vs Braunschweig", "Darmstadt vs FC Nurnberg", "Karlsruher SC vs Darmstadt", "Darmstadt vs FC Koln", "Hamburger SV vs Darmstadt", "FC Nurnberg vs Darmstadt", "Darmstadt vs Karlsruher SC", "FC Koln vs Darmstadt", "Dusseldorf vs Karlsruher SC", "Dusseldorf vs FC Koln", "Dusseldorf vs Hamburger SV", "FC Nurnberg vs Dusseldorf", "Karlsruher SC vs Dusseldorf", "FC Koln vs Dusseldorf", "Hamburger SV vs Dusseldorf", "Dusseldorf vs FC Nurnberg", "Elversberg vs FC Koln", "Karlsruher SC vs Elversberg", "Elversberg vs Hamburger SV", "Elversberg vs FC Nurnberg", "FC Koln vs Elversberg", "Elversberg vs Karlsruher SC", "Hamburger SV vs Elversberg", "Greuther Furth vs FC Nurnberg", "FC Koln vs Greuther Furth", "<PERSON><PERSON>uth<PERSON> vs Karlsruher SC", "Hamburger SV vs Greuther Furth", "FC Nurnberg vs Greuther Furth", "Greuther Furth vs FC Koln", "Karlsruher SC vs Greuther Furth", "Hannover 96 vs Hamburger SV", "Hannover 96 vs FC Nurnberg", "Hannover 96 vs Karlsruher SC", "FC Koln vs Hannover 96", "Hamburger SV vs Hannover 96", "FC Nurnberg vs Hannover 96", "Karlsruher SC vs Hannover 96", "Hannover 96 vs FC Koln", "Hamburger SV vs Hertha Berlin", "FC Nurnberg vs Hertha Berlin", "Karlsruher SC vs Hertha Berlin", "Hertha Berlin vs FC Koln", "Hertha Berlin vs Hamburger SV", "Hertha Berlin vs FC Nurnberg", "Hertha Berlin vs Karlsruher SC", "FC Koln vs Hertha Berlin", "Kaiserslautern vs Hamburger SV", "FC Nurnberg vs Kaiserslautern", "Kaiserslautern vs Karlsruher SC", "Kaiserslautern vs FC Koln", "Hamburger SV vs Kaiserslautern", "Kaiserslautern vs FC Nurnberg", "FC Nurnberg vs Magdeburg", "FC Koln vs Magdeburg", "Magdeburg vs Karlsruher SC", "Hamburger SV vs Magdeburg", "Magdeburg vs FC Nurnberg", "Magdeburg vs FC Koln", "Karlsruher SC vs Magdeburg", "Magdeburg vs Hamburger SV", "Hamburger SV vs Paderborn", "FC Koln vs Paderborn", "Paderborn vs FC Nurnberg", "Paderborn vs Karlsruher SC", "Paderborn vs Hamburger SV", "Paderborn vs FC Koln", "FC Nurnberg vs Paderborn", "Hamburger SV vs Preuss. Munster", "FC Nurnberg vs Preuss. Munster", "Karlsruher SC vs Preuss. Munster", "Preuss. Munster vs FC Koln", "Preuss. Munster vs Hamburger SV", "Preuss. Munster vs FC Nurnberg", "Preuss. Munster vs Karlsruher SC", "FC Koln vs Preuss. Munster", "Hamburger SV vs Regensburg", "FC Nurnberg vs Regensburg", "Regensburg vs FC Koln", "Karlsruher SC vs Regensburg", "Regensburg vs Hamburger SV", "Regensburg vs FC Nurnberg", "FC Nurnberg vs Schalke 04", "Schalke 04 vs FC Koln", "Karlsruher SC vs Schalke 04", "Hamburger SV vs Schalke 04", "Schalke 04 vs FC Nurnberg", "FC Koln vs Schalke 04", "Schalke 04 vs Karlsruher SC", "Schalke 04 vs Hamburger SV", "Ulm vs FC Nurnberg", "FC Koln vs Ulm", "Ulm vs Karlsruher SC", "Ulm vs Hamburger SV", "FC Nurnberg vs Ulm", "Ulm vs FC Koln", "Karlsruher SC vs Ulm"], "teams_affected": ["Karlsruher SC", "FC Nurnberg", "<PERSON><PERSON><PERSON><PERSON>", "FC Koln", "Darmstadt", "Magdeburg", "Elversberg", "Regensburg", "<PERSON><PERSON>", "Dusseldorf", "Preuss. Munster", "Braunschweig", "<PERSON><PERSON>", "Hannover 96", "Paderborn", "Hamburger SV", "Schalke 04", "Kaiserslautern"], "total_matches": 434, "valid_matches": 332, "skipped_matches": 102}, "BOLIVIA_LIGA_DE_FUTBOL_PROFESIONAL_BOLIVIANO": {"warning_count": 4, "missing_matches": ["<PERSON><PERSON> <PERSON> vs Always Ready", "<PERSON><PERSON> vs GV San Jose", "<PERSON><PERSON> Petrolero vs Real Oruro", "SA Bulo Bulo vs <PERSON><PERSON>"], "teams_affected": ["<PERSON><PERSON> Vin<PERSON>", "<PERSON><PERSON>", "Always Ready", "GV San Jose", "SA Bulo Bulo", "Real Oruro"], "total_matches": 9, "valid_matches": 5, "skipped_matches": 4}, "VIETNAM_V_LEAGUE_2": {"warning_count": 116, "missing_matches": ["Ba Ria Vung Tau vs <PERSON> B", "PVF-CAND vs Ba Ria Vung Tau", "<PERSON> vs Ba Ria <PERSON>", "<PERSON> R<PERSON> vs <PERSON><PERSON>", "Ba Ria Vung Tau vs PVF-CAND", "Ho Chi Minh B vs Ba Ria Vung Tau", "<PERSON><PERSON> vs Ba Ria <PERSON>", "Ba Ria <PERSON>ung Tau vs <PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON> B vs <PERSON><PERSON> Phuoc", "<PERSON> vs <PERSON><PERSON> Phuoc", "Binh Phuoc vs PVF-CAND", "<PERSON><PERSON> vs <PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> Phuoc", "<PERSON><PERSON> Ph<PERSON> vs <PERSON>", "PVF-CAND vs Binh Phuoc", "<PERSON><PERSON> vs <PERSON>", "<PERSON> vs <PERSON>", "<PERSON> vs <PERSON>", "<PERSON> vs PVF-CAND", "<PERSON> vs <PERSON><PERSON>", "<PERSON> Minh B vs <PERSON>", "<PERSON> vs <PERSON>", "PVF-CAND vs Dong Nai", "<PERSON><PERSON> vs <PERSON>", "<PERSON> vs <PERSON>", "<PERSON> An vs <PERSON>", "<PERSON> vs <PERSON><PERSON>", "<PERSON> vs <PERSON>", "<PERSON><PERSON> vs <PERSON>", "<PERSON> vs <PERSON><PERSON> Phuoc", "<PERSON><PERSON> vs <PERSON>", "<PERSON> vs Ba Ria <PERSON>", "PVF-CAND vs <PERSON> Thap", "<PERSON><PERSON> vs <PERSON>", "<PERSON> vs <PERSON> An", "<PERSON> Minh B vs <PERSON>", "<PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> Ph<PERSON> vs <PERSON>", "<PERSON> vs <PERSON><PERSON>", "<PERSON> vs <PERSON>", "<PERSON> vs PVF-CAND", "Ba Ria <PERSON>ung Tau vs <PERSON>", "<PERSON> vs <PERSON><PERSON>", "<PERSON> vs <PERSON>", "<PERSON> B vs <PERSON><PERSON> Phuoc", "Ho <PERSON> B vs PVF-CAND", "Ba Ria Vung Tau vs <PERSON> B", "<PERSON> vs Long An", "<PERSON> vs <PERSON>", "<PERSON><PERSON> vs <PERSON>", "<PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON>h vs <PERSON> B", "<PERSON> vs <PERSON><PERSON>", "PVF-CAND vs Ho <PERSON> B", "<PERSON><PERSON> vs <PERSON>", "<PERSON> Minh B vs <PERSON>", "<PERSON> Minh B vs <PERSON>", "<PERSON> An vs <PERSON>", "Ho Chi Minh B vs Ba Ria Vung Tau", "<PERSON><PERSON> vs <PERSON>", "<PERSON> B vs <PERSON><PERSON> Binh", "<PERSON><PERSON> vs <PERSON>", "<PERSON> vs <PERSON><PERSON> <PERSON>", "PVF-CAND vs Hoa Binh", "<PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON>h vs <PERSON> B", "<PERSON><PERSON> vs <PERSON>", "Ho<PERSON> Binh vs PVF-CAND", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON> B vs <PERSON><PERSON> Binh", "<PERSON><PERSON> vs <PERSON>", "Hue vs PVF-CAND", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON> vs <PERSON><PERSON>", "PVF-CAND vs Hue", "<PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON>", "Long An vs PVF-CAND", "<PERSON> An vs <PERSON>", "<PERSON> vs Long An", "<PERSON><PERSON> vs <PERSON> An", "<PERSON> vs <PERSON> An", "PVF-CAND vs Long An", "<PERSON> An vs <PERSON>", "<PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "PVF-CAND vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON>", "<PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs PVF-CAND", "<PERSON><PERSON> vs <PERSON>", "<PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON>", "PVF-CAND vs <PERSON><PERSON>", "<PERSON> R<PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON> An", "<PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> Phuoc", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs Ba Ria <PERSON>", "<PERSON><PERSON> vs PVF-CAND", "<PERSON> vs <PERSON><PERSON> <PERSON>"], "teams_affected": ["<PERSON> An", "<PERSON>", "<PERSON><PERSON>", "PVF-CAND", "<PERSON><PERSON>", "<PERSON> B", "<PERSON><PERSON> Binh", "Ba Ria Vung Tau", "<PERSON><PERSON> Ph<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "total_matches": 200, "valid_matches": 84, "skipped_matches": 116}, "SPAIN_SEGUNDA_RFEF_GROUP_3": {"warning_count": 62, "missing_matches": ["Ilicitano vs <PERSON><PERSON>", "SD Ibiza vs <PERSON><PERSON>", "<PERSON><PERSON> vs Ilicitano", "Alzira vs SD Ibiza", "Alzira vs Ilicitano", "SD Ibiza vs Alzira", "Ilicitano vs Alzira", "Ilicitano vs Andratx", "Andratx vs SD Ibiza", "Andratx vs Ilicitano", "SD Ibiza vs Andratx", "Ilicitano vs Badalona", "Badalona vs SD Ibiza", "Badalona vs Ilicitano", "SD Ibiza vs Badalona", "Cornella vs SD Ibiza", "Cornella vs Ilicitano", "SD Ibiza vs Cornella", "Ilicitano vs Cornella", "Espanyol B vs Ilicitano", "SD Ibiza vs Espanyol B", "Ilicitano vs Espanyol B", "Espanyol B vs SD Ibiza", "SD Ibiza vs Europa", "Europa vs Ilicitano", "Europa vs SD Ibiza", "Ilicitano vs Europa", "SD Ibiza vs Lleida", "Ilicitano vs Lleida", "Lleida vs SD Ibiza", "Lleida vs Ilicitano", "Mallorca B vs SD Ibiza", "Ilicitano vs Mallorca B", "SD Ibiza vs Mallorca B", "Mallorca B vs Ilicitano", "SD Ibiza vs Olot", "Ilicitano vs Olot", "Olot vs SD Ibiza", "Olot vs Ilicitano", "Pena Deportiva vs SD Ibiza", "Ilicitano vs Pena Deportiva", "SD Ibiza vs Pena Deportiva", "Sabadell vs SD Ibiza", "Sabadell vs Ilicitano", "SD Ibiza vs Sabadell", "Ilicitano vs Sabadell", "SD Ibiza vs Sant Andreu", "Ilicitano vs Sant Andreu", "<PERSON> vs SD Ibiza", "<PERSON> vs Ilicitano", "Terrassa vs Ilicitano", "SD Ibiza vs Terrassa", "Ilicitano vs Terrassa", "Terrassa vs SD Ibiza", "Ilicitano vs Torrent", "Torrent vs SD Ibiza", "Torrent vs Ilicitano", "SD Ibiza vs Torrent", "Valencia B vs Ilicitano", "SD Ibiza vs Valencia B", "Ilicitano vs Valencia B", "Valencia B vs SD Ibiza"], "teams_affected": ["<PERSON><PERSON>", "Pena Deportiva", "Badalona", "Terrassa", "Ilicitano", "Cornella", "Espanyol B", "Mallorca B", "Andratx", "Sa<PERSON>ell", "Valencia B", "<PERSON><PERSON>", "SD Ibiza", "Alzira", "Lleida", "<PERSON>", "<PERSON><PERSON>", "Europa"], "total_matches": 526, "valid_matches": 464, "skipped_matches": 62}, "MOROCCO_BOTALA": {"warning_count": 84, "missing_matches": ["FAR Rabat vs Berkane", "Berkane vs FUS Rabat", "Berkane vs JS Sousalem", "Berkane vs Maghreb de Fes", "Berkane vs FAR Rabat", "FUS Rabat vs Berkane", "JS Sousalem vs Berkane", "C. Mohammedia vs FAR Rabat", "FUS Rabat vs C. Mohammedia", "C. Mohammed<PERSON> vs JS Sousalem", "<PERSON><PERSON> vs Maghreb de Fes", "FAR Rabat vs C. Mohammedia", "C. Mohammedia vs FUS Rabat", "JS Sousalem vs C. Mohammedia", "Maghreb de Fes vs C. Mohammed<PERSON>", "Difaa El Jadida vs JS Sousalem", "Difaa El Jadida vs FAR Rabat", "Maghreb de Fes vs Difaa El Jadida", "FUS Rabat vs Difaa El Jadida", "JS Sousalem vs Difaa El Jadida", "FAR Rabat vs Difaa El Jadida", "Difaa El Jadida vs Maghreb de Fes", "JS Sousalem vs Hassania Agadir", "Maghreb de Fes vs Hassania Agadir", "Hassania Agadir vs FUS Rabat", "FAR Rabat vs Hassania Agadir", "Hassania Agadir vs JS Sousalem", "Hassania Agadir vs Maghreb de Fes", "FUS Rabat vs It<PERSON><PERSON>", "<PERSON><PERSON><PERSON> vs FAR Rabat", "JS Sousalem vs Ittihad Tanger", "<PERSON><PERSON><PERSON> vs Maghreb de Fes", "<PERSON><PERSON><PERSON> vs FUS Rabat", "FAR Rabat vs Ittihad Tanger", "<PERSON><PERSON><PERSON> vs JS Sousalem", "Maghreb de Fes vs <PERSON><PERSON><PERSON> Tang<PERSON>", "Meknes vs JS Sousalem", "FAR Rabat vs Meknes", "Meknes vs Maghreb de Fes", "FUS Rabat vs Meknes", "JS Sousalem vs Meknes", "Meknes vs FAR Rabat", "Maghreb de Fes vs Meknes", "Moghreb Tetouan vs Maghreb de Fes", "Moghreb Tetouan vs FUS Rabat", "JS Sousalem vs Moghreb Tetouan", "Moghreb Tetouan vs FAR Rabat", "Maghreb de Fes vs Moghreb Tetouan", "FUS Rabat vs Olympic Safi", "FAR Rabat vs Olympic Safi", "Olympic Safi vs JS Sousalem", "Maghreb de Fes vs Olympic Safi", "Olympic Safi vs FUS Rabat", "Olympic Safi vs FAR Rabat", "JS Sousalem vs Olympic Safi", "<PERSON><PERSON> vs FUS Rabat", "FAR Rabat vs <PERSON><PERSON>", "<PERSON><PERSON> vs JS Sousalem", "Maghre<PERSON> de Fes vs <PERSON><PERSON>", "FUS <PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs FAR Rabat", "J<PERSON> Sousalem vs <PERSON><PERSON>", "Raja Casablanca vs FUS Rabat", "JS Sousalem vs Raja Casablanca", "Raja Casablanca vs FAR Rabat", "Maghreb de Fes vs Raja Casablanca", "FUS Rabat vs Raja Casablanca", "Raja Casablanca vs JS Sousalem", "FAR Rabat vs Raja Casablanca", "Raja Casablanca vs Maghreb de Fes", "Union Touarga vs FUS Rabat", "Maghreb de Fes vs Union Touarga", "JS Sousalem vs Union Touarga", "FAR Rabat vs Union Touarga", "FUS Rabat vs Union Touarga", "Union Touarga vs Maghreb de Fes", "Union Touarga vs JS Sousalem", "Union Touarga vs FAR Rabat", "Maghreb de Fes vs Wydad", "FUS Rabat vs Wydad", "<PERSON><PERSON><PERSON> vs JS Sousalem", "FAR Rabat vs Wydad", "<PERSON><PERSON><PERSON> vs Maghreb de Fes", "Wydad vs FUS Rabat"], "teams_affected": ["C<PERSON>", "<PERSON><PERSON>", "Olympic Safi", "<PERSON><PERSON><PERSON>", "Difaa El Jadida", "Moghreb Tetouan", "Raja Casablanca", "Meknes", "Union Touarga", "Maghreb de Fes", "<PERSON><PERSON>", "FAR Rabat", "JS Sousalem", "FUS Rabat", "<PERSON><PERSON><PERSON>", "Berkane"], "total_matches": 312, "valid_matches": 228, "skipped_matches": 84}, "NORWAY_1_DIVISION": {"warning_count": 2, "missing_matches": ["<PERSON><PERSON> vs Kongsvinger", "<PERSON><PERSON>id vs Kongsvinger"], "teams_affected": ["<PERSON><PERSON><PERSON>", "Kongsvinger", "<PERSON><PERSON>"], "total_matches": 9, "valid_matches": 7, "skipped_matches": 2}, "PORTUGAL_CAMPEONATO_DE_PORTUGAL_GROUP_D": {"warning_count": 66, "missing_matches": ["FC Serpa vs Amora", "Amora vs Lusitano GC", "FC Barreirense vs Amora", "Amora vs FC Serpa", "Lusitano GC vs Amora", "Amora vs FC Barreirense", "E. Vendas Novas vs FC Serpa", "Lusitano GC vs E. Vendas Novas", "FC Barreirense vs E. Vendas Novas", "FC Serpa vs E. Vendas Novas", "<PERSON><PERSON>as Novas vs Lusitano GC", "E. Vendas Novas vs FC Barreirense", "Estrela B vs FC Barreirense", "FC Serpa vs Estrela B", "Estrela B vs Lusitano GC", "FC Barreirense vs Estrela B", "Estrela B vs FC Serpa", "Lusitano GC vs Estrela B", "FC Serpa vs Fabril <PERSON>", "<PERSON><PERSON><PERSON> vs Lusitano GC", "FC Barreirense vs Fabril Barreiro", "<PERSON><PERSON><PERSON> vs FC Serpa", "Lusitano GC vs <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> vs FC Barreirense", "Industria vs FC Barreirense", "Industria vs FC Serpa", "Lusitano GC vs Industria", "FC Barreirense vs Industria", "FC Serpa vs Industria", "Industria vs Lusitano GC", "Lusitano GC vs Lagoa", "Lagoa vs FC Barreirense", "Lagoa vs FC Serpa", "Lagoa vs Lusitano GC", "FC Barreirense vs Lagoa", "FC Serpa vs Lagoa", "Louletano vs FC Barreirense", "FC Serpa vs Louletano", "Louletano vs Lusitano GC", "FC Barreirense vs Louletano", "Louletano vs FC Serpa", "Lusitano GC vs Louletano", "FC Serpa vs Moncarapachense", "Moncarapachense vs Lusitano GC", "Moncarapachense vs FC Barreirense", "Moncarapachense vs FC Serpa", "Lusitano GC vs Moncarapachense", "FC Barreirense vs Moncarapachense", "FC Barreirense vs Moura", "Mo<PERSON> vs FC Serpa", "Lusitano GC vs Moura", "Moura vs FC Barreirense", "FC Serpa vs Moura", "<PERSON><PERSON> vs Lusitano GC", "Operario vs FC Serpa", "Lusitano GC vs Operario", "FC Barreirense vs Operario", "FC Serpa vs Operario", "Operario vs Lusitano GC", "Operario vs FC Barreirense", "Sintrense vs FC Barreirense", "Sintrense vs FC Serpa", "Lusitano GC vs Sintrense", "FC Barreirense vs Sintrense", "FC Serpa vs Sintrense", "Sintrense vs Lusitano GC"], "teams_affected": ["Moncarapachense", "Sintrense", "Operario", "Lusitano GC", "<PERSON><PERSON>", "FC Barreirense", "Lagoa", "<PERSON><PERSON>", "FC Serpa", "<PERSON><PERSON><PERSON>", "Estrela B", "Louletano", "<PERSON><PERSON>", "Industria"], "total_matches": 286, "valid_matches": 220, "skipped_matches": 66}, "CHINA_LEAGUE_ONE": {"warning_count": 4, "missing_matches": ["Nantong Zhiyun vs Chongqing T.", "Shanghai Jiadin vs Shenzhen J.", "Suzhou Dongwu vs Shaanxi Union", "<PERSON><PERSON> vs Suzhou Dongwu"], "teams_affected": ["Shanghai Jiadin", "Suzhou Dongwu", "Shaanxi Union", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shenzhen J.", "Chongqing T."], "total_matches": 8, "valid_matches": 4, "skipped_matches": 4}, "ARGENTINA_PRIMERA_NACIONAL": {"warning_count": 198, "missing_matches": ["CA Guemes vs A. Atlanta", "A. <PERSON> vs SM Tucuman", "A. Atlanta vs Gimnasia y Tiro", "D. de Belgrano vs Agropecuario", "CA Estudiantes vs Agropecuario", "Agropecuario vs D. de Belgrano", "All Boys vs CA Guemes", "SM Tucuman vs All Boys", "Gimnasia y Tiro vs All Boys", "Almagro vs SM Tucuman", "Almagro vs Gimnasia y Tiro", "CA Guemes vs Almagro", "SM Tucuman vs Almagro", "Gimnasia y Tiro vs Almagro", "CA Estudiantes vs Almirante Brown", "<PERSON><PERSON> Belgrano vs Almirante Brown", "Almirante Brown vs CA Estudiantes", "CA Guemes vs Alvarado", "<PERSON><PERSON><PERSON> vs SM Tucuman", "Alvarado vs Gimnasia y Tiro", "CA Guemes vs Arsenal Sarandi", "Arsenal Sarandi vs SM Tucuman", "Arsenal Sarandi vs Gimnasia y Tiro", "D. de Belgrano vs Atletico Mitre", "CA Estudiantes vs Atletico Mitre", "Chaco For Ever vs CA Estudiantes", "CA Estudiantes vs Almirante Brown", "Central Norte vs CA Estudiantes", "CA Estudiantes vs G. Mendoza", "Gimnasia Jujuy vs CA Estudiantes", "CA Estudiantes vs Agropecuario", "San Telmo vs CA Estudiantes", "CA Estudiantes vs Chacarita J.", "<PERSON><PERSON> Belgrano vs CA Estudiantes", "CA Estudiantes vs E. Rio Cuarto", "<PERSON><PERSON> vs CA Estudiantes", "CA Estudiantes vs Deportivo Moron", "Nueva Chicago vs CA Estudiantes", "Defensores U. vs CA Estudiantes", "Colon Santa Fe vs CA Estudiantes", "CA Estudiantes vs Atletico Mitre", "CA Estudiantes vs Temperley", "CA Estudiantes vs Chaco For Ever", "Almirante Brown vs CA Estudiantes", "CA Estudiantes vs Central Norte", "<PERSON><PERSON> vs CA Estudiantes", "Ferro Carril vs CA Guemes", "CA Guemes vs Patronato", "Los Andes vs CA Guemes", "SM Tucuman vs CA Guemes", "CA Guemes vs Tristan <PERSON>", "Gimnasia y Tiro vs CA Guemes", "CA Guemes vs Deportivo Maipu", "<PERSON><PERSON> vs CA Guemes", "CA Guemes vs Alvarado", "All Boys vs CA Guemes", "CA Guemes vs Arsenal Sarandi", "Colegiales vs CA Guemes", "CA Guemes vs A. Atlanta", "Racing Cordoba vs CA Guemes", "CA Guemes vs Quilmes", "San Miguel vs CA Guemes", "CA Guemes vs Almagro", "CA Guemes vs Ferro Carril", "Patronato vs CA Guemes", "CA Guemes vs Los Andes", "CA Guemes vs SM Tucuman", "Central Norte vs CA Estudiantes", "Central Norte vs D. de Belgrano", "CA Estudiantes vs Central Norte", "<PERSON><PERSON> vs Chacarita J.", "CA Estudiantes vs Chacarita J.", "<PERSON><PERSON><PERSON> J. vs <PERSON><PERSON>", "Chaco For Ever vs CA Estudiantes", "Chaco For Ever vs <PERSON><PERSON> <PERSON> Belgrano", "CA Estudiantes vs Chaco For Ever", "Colegiales vs CA Guemes", "SM Tucuman vs Colegiales", "Gimnasia y Tiro vs Colegiales", "Colon Santa Fe vs D. de Belgrano", "Colon Santa Fe vs CA Estudiantes", "Gimnasia Jujuy vs D<PERSON>grano", "D. de Belgrano vs Agropecuario", "San Telmo vs D. de Belgrano", "<PERSON><PERSON> vs Chacarita J.", "Nueva Chicago vs <PERSON><PERSON> de Belgrano", "<PERSON><PERSON> vs D<PERSON> de Belgrano", "<PERSON><PERSON> vs <PERSON><PERSON>", "Deportivo Moron vs D. de Belgrano", "<PERSON><PERSON> Belgrano vs CA Estudiantes", "Defensores U. vs <PERSON><PERSON>", "<PERSON><PERSON> vs Temperley", "Colon Santa Fe vs D. de Belgrano", "D. de Belgrano vs Atletico Mitre", "Chaco For Ever vs <PERSON><PERSON> <PERSON> Belgrano", "Central Norte vs D. de Belgrano", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> Belgrano vs Almirante Brown", "<PERSON><PERSON>grano vs Gimnasia Jujuy", "Agropecuario vs D. de Belgrano", "<PERSON><PERSON><PERSON> J. vs <PERSON><PERSON>", "<PERSON><PERSON> vs CA Guemes", "SM Tucuman vs <PERSON><PERSON>", "Gimnasia y Tiro vs <PERSON><PERSON>", "Defensores U. vs <PERSON><PERSON>", "Defensores U. vs CA Estudiantes", "CA Guemes vs Deportivo Maipu", "Deportivo Maipu vs SM Tucuman", "Deportivo Maipu vs Gimnasia y Tiro", "Deportivo Moron vs D. de Belgrano", "CA Estudiantes vs Deportivo Moron", "<PERSON><PERSON> vs D<PERSON> de Belgrano", "CA Estudiantes vs E. Rio Cuarto", "Ferro Carril vs CA Guemes", "SM Tucuman vs <PERSON><PERSON>", "Gimnasia y Tiro vs Ferro Carril", "CA Guemes vs Ferro Carril", "<PERSON><PERSON> vs SM Tucuman", "<PERSON>rro Carril vs Gimnasia y Tiro", "CA Estudiantes vs G. Mendoza", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs CA Estudiantes", "Gimnasia Jujuy vs D<PERSON>grano", "Gimnasia Jujuy vs CA Estudiantes", "<PERSON><PERSON>grano vs Gimnasia Jujuy", "Quilmes vs Gimnasia y Tiro", "Gimnasia y Tiro vs San Miguel", "Almagro vs Gimnasia y Tiro", "Gimnasia y Tiro vs Ferro Carril", "Patronato vs Gimnasia y Tiro", "Gimnasia y Tiro vs CA Guemes", "SM Tucuman vs Gimnasia y Tiro", "Gimnasia y Tiro vs <PERSON>", "Los Andes vs Gimnasia y Tiro", "Deportivo Maipu vs Gimnasia y Tiro", "Gimnasia y Tiro vs <PERSON><PERSON>", "Alvarado vs Gimnasia y Tiro", "Gimnasia y Tiro vs All Boys", "Arsenal Sarandi vs Gimnasia y Tiro", "Gimnasia y Tiro vs Colegiales", "A. Atlanta vs Gimnasia y Tiro", "Gimnasia y Tiro vs Racing Cordoba", "Gimnasia y Tiro vs Quilmes", "San Miguel vs Gimnasia y Tiro", "Gimnasia y Tiro vs Almagro", "<PERSON>rro Carril vs Gimnasia y Tiro", "Los Andes vs CA Guemes", "Los Andes vs SM Tucuman", "Los Andes vs Gimnasia y Tiro", "CA Guemes vs Los Andes", "Nueva Chicago vs <PERSON><PERSON> de Belgrano", "Nueva Chicago vs CA Estudiantes", "CA Guemes vs Patronato", "Patronato vs SM Tucuman", "Patronato vs Gimnasia y Tiro", "Patronato vs CA Guemes", "SM Tucuman vs Patronato", "Quilmes vs Gimnasia y Tiro", "CA Guemes vs Quilmes", "<PERSON><PERSON><PERSON> vs SM Tucuman", "Gimnasia y Tiro vs Quilmes", "Racing Cordoba vs CA Guemes", "SM Tucuman vs Racing Cordoba", "Gimnasia y Tiro vs Racing Cordoba", "Gimnasia y Tiro vs San Miguel", "San Miguel vs CA Guemes", "SM Tucuman vs San Miguel", "San Miguel vs Gimnasia y Tiro", "San Telmo vs D. de Belgrano", "San Telmo vs CA Estudiantes", "Almagro vs SM Tucuman", "SM Tucuman vs <PERSON><PERSON>", "Patronato vs SM Tucuman", "SM Tucuman vs CA Guemes", "Los Andes vs SM Tucuman", "<PERSON> vs SM Tucuman", "SM Tucuman vs Gimnasia y Tiro", "Deportivo Maipu vs SM Tucuman", "SM Tucuman vs <PERSON><PERSON>", "<PERSON><PERSON><PERSON> vs SM Tucuman", "SM Tucuman vs All Boys", "Arsenal Sarandi vs SM Tucuman", "SM Tucuman vs Colegiales", "A. <PERSON> vs SM Tucuman", "SM Tucuman vs Racing Cordoba", "<PERSON><PERSON><PERSON> vs SM Tucuman", "SM Tucuman vs San Miguel", "SM Tucuman vs Almagro", "<PERSON><PERSON> vs SM Tucuman", "SM Tucuman vs Patronato", "CA Guemes vs SM Tucuman", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs CA Estudiantes", "<PERSON><PERSON> vs Temperley", "CA Estudiantes vs Temperley", "CA Guemes vs Tristan <PERSON>", "<PERSON> vs SM Tucuman", "Gimnasia y Tiro vs <PERSON>"], "teams_affected": ["Almirante Brown", "SM Tucuman", "Gimnasia y Tiro", "<PERSON><PERSON><PERSON>", "A. Atlanta", "Agropecuario", "Defensores U.", "San Miguel", "<PERSON><PERSON>", "Almagro", "Deportivo Moron", "CA Estudiantes", "Nueva Chicago", "Patronato", "<PERSON><PERSON><PERSON>", "Colon Santa Fe", "<PERSON><PERSON>", "Cha<PERSON>ita J.", "<PERSON><PERSON>", "<PERSON>", "Racing Cordoba", "All Boys", "San Telmo", "Central Norte", "Colegiales", "<PERSON><PERSON>", "Chaco For Ever", "Arsenal Sarandi", "Atletico Mitre", "Gimnasia Jujuy", "E. <PERSON> Cuarto", "CA Guemes", "Los Andes", "Deportivo Maipu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "total_matches": 746, "valid_matches": 548, "skipped_matches": 198}, "BRAZIL_BRASILEIRO_WOMEN": {"warning_count": 2, "missing_matches": ["RB Bragantino W vs Flamengo W", "Palmeiras W vs RB Bragantino W"], "teams_affected": ["Flamengo W", "RB Bragantino W", "Palmeiras W"], "total_matches": 13, "valid_matches": 11, "skipped_matches": 2}, "INDONESIA_LIGA_1": {"warning_count": 100, "missing_matches": ["PSM vs Arema", "PSS Sleman vs Arema", "PSIS Semarang vs Arema", "Arema vs PSBS Biak N.", "Arema vs PSM", "Arema vs PSS Sleman", "Arema vs PSIS Semarang", "Bali Utd vs PSS Sleman", "PSBS Biak N. vs Bali Utd", "Bali Utd vs PSM", "PSIS Semarang vs Bali Utd", "PSS Sleman vs Bali Utd", "Bali Utd vs PSBS Biak N.", "PSM vs Bali Utd", "Barito Putera vs PSS Sleman", "Barito Putera vs PSBS Biak N.", "PSM vs Barito Putera", "Barito Putera vs PSIS Semarang", "PSS Sleman vs Barito Putera", "PSBS Biak N. vs Barito Putera", "PSS Sleman vs Borneo", "Borneo vs PSBS Biak N.", "PSM vs Borneo", "Borneo vs PSIS Semarang", "Borneo vs PSS Sleman", "PSBS Biak N. vs Borneo", "Borneo vs PSM", "PSIS Semarang vs Borneo", "PSM vs Dewa Utd", "Dewa Utd vs PSIS Semarang", "Dewa Utd vs PSS Sleman", "PSBS Biak N. vs Dewa Utd", "Dewa Utd vs PSM", "PSIS Semarang vs Dewa Utd", "PSS Sleman vs Dewa Utd", "PSBS Biak N. vs Madura Utd", "PSM vs Madura Utd", "Madura Utd vs PSIS Semarang", "PSS Sleman vs Madura Utd", "Madura Utd vs PSBS Biak N.", "Madura Utd vs PSM", "PSIS Semarang vs Madura Utd", "PSS Sleman vs Malut United", "PSBS Biak N. vs Malut United", "Malut United vs PSM", "PSIS Semarang vs Malut United", "Malut United vs PSS Sleman", "Malut United vs PSBS Biak N.", "Persebaya S. vs PSS Sleman", "PSBS Biak N. vs Persebaya S.", "Persebaya S. vs PSM", "PSIS Semarang vs Persebaya S.", "PSS Sleman vs Persebaya S.", "Persebaya S. vs PSBS Biak N.", "PSM vs Persebaya S.", "Persebaya S. vs PSIS Semarang", "Persib vs PSBS Biak N.", "PSM vs Persib", "Persib vs PSIS Semarang", "PSS Sleman vs Persib", "PSBS Biak N. vs Persib", "Persib vs PSM", "PSIS Semarang vs Persib", "Persib vs PSS Sleman", "PSBS Biak N. vs Persija", "Persija vs PSM", "PSIS Semarang vs Persija", "Persija vs PSS Sleman", "Persija vs PSBS Biak N.", "PSM vs Persija", "Persija vs PSIS Semarang", "PSS Sleman vs Persik Kediri", "Persik Kediri vs PSBS Biak N.", "PSM vs Persik Kediri", "Persik Kediri vs PSIS Semarang", "<PERSON>sik Kediri vs PSS Sleman", "PSBS Biak N. vs Persik Kediri", "Persik Kediri vs PSM", "PSIS Semarang vs Persik Kediri", "PSM vs Persis Solo", "Persis Solo vs PSIS Semarang", "<PERSON><PERSON> Solo vs PSS Sleman", "Persis Solo vs PSBS Biak N.", "Persis Solo vs PSM", "PSIS Semarang vs Persis Solo", "PSS Sleman vs Persis Solo", "PSIS Semarang vs Persita", "PSS Sleman vs Persita", "PSBS Biak N. vs Persita", "Persita vs PSM", "Persita vs PSIS Semarang", "Persita vs PSS Sleman", "<PERSON><PERSON> vs PSS Sleman", "PSBS Biak N. vs <PERSON><PERSON>", "<PERSON><PERSON> vs PSM", "PSIS Semarang vs Semen <PERSON>", "PSS Sleman vs Semen <PERSON>", "<PERSON><PERSON> vs PSBS Biak N.", "PSM vs Semen <PERSON>", "<PERSON><PERSON> vs PSIS Semarang"], "teams_affected": ["<PERSON><PERSON><PERSON>", "Persebaya S.", "<PERSON><PERSON>", "PSIS Semarang", "Malut United", "Bali Utd", "Madura Utd", "PSBS Biak N.", "Borneo", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Barito Putera", "PSM", "Dewa Utd", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "PSS Sleman"], "total_matches": 416, "valid_matches": 316, "skipped_matches": 100}, "PORTUGAL_CAMPEONATO_DE_PORTUGAL_GROUP_B": {"warning_count": 48, "missing_matches": ["AD Marco 09 vs Alpendorada", "Beira-Mar vs Alpendorada", "Alpendorada vs AD Marco 09", "Alpendorada vs Beira-Mar", "Beira-Mar vs Camacha", "Camacha vs AD Marco 09", "Camacha vs Beira-Mar", "AD Marco 09 vs Camacha", "Cinfaes vs Beira-Mar", "Cinfaes vs AD Marco 09", "Beira-Mar vs Cinfaes", "AD Marco 09 vs Cinfaes", "Beira-Mar vs Coimbroes", "AD Marco 09 vs Coimbroes", "Coimbroes vs Beira-Mar", "Coimbroes vs AD Marco 09", "Gondomar vs Beira-Mar", "Gondomar vs AD Marco 09", "Beira-Mar vs Gondomar", "AD Marco 09 vs Gondomar", "AD Marco 09 vs Guarda", "Beira-Mar vs Guarda", "Guarda vs AD Marco 09", "Guarda vs Beira-Mar", "Beira-Mar vs Leca", "Leca vs AD Marco 09", "Leca vs Beira-Mar", "AD Marco 09 vs Leca", "Machico vs AD Marco 09", "Machico vs Beira-Mar", "AD Marco 09 vs Machico", "Beira-Mar vs Machico", "Beira-Mar vs Maritimo B", "AD Marco 09 vs Maritimo B", "Maritimo B vs Beira-Mar", "Maritimo B vs AD Marco 09", "Regua vs Beira-Mar", "AD Marco 09 vs Regua", "Beira-Mar vs Regua", "Regua vs AD Marco 09", "Salgueiros vs AD Marco 09", "Salgueiros vs Beira-Mar", "AD Marco 09 vs Salgueiros", "Beira-Mar vs Salgueiros", "Beira-Mar vs Uniao Lamas", "AD Marco 09 vs Uniao Lamas", "<PERSON><PERSON><PERSON> vs Beira-Mar", "Unia<PERSON> vs AD Marco 09"], "teams_affected": ["<PERSON><PERSON><PERSON>", "Mac<PERSON><PERSON>", "Gondomar", "Guarda", "AD Marco 09", "Leca", "Maritimo B", "Cinfaes", "Regua", "Salgueiros", "Camacha", "Alpendorada", "Beira-Mar", "Coim<PERSON>es"], "total_matches": 312, "valid_matches": 264, "skipped_matches": 48}, "LATVIA_VIRSLIGA": {"warning_count": 9, "missing_matches": ["Auda vs RFS", "Riga FC vs Auda", "Auda vs Super Nova", "Auda vs FS Jelgava", "RFS vs Auda", "Auda vs Riga FC", "FS Jelgava vs Daugavpils", "Liepaja vs Riga FC", "Tukums vs Super Nova"], "teams_affected": ["Auda", "RFS", "<PERSON><PERSON><PERSON>", "Daugavpils", "Tukums", "Super Nova", "FS Jelgava", "Riga FC"], "total_matches": 18, "valid_matches": 9, "skipped_matches": 9}, "SWITZERLAND_WOMEN_SUPER_LEAGUE": {"warning_count": 42, "missing_matches": ["Servette FCC W vs Aarau W", "BSC Y. Boys W vs Aarau W", "Aarau W vs FC Zurich W", "Aarau W vs Servette FCC W", "<PERSON><PERSON><PERSON> W vs BSC Y. Boys W", "FC Zurich W vs Aarau W", "Basel W vs BSC Y. Boys W", "Basel W vs FC Zurich W", "Servette FCC W vs Basel W", "BSC Y. Boys W vs Basel W", "FC Zurich W vs Basel W", "Basel W vs Servette FCC W", "FC Zurich W vs Grasshopper W", "Grasshopper W vs Servette FCC W", "Grasshopper W vs BSC Y. Boys W", "Grasshopper W vs FC Zurich W", "Servette FCC W vs Grasshopper W", "BSC Y. Boys W vs Grasshopper W", "Luzern W vs FC Zurich W", "Luzern W vs Servette FCC W", "BSC Y. Boys W vs Luzern W", "FC Zurich W vs Luzern W", "Servette FCC W vs Luzern W", "Luzern W vs BSC Y. Boys W", "Rapperswil J. W vs Servette FCC W", "FC Zurich W vs Rapperswil J. W", "Rapperswil J. W vs BSC Y. Boys W", "Servette FCC W vs Rapperswil J. W", "Rapperswil J. W vs FC Zurich W", "BSC Y. Boys W vs Rapperswil J. W", "St. Gallen W vs Servette FCC W", "BSC Y. Boys W vs St. Gallen W", "FC Zurich W vs St. Gallen W", "Servette FCC W vs St. Gallen W", "St. Gallen W vs BSC Y. Boys W", "St. Gallen W vs FC Zurich W", "FC Zurich W vs Thun W", "Servette FCC W vs Thun W", "T<PERSON> W vs BSC Y. Boys W", "Thun W vs FC Zurich W", "Thun W vs Servette FCC W", "BSC Y. Boys W vs Thun W"], "teams_affected": ["Grasshopper W", "Thun W", "BSC Y. Boys W", "Basel W", "Rapperswil J. W", "<PERSON><PERSON><PERSON> W", "Luzern W", "Servette FCC W", "St. Gallen W", "FC Zurich W"], "total_matches": 126, "valid_matches": 84, "skipped_matches": 42}, "NETHERLANDS_DERDE_DIVISIE_SATURDAY": {"warning_count": 152, "missing_matches": ["HSC 21 vs Ajax Amateurs", "Ajax Amateurs vs DEM", "TEC vs Ajax Amateurs", "IJsselmeervogel vs Ajax Amateurs", "Ajax Amateurs vs USV Hercules", "HBC vs Ajax Amateurs", "Ajax Amateurs vs DVS 33", "DOVO vs Ajax Amateurs", "Ajax Amateurs vs HSC 21", "DEM vs Ajax Amateurs", "Ajax Amateurs vs TEC", "Ajax Amateurs vs IJsselmeervogel", "USV Hercules vs Ajax Amateurs", "Ajax Amateurs vs HBC", "DVS 33 vs Ajax Amateurs", "Ajax Amateurs vs DOVO", "HSC 21 vs Eemdijk", "Eemdijk vs DEM", "Eemdijk vs TEC", "IJsselmeervogel vs Eemdijk", "Eemdijk vs USV Hercules", "HBC vs Eemdijk", "Eemdijk vs DVS 33", "DOVO vs Eemdijk", "Eemdijk vs HSC 21", "DEM vs Eemdijk", "TEC vs Eemdijk", "Eemdijk vs IJsselmeervogel", "USV Hercules vs Eemdijk", "Eemdijk vs HBC", "DVS 33 vs Eemdijk", "TEC vs Excelsior 31", "IJsselmeervogel vs Excelsior 31", "Excelsior 31 vs USV Hercules", "HBC vs Excelsior 31", "Excelsior 31 vs DVS 33", "DOVO vs Excelsior 31", "Excelsior 31 vs HSC 21", "DEM vs Excelsior 31", "Excelsior 31 vs TEC", "Excelsior 31 vs IJsselmeervogel", "USV Hercules vs Excelsior 31", "Excelsior 31 vs HBC", "DVS 33 vs Excelsior 31", "Excelsior 31 vs DOVO", "Genemuiden vs TEC", "Genemuiden vs IJsselmeervogel", "USV Hercules vs Genemuiden", "Genemuiden vs HBC", "DVS 33 vs Genemuiden", "Genemuiden vs DOVO", "HSC 21 vs Genemuiden", "Genemuiden vs DEM", "TEC vs Genemuiden", "IJsselmeervogel vs Genemuiden", "Genemuiden vs USV Hercules", "HBC vs Genemuiden", "Genemuiden vs DVS 33", "DOVO vs Genemuiden", "Genemuiden vs HSC 21", "Harkemase Boys vs DEM", "Harkemase Boys vs TEC", "Harkemase Boys vs IJsselmeervogel", "USV Hercules vs Harkemase Boys", "Harkemase Boys vs HBC", "DVS 33 vs Harkemase Boys", "Harkemase Boys vs DOVO", "HSC 21 vs Harkemase Boys", "DEM vs Harkemase Boys", "TEC vs Harkemase Boys", "IJsselmeervogel vs Harkemase Boys", "HBC vs Harkemase Boys", "Harkemase Boys vs DVS 33", "Harkemase Boys vs USV Hercules", "DOVO vs Harkemase Boys", "DVS 33 vs Huizen", "Huizen vs DOVO", "Huizen vs HSC 21", "DEM vs Huizen", "TEC vs Huizen", "IJsselmeervogel vs Huizen", "Huizen vs USV Hercules", "Huizen vs HBC", "Huizen vs DVS 33", "DOVO vs Huizen", "HSC 21 vs Huizen", "Huizen vs DEM", "Huizen vs TEC", "Huizen vs IJsselmeervogel", "USV Hercules vs Huizen", "HBC vs Raalte", "Raalte vs DVS 33", "DOVO vs Raalte", "HSC 21 vs Raalte", "Raalte vs DEM", "Raalte vs TEC", "Raalte vs IJsselmeervogel", "USV Hercules vs Raalte", "Raalte vs HBC", "DVS 33 vs Raalte", "Raalte vs DOVO", "Raalte vs HSC 21", "DEM vs Raalte", "TEC vs Raalte", "IJsselmeervogel vs Raalte", "Raalte vs USV Hercules", "Sparta Nijkerk vs IJsselmeervogel", "USV Hercules vs Sparta Nijkerk", "Sparta Nijkerk vs HBC", "DVS 33 vs Sparta Nijkerk", "Sparta Nijkerk vs DOVO", "HSC 21 vs Sparta Nijkerk", "Sparta Nijkerk vs DEM", "Sparta Nijkerk vs TEC", "IJsselmeervogel vs Sparta Nijkerk", "Sparta Nijkerk vs USV Hercules", "HBC vs Sparta Nijkerk", "Sparta Nijkerk vs DVS 33", "DOVO vs Sparta Nijkerk", "Sparta Nijkerk vs HSC 21", "DEM vs Sparta Nijkerk", "IJsselmeervogel vs Sportlust", "Sportlust vs USV Hercules", "HBC vs Sportlust", "Sportlust vs DVS 33", "DOVO vs Sportlust", "Sportlust vs HSC 21", "DEM vs Sportlust", "TEC vs Sportlust", "Sportlust vs IJsselmeervogel", "USV Hercules vs Sportlust", "Sportlust vs HBC", "DVS 33 vs Sportlust", "Sportlust vs DOVO", "HSC 21 vs Sportlust", "Sportlust vs DEM", "HSC 21 vs Urk", "Urk vs HBC", "DEM vs Urk", "Urk vs DVS 33", "Urk vs DOVO", "TEC vs Urk", "IJsselmeervogel vs Urk", "USV Hercules vs Urk", "Urk vs HSC 21", "HBC vs Urk", "Urk vs DEM", "DVS 33 vs Urk", "DOVO vs Urk", "Urk vs TEC", "Urk vs IJsselmeervogel", "Urk vs USV Hercules"], "teams_affected": ["Excelsior 31", "TEC", "Harkemase Boys", "IJsselmeervogel", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "HBC", "<PERSON><PERSON><PERSON>", "Sparta Nijkerk", "DEM", "Sportlust", "Ajax Amateurs", "DVS 33", "Eemdijk", "USV Hercules", "HSC 21", "Urk", "DOVO"], "total_matches": 320, "valid_matches": 168, "skipped_matches": 152}, "JAPAN_J1_LEAGUE": {"warning_count": 1, "missing_matches": ["<PERSON><PERSON><PERSON> vs Kyoto Sanga"], "teams_affected": ["<PERSON><PERSON><PERSON>", "Kyoto Sanga"], "total_matches": 13, "valid_matches": 12, "skipped_matches": 1}, "POLAND_2_LIGA": {"warning_count": 80, "missing_matches": ["LKS Lodz B vs Chojniczanka", "Pogon Grodzisk vs Chojniczanka", "SKRA Czestochow vs Chojniczanka", "Chojniczanka vs LKS Lodz B", "Chojniczanka vs Pogon Grodzisk", "Chojniczanka vs SKRA Czestochow", "SKRA Czestochow vs Hutnik Krakow", "Hutnik Krakow vs LKS Lodz B", "Pogon Grodzisk vs Hutnik Krakow", "Hutnik Krakow vs SKRA Czestochow", "LKS Lodz B vs Hutnik Krakow", "Jastrzebie vs Pogon Grodzisk", "SKRA Czestochow vs Jastrzebie", "Jastrzebie vs LKS Lodz B", "Pogon Grodzisk vs Jastrzebie", "Jastrzebie vs SKRA Czestochow", "Kalisz vs SKRA Czestochow", "LKS Lodz B vs Kalisz", "Kalisz vs Pogon Grodzisk", "SKRA Czestochow vs Kalisz", "Kalisz vs LKS Lodz B", "LKS Lodz B vs O. Grudziadz", "Pogon Grodzisk vs O. Grudziadz", "SKRA Czestochow vs <PERSON>. <PERSON>", "<PERSON>. Grudziadz vs LKS Lodz B", "O. Grudziadz vs Pogon Grodzisk", "Olimpia Elblag vs SKRA Czestochow", "LKS Lodz B vs Olimpia Elblag", "Olimpia Elblag vs Pogon Grodzisk", "SKRA Czestochow vs Olimpia Elblag", "Olimpia Elblag vs LKS Lodz B", "Pogon Grodzisk vs Podbeskidzie", "Podbeskidzie vs SKRA Czestochow", "LKS Lodz B vs Podbeskidzie", "Podbeskidzie vs Pogon Grodzisk", "SKRA Czestochow vs Podbeskidzie", "Pogon Grodzisk vs Polonia Bytom", "Polonia Bytom vs LKS Lodz B", "SKRA Czestochow vs Polonia Bytom", "Polonia Bytom vs Pogon Grodzisk", "Polonia Bytom vs SKRA Czestochow", "LKS Lodz B vs Polonia Bytom", "Rekord Bielsko vs LKS Lodz B", "Rekord Bielsko vs Pogon Grodzisk", "Rekord Bielsko vs SKRA Czestochow", "LKS Lodz B vs Rekord Bielsko", "Pogon Grodzisk vs Rekord Bielsko", "LKS Lodz B vs Resovia Rzeszow", "Pogon Grodzisk vs Resovia Rzeszow", "SKRA Czestochow vs Resovia Rzeszow", "Resovia Rzeszow vs LKS Lodz B", "Resovia Rzeszow vs Pogon Grodzisk", "Pogon Grodzisk vs Swit Skolwin", "<PERSON><PERSON><PERSON> vs SKRA Czestochow", "LKS Lodz B vs Swit Skolwin", "Swit Skolwin vs Pogon Grodzisk", "SKRA Czestochow vs Swit Skolwin", "Wieczysta K. vs LKS Lodz B", "Wieczysta K. vs Pogon Grodzisk", "Wieczysta K. vs SKRA Czestochow", "LKS Lodz B vs Wieczysta K.", "Pogon Grodzisk vs Wieczysta K.", "SKRA Czestochow vs Wieczysta K.", "LKS Lodz B vs Wisla Pulawy", "Pogon Grodzisk vs Wisla Pulawy", "SKRA Czestochow vs <PERSON><PERSON><PERSON>wy", "<PERSON><PERSON><PERSON> vs LKS Lodz B", "<PERSON><PERSON><PERSON> Pulawy vs Pogon Grodzisk", "<PERSON><PERSON><PERSON> vs SKRA Czestochow", "<PERSON>. So<PERSON>wi<PERSON> vs LKS Lodz B", "<PERSON><PERSON> vs SKRA Czestochow", "Z. Sosnowiec vs Pogon Grodzisk", "LKS Lodz B vs Z. Sosnowiec", "Pogon Grodzisk vs Z. Sosnowiec", "SKRA Czestochow vs Z. So<PERSON>nowiec", "Zaglebie L. B vs LKS Lodz B", "Zaglebie L. B vs Pogon Grodzisk", "Zaglebie L. B vs SKRA Czestochow", "LKS Lodz B vs Zaglebie L. B", "Pogon Grodzisk vs Zaglebie L. B"], "teams_affected": ["Olimpia Elblag", "Podbeskidzie", "Wieczysta K.", "Rekord Bielsko", "<PERSON><PERSON><PERSON>", "Pogon Grodzisk", "Kalisz", "Chojniczanka", "Hutnik Krakow", "Polonia Bytom", "Jastrzebie", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Zaglebie L. B", "Resovia <PERSON>zow", "LKS Lodz B", "<PERSON><PERSON>", "SKRA Czestochow"], "total_matches": 450, "valid_matches": 370, "skipped_matches": 80}, "NETHERLANDS_EREDIVISIE": {"warning_count": 141, "missing_matches": ["NAC Breda vs Ajax Amsterdam", "RKC Waalwijk vs Ajax Amsterdam", "Ajax Amsterdam vs FC Groningen", "Ajax Amsterdam vs Willem II", "Ajax Amsterdam vs PSV Eindhoven", "FC Twente vs Ajax Amsterdam", "Ajax Amsterdam vs PEC Zwolle", "NEC Nijmegen vs Ajax Amsterdam", "Ajax Amsterdam vs FC Utrecht", "AZ Alkmaar vs Ajax Amsterdam", "Ajax Amsterdam vs RKC Waalwijk", "PEC Zwolle vs Ajax Amsterdam", "Ajax Amsterdam vs AZ Alkmaar", "PSV Eindhoven vs Ajax Amsterdam", "Ajax Amsterdam vs NAC Breda", "Willem II vs Ajax Amsterdam", "FC Utrecht vs Ajax Amsterdam", "Almere City vs AZ Alkmaar", "Almere City vs PSV Eindhoven", "Almere City vs FC Groningen", "Almere City vs FC Twente", "PEC Zwolle vs Almere City", "Almere City vs Willem II", "Almere City vs NEC Nijmegen", "RKC Waalwijk vs Almere City", "NAC Breda vs Almere City", "Almere City vs FC Utrecht", "FC Groningen vs Almere City", "Almere City vs RKC Waalwijk", "FC Utrecht vs Almere City", "NEC Nijmegen vs Almere City", "FC Twente vs Almere City", "Almere City vs NAC Breda", "Willem II vs Almere City", "Almere City vs PEC Zwolle", "PSV Eindhoven vs Almere City", "Feyenoord vs Willem II", "PEC Zwolle vs Feyenoord", "FC Groningen vs Feyenoord", "Feyenoord vs NAC Breda", "NEC Nijmegen vs Feyenoord", "Feyenoord vs FC Twente", "FC Utrecht vs Feyenoord", "Feyenoord vs AZ Alkmaar", "RKC Waalwijk vs Feyenoord", "PSV Eindhoven vs Feyenoord", "Feyenoord vs FC Utrecht", "Willem II vs Feyenoord", "NAC Breda vs Feyenoord", "Feyenoord vs NEC Nijmegen", "FC Twente vs Feyenoord", "Feyenoord vs FC Groningen", "AZ Alkmaar vs Feyenoord", "Feyenoord vs PEC Zwolle", "Fortuna Sittard vs NEC Nijmegen", "NAC Breda vs Fortuna Sittard", "Fortuna Sittard vs PSV Eindhoven", "Fortuna Sittard vs AZ Alkmaar", "Willem II vs Fortuna Sittard", "Fortuna Sittard vs FC Groningen", "PEC Zwolle vs Fortuna Sittard", "Fortuna Sittard vs FC Twente", "Fortuna Sittard vs RKC Waalwijk", "FC Utrecht vs Fortuna Sittard", "NEC Nijmegen vs Fortuna Sittard", "Fortuna Sittard vs PEC Zwolle", "AZ Alkmaar vs Fortuna Sittard", "RKC Waalwijk vs Fortuna Sittard", "FC Groningen vs Fortuna Sittard", "FC Twente vs Fortuna Sittard", "Fortuna Sittard vs Willem II", "Willem II vs Go Ahead Eagles", "Go Ahead Eagles vs RKC Waalwijk", "PSV Eindhoven vs Go Ahead Eagles", "FC Groningen vs Go Ahead Eagles", "AZ Alkmaar vs Go Ahead Eagles", "Go Ahead Eagles vs PEC Zwolle", "FC Twente vs Go Ahead Eagles", "Go Ahead Eagles vs NEC Nijmegen", "FC Utrecht vs Go Ahead Eagles", "Go Ahead Eagles vs NAC Breda", "Go Ahead Eagles vs FC Groningen", "Go Ahead Eagles vs FC Twente", "Go Ahead Eagles vs PSV Eindhoven", "NEC Nijmegen vs Go Ahead Eagles", "Go Ahead Eagles vs Willem II", "Go Ahead Eagles vs FC Utrecht", "NAC Breda vs Go Ahead Eagles", "Heerenveen vs FC Utrecht", "Heerenveen vs NAC Breda", "AZ Alkmaar vs Heerenveen", "FC Twente vs Heerenveen", "Heerenveen vs FC Groningen", "Heerenveen vs PEC Zwolle", "NEC Nijmegen vs Heerenveen", "Heerenveen vs RKC Waalwijk", "Willem II vs Heerenveen", "Heerenveen vs PSV Eindhoven", "NAC Breda vs Heerenveen", "FC Groningen vs Heerenveen", "Heerenveen vs FC Twente", "PEC Zwolle vs Heerenveen", "Heerenveen vs AZ Alkmaar", "PSV Eindhoven vs Heerenveen", "FC Utrecht vs Heerenveen", "Heerenveen vs Willem II", "Heerenveen vs NEC Nijmegen", "Heracles Almelo vs PSV Eindhoven", "Heracles Almelo vs Willem II", "PEC Zwolle vs Heracles Almelo", "NEC Nijmegen vs Heracles Almelo", "FC Twente vs Heracles Almelo", "Heracles Almelo vs NAC Breda", "FC Utrecht vs Heracles Almelo", "Heracles Almelo vs RKC Waalwijk", "AZ Alkmaar vs Heracles Almelo", "Heracles Almelo vs FC Utrecht", "Heracles Almelo vs FC Groningen", "NAC Breda vs Heracles Almelo", "Heracles Almelo vs PEC Zwolle", "Heracles Almelo vs FC Twente", "RKC Waalwijk vs Heracles Almelo", "Heracles Almelo vs AZ Alkmaar", "FC Groningen vs Heracles Almelo", "FC Twente vs Sparta", "Willem II vs Sparta", "RKC Waalwijk vs Sparta", "PSV Eindhoven vs Sparta", "Sparta vs FC Utrecht", "FC Groningen vs Sparta", "Sparta vs AZ Alkmaar", "PEC Zwolle vs Sparta", "Sparta vs NAC Breda", "NEC Nijmegen vs Sparta", "Sparta vs RKC Waalwijk", "AZ Alkmaar vs Sparta", "Sparta vs FC Groningen", "Sparta vs Willem II", "NAC Breda vs Sparta", "Sparta vs PEC Zwolle", "Sparta vs NEC Nijmegen"], "teams_affected": ["Heerenveen", "Ajax Amsterdam", "PEC Zwolle", "Feyenoord", "RKC Waalwijk", "Willem II", "FC Utrecht", "FC Twente", "NAC Breda", "FC Groningen", "PSV Eindhoven", "NEC Nijmegen", "Fortuna Sittard", "Go Ahead Eagles", "Heracles Almelo", "Sparta", "Almere City", "AZ Alkmaar"], "total_matches": 243, "valid_matches": 102, "skipped_matches": 141}, "SPAIN_LA_LIGA": {"warning_count": 65, "missing_matches": ["Alaves vs Sevilla FC", "Alaves vs FC Barcelona", "FC Barcelona vs Alaves", "Sevilla FC vs Alaves", "FC Barcelona vs Athletic Bilbao", "Athletic Bilbao vs Sevilla FC", "Sevilla FC vs Athletic Bilbao", "Atletico Madrid vs Sevilla FC", "FC Barcelona vs Atletico Madrid", "Atletico Madrid vs FC Barcelona", "Sevilla FC vs Atletico Madrid", "Celta Vigo vs FC Barcelona", "Sevilla FC vs Celta Vigo", "FC Barcelona vs Celta Vigo", "Celta Vigo vs Sevilla FC", "Espanyol vs Sevilla FC", "FC Barcelona vs Espanyol", "Sevilla FC vs Espanyol", "Sevilla FC vs Getafe", "FC Barcelona vs Getafe", "Getafe vs FC Barcelona", "Getafe vs Sevilla FC", "Sevilla FC vs Girona", "Girona vs FC Barcelona", "Girona vs Sevilla FC", "FC Barcelona vs Girona", "Las Palmas vs Sevilla FC", "FC Barcelona vs Las Palmas", "Las Palmas vs FC Barcelona", "Leganes vs Sevilla FC", "FC Barcelona vs Leganes", "Leganes vs FC Barcelona", "Sevilla FC vs Leganes", "Mallorca vs Sevilla FC", "Mallorca vs FC Barcelona", "Sevilla FC vs Mallorca", "FC Barcelona vs Mallorca", "Osasuna vs FC Barcelona", "Sevilla FC vs Osasuna", "FC Barcelona vs Osasuna", "Osasuna vs Sevilla FC", "Rayo Vallecano vs FC Barcelona", "Sevilla FC vs Rayo Vallecano", "FC Barcelona vs Rayo Vallecano", "Rayo Vallecano vs Sevilla FC", "Sevilla FC vs Real Betis", "Real Betis vs FC Barcelona", "Real Betis vs Sevilla FC", "FC Barcelona vs Real Betis", "Real Madrid vs FC Barcelona", "Real Madrid vs Sevilla FC", "Sevilla FC vs Real Sociedad", "Real Sociedad vs FC Barcelona", "FC Barcelona vs Real Sociedad", "Real Sociedad vs Sevilla FC", "Valencia vs FC Barcelona", "Sevilla FC vs Valencia", "FC Barcelona vs Valencia", "Valencia vs Sevilla FC", "FC Barcelona vs Valladolid", "Sevilla FC vs Valladolid", "Valladolid vs Sevilla FC", "Valladolid vs FC Barcelona", "Sevilla FC vs Villarreal", "Villarreal vs FC Barcelona"], "teams_affected": ["Real Sociedad", "<PERSON><PERSON><PERSON><PERSON>", "Valencia", "Espanyol", "Athletic Bilbao", "Las Palmas", "Girona", "Real Madrid", "Atletico Madrid", "Real Betis", "Sevilla FC", "Mallorca", "Celta Vigo", "Rayo Vallecano", "Getafe", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "FC Barcelona", "Valladoli<PERSON>", "<PERSON><PERSON><PERSON>"], "total_matches": 625, "valid_matches": 560, "skipped_matches": 65}, "NORTHERN_IRELAND_CHAMPIONSHIP": {"warning_count": 70, "missing_matches": ["HW Welders vs Annagh Utd", "<PERSON><PERSON> vs Ballinamallard", "<PERSON><PERSON> vs HW Welders", "<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "HW Welders vs Annagh Utd", "<PERSON><PERSON> vs Ballinamallard", "<PERSON><PERSON> vs HW Welders", "Ballinamallard vs Ards", "Ards vs HW Welders", "HW Welders vs Ards", "Ards vs Ballinamallard", "Ballinamallard vs Ards", "HW Welders vs Ards", "Ards vs HW Welders", "HW Welders vs Armagh City", "Ballinamallard vs Armagh City", "Armagh City vs Ballinamallard", "Armagh City vs HW Welders", "Armagh City vs HW Welders", "Ballinamallard vs Armagh City", "Armagh City vs Ballinamallard", "Ballinamallard vs Ballyclare", "Ballyclare vs HW Welders", "HW Welders vs Ballyclare", "Ballyclare vs Ballinamallard", "HW Welders vs Ballyclare", "Ballinamallard vs Ballyclare", "Ballyclare vs Ballinamallard", "Bangor vs HW Welders", "Ballinamallard vs Bangor", "HW Welders vs Bangor", "Bangor vs Ballinamallard", "Bangor vs Ballinamallard", "Bangor vs HW Welders", "HW Welders vs Bangor", "HW Welders vs Dundela", "Dundela vs Ballinamallard", "Ballinamallard vs Dundela", "Dundela vs HW Welders", "Ballinamallard vs Dundela", "Dundela vs HW Welders", "HW Welders vs Dundela", "Institute vs HW Welders", "<PERSON><PERSON><PERSON><PERSON> vs Institute", "Institute vs Ballinamallard", "HW Welders vs Institute", "Institute vs HW Welders", "Institute vs Ballinamallard", "<PERSON><PERSON><PERSON><PERSON> vs Institute", "Limavady vs Ballinamallard", "HW Welders vs Limavady", "Ballinamallard vs Limavady", "Limavady vs HW Welders", "Limavady vs Ballinamallard", "Limavady vs HW Welders", "HW Welders vs Limavady", "Ballinamallard vs Newington", "Newington vs HW Welders", "Newington vs Ballinamallard", "HW Welders vs Newington", "Ballinamallard vs Newington", "Newington vs HW Welders", "Newington vs Ballinamallard", "Newry City vs Ballinamallard", "HW Welders vs Newry City", "Newry City vs HW Welders", "Ballinamallard vs Newry City", "HW Welders vs Newry City", "Ballinamallard vs Newry City", "Newry City vs Ballinamallard"], "teams_affected": ["Ballyclare", "Newry City", "Limavady", "Annagh Utd", "HW Welders", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Newington", "Ards", "Bangor", "Institute", "Armagh City"], "total_matches": 380, "valid_matches": 310, "skipped_matches": 70}, "ESTONIA_ESILIIGA_B": {"warning_count": 4, "missing_matches": ["Paide B vs Legion", "Maardu vs Narva Trans B", "Narva Trans B vs Maardu", "Tartu Kalev vs Tabasalu"], "teams_affected": ["Narva Trans B", "Tart<PERSON>", "<PERSON><PERSON><PERSON>", "Paide B", "Legion", "Tabasalu"], "total_matches": 7, "valid_matches": 3, "skipped_matches": 4}, "GERMANY_OBERLIGA_MITTELRHEIN": {"warning_count": 90, "missing_matches": ["SSV Merten vs Bergisch G.", "Bergisch G. vs VfL Vichttal", "Bergisch G. vs Wegberg<PERSON><PERSON><PERSON>", "Bergisch G. vs Bonn-Endenich", "Bonner SC vs Bergisch G.", "Bergisch G. vs SSV Merten", "VfL Vichttal vs Bergisch G.", "Wegberg<PERSON><PERSON><PERSON> vs Bergisch G.", "Fortuna Koln B vs Bonn-Endenich", "Bonner SC vs Fortuna Koln B", "VfL Vichttal vs Fortuna Koln B", "SSV Merten vs Fortuna Koln B", "Fortuna Koln B vs Wegberg-Beeck", "Bonn-Endenich vs Fortuna Koln B", "Fortuna Koln B vs Bonner SC", "Fortuna Koln B vs VfL Vichttal", "Frechen vs Bonn-Endenich", "Bonner SC vs Frechen", "VfL Vichttal vs Frechen", "SSV Merten vs Frechen", "Frechen vs Wegberg-Beeck", "Bonn-<PERSON><PERSON><PERSON> vs Frechen", "Frechen vs Bonner SC", "Frechen vs VfL Vichttal", "Frechen vs SSV Merten", "Bonn<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> vs Bonner SC", "<PERSON><PERSON>f vs VfL Vichttal", "SSV Merten vs <PERSON>nnef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> vs Bonn-Endenich", "Bonner SC vs Hennef", "VfL Vichttal vs Hennef", "<PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Hurt<PERSON> vs SSV Merten", "Bonn-<PERSON><PERSON><PERSON> vs Hurt<PERSON>", "Hurt<PERSON> vs Bonner SC", "Hurth vs VfL Vichttal", "<PERSON>g<PERSON>-<PERSON><PERSON> vs <PERSON><PERSON>", "SSV Merten vs Hurth", "Konigsdorf vs Wegberg-Beeck", "Konigsdorf vs SSV Merten", "Bonn-Endenich vs Konigsdorf", "Konigsdorf vs Bonner SC", "Konigsdorf vs VfL Vichttal", "Wegberg-<PERSON><PERSON> vs Konigsdorf", "SSV Merten vs Konigsdorf", "Konigsdorf vs Bonn-Endenich", "Bonner SC vs Konigsdorf", "Wegberg-<PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "<PERSON><PERSON>ch vs SSV Merten", "<PERSON><PERSON><PERSON> vs Bonn-Endenich", "Bonner SC vs Pesch", "VfL Vichttal vs Pesch", "<PERSON><PERSON><PERSON> vs Wegberg<PERSON><PERSON><PERSON>", "SSV Merten vs Pesch", "Bonn-<PERSON><PERSON><PERSON> vs <PERSON><PERSON>ch", "Bonn-Endenich vs Porz", "Porz vs Bonner SC", "Porz vs VfL Vichttal", "SSV Merten vs Porz", "Wegberg-<PERSON><PERSON> vs Porz", "Porz vs Bonn-Endenich", "Bonner SC vs Porz", "VfL Vichttal vs Porz", "Porz vs SSV Merten", "Schafhausen vs Bonn-Endenich", "Bonner SC vs Schafhausen", "VfL Vichttal vs Schafhausen", "SSV Merten vs Schafhausen", "Wegberg-Beeck vs Schafhausen", "Bonn-Endenich vs Schafhausen", "Schafhausen vs Bonner SC", "Schafhausen vs VfL Vichttal", "Schafhausen vs SSV Merten", "<PERSON>g<PERSON><PERSON><PERSON><PERSON> vs Siegburger", "Siegburger vs SSV Merten", "Siegburger vs Bonn-Endenich", "Bonner SC vs Siegburger", "VfL Vichttal vs Siegburger", "Siegburger vs Wegberg-Beeck", "Wegberg<PERSON><PERSON><PERSON> vs Teutonia Weiden", "Teutonia Weiden vs SSV Merten", "Teutonia Weiden vs Bonn-Endenich", "Bonner SC vs Teutonia Weiden", "VfL Vichttal vs Teutonia Weiden", "Teutonia Weiden vs Wegberg-Beeck", "SSV Merten vs Teutonia Weiden", "Bonn-End<PERSON><PERSON> vs Teutonia Weiden", "Teutonia Weiden vs Bonner SC"], "teams_affected": ["Bergisch G.", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Fortuna Koln B", "Frechen", "Siegburger", "Schafhausen", "Konigsdorf", "Teutonia Weiden", "VfL Vichttal", "Wegberg-<PERSON><PERSON>", "SSV Merten", "Bonner SC", "Bonn-Endenich"], "total_matches": 264, "valid_matches": 174, "skipped_matches": 90}, "ROMANIA_LIGA_I": {"warning_count": 110, "missing_matches": ["Botosani vs UTA Arad", "CFR Cluj vs Botosani", "Sepsi OSK vs Botosani", "Botosani vs FC Buzau", "Botosani vs FCSB", "FCSB vs Botosani", "UTA Arad vs Botosani", "Botosani vs CFR Cluj", "Botosani vs Sepsi OSK", "FC Buzau vs Botosani", "CFR Cluj vs Din. Bucharest", "Sepsi OSK vs Din. Bucharest", "Din. Bucharest vs FC Buzau", "Din. Bucharest vs FCSB", "Din. Bucharest vs UTA Arad", "Din. Bucharest vs CFR Cluj", "Din. Bucharest vs Sepsi OSK", "FC Buzau vs Din. Bucharest", "FCSB vs Din. Bucharest", "UTA Arad vs Din. Bucharest", "FCSB vs Farul Constanta", "UTA Arad vs Farul Constanta", "Farul Constanta vs CFR Cluj", "Farul Constanta vs Sepsi OSK", "FC Buzau vs Farul Constanta", "Farul Constanta vs FCSB", "Farul Constanta vs UTA Arad", "CFR Cluj vs Farul Constanta", "Sepsi OSK vs Farul Constanta", "Farul Constanta vs FC Buzau", "Hermannstadt vs FCSB", "UTA Arad vs Hermannstadt", "Hermannstadt vs CFR Cluj", "Hermannstadt vs Sepsi OSK", "FC Buzau vs Hermannstadt", "FCSB vs Hermannstadt", "Hermannstadt vs UTA Arad", "CFR Cluj vs Hermannstadt", "Sepsi OSK vs Hermannstadt", "Hermannstadt vs FC Buzau", "FCSB vs Otelul", "UTA Arad vs Otelul", "Otelul vs Sepsi OSK", "FC Buzau vs Otelul", "Otelul vs CFR Cluj", "Otelul vs FCSB", "Otelul vs UTA Arad", "CFR Cluj vs Otelul", "Sepsi OSK vs Otelul", "Otelul vs FC Buzau", "Sepsi OSK vs <PERSON><PERSON> <PERSON>", "<PERSON><PERSON> vs FC Buzau", "FCSB vs P. Iasi", "UTA Arad vs <PERSON><PERSON> <PERSON>", "CFR Cluj vs <PERSON><PERSON>", "<PERSON><PERSON> vs Sepsi OSK", "FC Buzau vs P. Iasi", "<PERSON><PERSON> vs FCSB", "<PERSON><PERSON> vs UTA Arad", "<PERSON><PERSON> vs CFR Cluj", "Petrolul vs FC Buzau", "FCSB vs Petrolul", "UTA Arad vs Petrolul", "Petrolul vs CFR Cluj", "Sepsi OSK vs Petrolul", "FC Buzau vs Petrolul", "Petrolul vs FCSB", "Petrolul vs UTA Arad", "CFR Cluj vs Petrolul", "Petrolul vs Sepsi OSK", "UTA Arad vs Rapid Bucharest", "Rapid Bucharest vs CFR Cluj", "Rapid Bucharest vs Sepsi OSK", "FC Buzau vs Rapid Bucharest", "FCSB vs Rapid Bucharest", "Rapid Bucharest vs UTA Arad", "CFR Cluj vs Rapid Bucharest", "Sepsi OSK vs Rapid Bucharest", "Rapid Bucharest vs FC Buzau", "Rapid Bucharest vs FCSB", "FCSB vs U. Cluj", "UTA Arad vs U. Cluj", "CFR Cluj vs U. Cluj", "U. Cluj vs Sepsi OSK", "FC Buzau vs U. Cluj", "U. Cluj vs FCSB", "<PERSON><PERSON>lu<PERSON> vs UTA Arad", "U. Cluj vs CFR Cluj", "Sepsi OSK vs U. Cluj", "U. Cluj vs FC Buzau", "Unirea Slobozia vs FCSB", "Unirea Slobozia vs UTA Arad", "CFR Cluj vs Unirea Slobozia", "Sepsi OSK vs Unirea Slobozia", "Unirea Slobozia vs FC Buzau", "FCSB vs Unirea Slobozia", "UTA Arad vs Unirea Slobozia", "Unirea Slobozia vs CFR Cluj", "Unirea Slobozia vs Sepsi OSK", "FC Buzau vs Unirea Slobozia", "Univ. Craiova vs UTA Arad", "CFR Cluj vs Univ. Craiova", "Sepsi OSK vs Univ. Craiova", "Univ. Craiova vs FC Buzau", "Univ. Craiova vs FCSB", "UTA Arad vs Univ. Craiova", "Univ. Craiova vs CFR Cluj", "Univ. Craiova vs Sepsi OSK", "FC Buzau vs Univ. Craiova", "FCSB vs Univ. Craiova"], "teams_affected": ["Sepsi OSK", "FC Buzau", "UTA Arad", "Univ. Craiova", "<PERSON><PERSON> Constant<PERSON>", "Petrolul", "CFR Cluj", "U. <PERSON>lu<PERSON>", "Otelul", "Hermannstadt", "<PERSON><PERSON>", "Rapid Bucharest", "<PERSON><PERSON><PERSON>", "Unirea Slobozia", "FCSB", "Din. Bucharest"], "total_matches": 330, "valid_matches": 220, "skipped_matches": 110}, "GERMANY_OBERLIGA_BADEN_WURTTEMBERG": {"warning_count": 77, "missing_matches": ["TSV Essingen vs Aalen", "Aalen vs VfR Mannheim", "Aalen vs Leinfelden-E.", "Aalen vs TSV Essingen", "VfR Mannheim vs Aalen", "VfR Mann<PERSON> vs Backnang", "Backnang vs Leinfelden-E.", "TSV Essingen vs Backnang", "<PERSON>nang vs VfR Mannheim", "Leinfelden-E. vs Backnang", "Balingen vs Leinfelden-E.", "TSV Essingen vs Balingen", "Balingen vs VfR Mannheim", "Leinfelden-E. vs Balingen", "Balingen vs TSV Essingen", "VfR Mannheim vs Balingen", "Leinfelden-E. vs Bissingen", "TSV Essingen vs Bissingen", "Bissingen vs VfR Mannheim", "Bissingen vs Leinfelden-E.", "Bissingen vs TSV Essingen", "Fellbach vs TSV Essingen", "VfR Mannheim vs Fellbach", "Leinfelden-E. vs Fellbach", "TSV Essingen vs Fellbach", "Fellbach vs VfR Mannheim", "Leinfelden-E. vs Grossaspach", "Grossaspach vs TSV Essingen", "VfR Mannheim vs Grossaspach", "Grossaspach vs Leinfelden-E.", "TSV Essingen vs Grossaspach", "Grossaspach vs VfR Mannheim", "Hollenbach vs TSV Essingen", "VfR Mannheim vs Hollenbach", "Leinfelden-E. vs Hollenbach", "TSV Essingen vs Hollenbach", "Hollenbach vs VfR Mannheim", "Normannia Gmund vs Leinfelden-E.", "Normannia Gmund vs TSV Essingen", "VfR Mannheim vs Normannia Gmund", "Leinfelden-E. vs Normannia Gmund", "Leinfelden-E. vs Nottingen", "TSV Essingen vs Nottingen", "Nottingen vs VfR Mannheim", "Nottingen vs Leinfelden-E.", "Nottingen vs TSV Essingen", "Oberachern vs VfR Mannheim", "Leinfelden-E. vs Oberachern", "TSV Essingen vs Oberachern", "VfR Mann<PERSON> vs Oberachern", "Oberachern vs Leinfelden-E.", "TSV Essingen vs Pforzheim", "Pforzheim vs VfR Mannheim", "Pforzheim vs Leinfelden-E.", "Pforzheim vs TSV Essingen", "VfR Mannheim vs Pforzheim", "Ravensburg vs VfR Mannheim", "Leinfelden-E. vs Ravensburg", "Ravensburg vs TSV Essingen", "VfR Mannheim vs Ravensburg", "Ravensburg vs Leinfelden-E.", "Reutlingen vs Leinfelden-E.", "Reutlingen vs TSV Essingen", "VfR Mannheim vs Reutlingen", "Leinfelden-E. vs Reutlingen", "TSV Essingen vs Reutlingen", "Villingen B vs TSV Essingen", "VfR Mannheim vs Villingen B", "Leinfelden-E. vs Villingen B", "TSV Essingen vs Villingen B", "Villingen B vs VfR Mannheim", "Zuzenhausen vs Leinfelden-E.", "TSV Essingen vs Zuzenhausen", "Zuzenhausen vs VfR Mannheim", "Leinfelden-E. vs Zuzenhausen", "Zuzenhausen vs TSV Essingen", "VfR Mannheim vs Zuzenhausen"], "teams_affected": ["Grossaspach", "Pforzheim", "Ravensburg", "Leinfelden-E.", "Villingen B", "<PERSON><PERSON><PERSON>", "Zuzenhausen", "A<PERSON>n", "TSV Essingen", "Normannia Gmund", "Nottingen", "Reutlingen", "Balingen", "VfR Mannheim", "Oberachern", "Hollenbach", "Fellbach", "Bissingen"], "total_matches": 433, "valid_matches": 356, "skipped_matches": 77}, "FRANCE_NATIONAL_2_GROUP_C": {"warning_count": 105, "missing_matches": ["Aubervilliers vs Fleury-Merogis", "Feignies-A. vs Aubervilliers", "Furiani-Agliani vs Aubervilliers", "Aubervilliers vs US Chantilly", "Villers H. vs Aubervilliers", "Fleury-Merogis vs Aubervilliers", "Aubervilliers vs Feignies-A.", "Aubervilliers vs Furiani-Agliani", "US Chantilly vs Aubervilliers", "Aubervilliers vs Villers H.", "Balagne vs Furiani-Agliani", "US Chantilly vs Balagne", "Balagne vs Villers H.", "Balagne vs Fleury-Merogis", "Feignies-A. vs Balagne", "Furiani<PERSON><PERSON><PERSON><PERSON> vs Balagne", "<PERSON><PERSON><PERSON> vs US Chantilly", "Villers H. vs Balagne", "F<PERSON>ury<PERSON><PERSON><PERSON><PERSON> vs Balagne", "Balagne vs Feignies-A.", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs Beauvais", "<PERSON><PERSON><PERSON> vs US Chantilly", "Villers H. vs Beauvais", "<PERSON><PERSON>ury<PERSON><PERSON><PERSON><PERSON> vs Beauvais", "Beauvais vs Feignies-A.", "Beauvais vs Furiani-Agliani", "US Chantilly vs Beauvais", "Beauvais vs Villers H.", "Beau<PERSON><PERSON> vs Fleury-Merogis", "Feignies-A. vs Beauvais", "Biesheim vs US Chantilly", "Biesheim vs Villers H.", "Furiani<PERSON><PERSON><PERSON>ni vs Biesheim", "Biesheim vs Fleury-Merogis", "Villers H. vs Biesheim", "Feignies-A. vs Biesheim", "Biesheim vs Furiani-Agliani", "Fleury-<PERSON><PERSON><PERSON> vs Biesheim", "Biesheim vs Feignies-A.", "Bobigny vs Feignies-A.", "Bobigny vs Furiani-Agliani", "US Chantilly vs <PERSON><PERSON><PERSON>", "Bobigny vs Villers H.", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs Bobigny", "<PERSON><PERSON><PERSON> vs US Chantilly", "<PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "<PERSON>igny vs Fleury-Merogis", "Chambly vs Fleury-Merogis", "Feignies-A. vs Chambly", "Chambly vs Furiani-Agliani", "Chambly vs US Chantilly", "Villers H<PERSON> vs Chambly", "F<PERSON>ury<PERSON><PERSON><PERSON><PERSON> vs Chambly", "Chambly vs Feignies-A.", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs Chambly", "US Chantilly vs Chambly", "Chambly vs Villers H.", "US Chantilly vs Creteil", "Creteil vs Villers H.", "Creteil vs Fleury-Merogis", "Feignies-A. vs Creteil", "Creteil vs Furiani-Agliani", "Creteil vs US Chantilly", "Villers H. vs Creteil", "Fleury-<PERSON><PERSON><PERSON> vs Creteil", "Creteil vs Feignies-A.", "Furiani-A<PERSON>ni vs Creteil", "<PERSON><PERSON>ury<PERSON><PERSON><PERSON><PERSON> vs Epinal", "Epinal vs Feignies-A.", "Epinal vs Furiani-Agliani", "US Chantilly vs Epinal", "Epinal vs Villers H.", "Feignies-A. vs Epinal", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs Epinal", "Epinal vs Fleury-Merogis", "Epinal vs US Chantilly", "Villers H. vs Epinal", "Haguenau vs Fleury-Merogis", "Feignies-A. vs Haguenau", "Haguenau vs US Chantilly", "Villers H. vs Haguenau", "Furiani-Agliani vs Haguenau", "Haguenau vs Feignies-A.", "Haguenau vs Furiani-Agliani", "US Chantilly vs Haguenau", "Haguenau vs Villers H.", "Furiani<PERSON><PERSON><PERSON> vs Thionville L.", "Thionville L. vs US Chantilly", "Villers H. vs Thionville L.", "Fleury<PERSON><PERSON><PERSON><PERSON> vs Thionville L.", "Thionville L. vs Feignies-A.", "US Chantilly vs Thionville L.", "Thionville L. vs Villers H.", "Thionville L. vs Fleury-Me<PERSON>is", "Feignies-A. vs Thionville L.", "<PERSON><PERSON><PERSON> vs Villers H.", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> vs Feignies-A.", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "US Chantilly vs <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> vs Fleury-Merogis", "Feignies-A. vs <PERSON><PERSON><PERSON>", "Wasquehal vs Furiani-Agliani", "<PERSON><PERSON><PERSON> vs US Chantilly"], "teams_affected": ["Biesheim", "Epinal", "Creteil", "Feignies-A.", "<PERSON><PERSON>ury<PERSON><PERSON><PERSON><PERSON>", "US Chantilly", "Cha<PERSON>ly", "Furiani<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Thionville L.", "<PERSON><PERSON><PERSON>", "Haguenau", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>.", "Aubervilliers", "<PERSON><PERSON><PERSON>"], "total_matches": 319, "valid_matches": 214, "skipped_matches": 105}, "UKRAINE_PREMIER_LEAGUE": {"warning_count": 52, "missing_matches": ["Kryvbas KR vs Chornomorets", "LNZ Cherkasy vs Chornomorets", "Chornomorets vs Kryvbas KR", "Chornomorets vs LNZ Cherkasy", "Dynamo Kyiv vs LNZ Cherkasy", "Dynamo Kyiv vs Kryvbas KR", "LNZ Cherkasy vs Dynamo Kyiv", "Kryvbas KR vs Dynamo Kyiv", "Kryvbas KR vs Inhulets", "LNZ Cherkasy vs Inhulets", "Inhulets vs Kryvbas KR", "Inhulets vs LNZ Cherkasy", "Karpaty Lviv vs Kryvbas KR", "LNZ Cherkasy vs Karpaty Lviv", "Kryvbas KR vs Karpaty Lviv", "Karpaty Lviv vs LNZ Cherkasy", "<PERSON><PERSON> vs LNZ Cherkasy", "<PERSON><PERSON> vs Kryvbas KR", "LNZ Cherkasy vs <PERSON><PERSON>", "Kryvbas KR vs <PERSON><PERSON>", "LNZ Cherkasy vs Livyi Bereh", "Livyi Bereh vs Kryvbas KR", "Livyi Bereh vs LNZ Cherkasy", "Kryvbas KR vs Livyi Bereh", "LNZ Cherkasy vs Obolon", "Obolon vs Kryvbas KR", "Obolon vs LNZ Cherkasy", "LNZ Cherkasy vs Oleksandria", "Kryvbas KR vs Oleksandria", "Oleksandria vs LNZ Cherkasy", "Oleksandria vs Kryvbas KR", "Polessya vs LNZ Cherkasy", "Kryvbas KR vs Polessya", "LNZ Cherkasy vs Polessya", "LNZ Cherkasy vs Rukh Lviv", "Kryvbas KR vs Rukh Lviv", "Rukh Lviv vs LNZ Cherkasy", "Rukh Lviv vs Kryvbas KR", "<PERSON><PERSON><PERSON><PERSON> vs LNZ Cherkasy", "Shakhtar vs Kryvbas KR", "LNZ Cherkasy vs Shakhtar", "Kryvbas KR vs Shakhtar", "Veres vs Kryvbas KR", "LNZ Cherkasy vs Veres", "Kryvbas KR vs Veres", "Kryvbas KR vs Vorskla Poltava", "Vorskla Poltava vs LNZ Cherkasy", "Vorskla Poltava vs Kryvbas KR", "Zorya Luhansk vs Kryvbas KR", "Zorya Luhansk vs LNZ Cherkasy", "Kryvbas KR vs Zorya Luhansk", "LNZ Cherkasy vs Zorya Luhansk"], "teams_affected": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Karpaty Lviv", "<PERSON><PERSON>", "Rukh Lviv", "LNZ Cherkasy", "Oleksandria", "Zorya Luhansk", "Polessya", "Dynamo Kyiv", "Liv<PERSON>", "Inhulets", "Kryvbas KR", "Vorskla Poltava", "Chornomorets", "Obolon"], "total_matches": 392, "valid_matches": 340, "skipped_matches": 52}, "SOUTH_KOREA_K_LEAGUE_1": {"warning_count": 3, "missing_matches": ["Daegu vs Anyang", "Gwangju vs Anyang", "Anyang vs Suwon City"], "teams_affected": ["Anyang", "Suwon City", "Gwangju", "Daegu"], "total_matches": 29, "valid_matches": 26, "skipped_matches": 3}, "PORTUGAL_LIGA_NOS": {"warning_count": 87, "missing_matches": ["Arouca vs Sporting CP", "FC Porto vs Arouca", "Arouca vs AVS", "Sporting CP vs Arouca", "Arouca vs FC Porto", "AVS vs Arouca", "Benfica vs FC Porto", "AVS vs Benfica", "Sporting CP vs Benfica", "FC Porto vs Benfica", "Benfica vs AVS", "Benfica vs Sporting CP", "Sporting CP vs Boavista", "Boavista vs AVS", "FC Porto vs Boavista", "Boavista vs Sporting CP", "AVS vs Boavista", "Boavista vs FC Porto", "Sporting CP vs Casa Pia", "FC Porto vs Casa Pia", "Casa Pia vs AVS", "Casa Pia vs Sporting CP", "Casa Pia vs FC Porto", "AVS vs Casa Pia", "Estoril vs Sporting CP", "FC Porto vs Estoril", "Estoril vs AVS", "Sporting CP vs Estoril", "Estoril vs FC Porto", "AVS vs Estoril", "Sporting CP vs Estrela Amadora", "FC Porto vs Estrela Amadora", "AVS vs Estrela Amadora", "Estrela Amadora vs Sporting CP", "Estrela Amadora vs FC Porto", "Estrela Amadora vs AVS", "Famalicao vs Sporting CP", "AVS vs Famalicao", "Famalicao vs FC Porto", "Sporting CP vs Famalicao", "Famalicao vs AVS", "FC Porto vs Famalicao", "Farense vs Sporting CP", "FC Porto vs Farense", "AVS vs Farense", "Sporting CP vs Farense", "Farense vs FC Porto", "Farense vs AVS", "FC Porto vs Gil Vicente", "<PERSON> vs AVS", "<PERSON> vs Sporting CP", "<PERSON> vs FC Porto", "AVS vs Gil Vicente", "Sporting CP vs Gil Vicente", "AVS vs Guimaraes", "Guimaraes vs FC Porto", "Guimaraes vs Sporting CP", "G<PERSON><PERSON>es vs AVS", "FC Porto vs Guimaraes", "Moreirense vs Sporting CP", "Moreirense vs FC Porto", "Moreirense vs AVS", "Sporting CP vs Moreirense", "FC Porto vs Moreirense", "AVS vs Nacional", "Nacional vs Sporting CP", "Nacional vs FC Porto", "Nacional vs AVS", "Sporting CP vs Nacional", "Sporting CP vs Rio Ave", "FC Porto vs Rio Ave", "AVS vs Rio Ave", "Rio Ave vs Sporting CP", "Rio Ave vs FC Porto", "Rio Ave vs AVS", "Santa Clara vs FC Porto", "Santa Clara vs AVS", "Sporting CP vs Santa Clara", "FC Porto vs Santa Clara", "AVS vs Santa Clara", "Santa Clara vs Sporting CP", "FC Porto vs Sporting Braga", "Sporting Braga vs Sporting CP", "AVS vs Sporting Braga", "Sporting Braga vs FC Porto", "Sporting CP vs Sporting Braga", "Sporting Braga vs AVS"], "teams_affected": ["Rio Ave", "Sporting Braga", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Moreirense", "Benfica", "FC Porto", "Sporting CP", "AVS", "Famalicao", "Arouca", "Casa Pia", "Farense", "Nacional", "Santa Clara", "<PERSON><PERSON><PERSON>", "Estrela Amadora", "Estoril"], "total_matches": 495, "valid_matches": 408, "skipped_matches": 87}, "ARGENTINA_PRIMERA_DIVISION": {"warning_count": 104, "missing_matches": ["T<PERSON> de Cordoba vs Independiente", "Independiente vs SM San Juan", "Aldosivi vs Estudiantes", "Defensa y J. vs Estudiantes", "<PERSON><PERSON> vs SM San Juan", "<PERSON><PERSON> vs Defensa y J.", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON>rdo<PERSON>", "I. Rivadavia vs Aldosivi", "I. Rivada<PERSON> vs Defensa y J.", "Instituto vs SM San Juan", "T<PERSON> de Cordoba vs Instituto", "Sarmiento vs SM San Juan", "Aldosivi vs Sarmiento", "Sarmiento vs T. de Cordoba", "Central Cordoba vs Aldosivi", "Aldosivi vs Defensa y J.", "Newells vs Aldosivi", "Aldosivi vs Barracas C.", "Belgrano vs Aldosivi", "Aldosivi vs Estudiantes", "Boca Juniors vs Aldosivi", "Aldosivi vs Sarmiento", "Aldosivi vs Tigre", "Argentinos Jrs vs Aldosivi", "Aldosivi vs Union Santa Fe", "<PERSON><PERSON><PERSON> vs Aldosivi", "Aldosivi vs Racing Club", "I. Rivadavia vs Aldosivi", "Aldosivi vs Banfield", "SM San Juan vs Aldosivi", "Platense vs Defensa y J.", "SM San Juan vs Platense", "Platense vs T. de Cordoba", "Aldosivi vs Racing Club", "Defensa y J. vs Racing Club", "Velez Sarsfield vs SM San Juan", "T<PERSON> de Cordoba vs Velez Sarsfield", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON>rdo<PERSON>", "SM San Juan vs <PERSON><PERSON>", "Newells vs Aldosivi", "Newells vs Defensa y J.", "Aldosivi vs Barracas C.", "Defensa y J. vs Barracas C.", "San Lorenzo vs T. de Cordoba", "T<PERSON> de Cordoba vs Independiente", "<PERSON><PERSON> Cordoba vs Lanus", "SM San Juan vs T. de Cordoba", "<PERSON><PERSON> <PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON>rdo<PERSON>", "<PERSON><PERSON> <PERSON> Cordoba vs Tigre", "T<PERSON> de Cordoba vs Rosario Central", "Sarmiento vs T. de Cordoba", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON>rdo<PERSON>", "Bel<PERSON>no vs T<PERSON> de Cordoba", "T<PERSON> de Cordoba vs Gimnasia", "River Plate vs T. de Cordoba", "T<PERSON> de Cordoba vs Velez Sarsfield", "Platense vs T. de Cordoba", "T<PERSON> de Cordoba vs Instituto", "Defensa y J. vs Banfield", "Aldosivi vs Defensa y J.", "Defensa y J. vs Central Cordoba", "<PERSON><PERSON> vs Defensa y J.", "Newells vs Defensa y J.", "Defensa y J. vs Barracas C.", "Belgrano vs Defensa y J.", "Platense vs Defensa y J.", "Defensa y J. vs Estudiantes", "Boca Juniors vs Defensa y J.", "Defensa y J. vs Tigre", "Argentinos Jrs vs Defensa y J.", "Defensa y J. vs Union Santa Fe", "<PERSON><PERSON><PERSON> vs Defensa y J.", "Defensa y J. vs Racing Club", "I. Rivada<PERSON> vs Defensa y J.", "Argentinos Jrs vs Aldosivi", "Argentinos Jrs vs Defensa y J.", "San Lorenzo vs T. de Cordoba", "SM San Juan vs San Lorenzo", "Belgrano vs Aldosivi", "Belgrano vs Defensa y J.", "SM San Juan vs Belgrano", "Bel<PERSON>no vs T<PERSON> de Cordoba", "<PERSON><PERSON><PERSON> vs Aldosivi", "<PERSON><PERSON><PERSON> vs Defensa y J.", "Gimnasia vs SM San Juan", "T<PERSON> de Cordoba vs Gimnasia", "Central Cordoba vs Aldosivi", "Defensa y J. vs Central Cordoba", "Aldosivi vs Union Santa Fe", "Defensa y J. vs Union Santa Fe", "SM San Juan vs River Plate", "River Plate vs T. de Cordoba", "Boca Juniors vs Aldosivi", "Boca Juniors vs Defensa y J.", "Defensa y J. vs Banfield", "Aldosivi vs Banfield", "<PERSON><PERSON> <PERSON> Cordoba vs Tigre", "Aldosivi vs Tigre", "Defensa y J. vs Tigre", "<PERSON><PERSON> Cordoba vs Lanus", "Lanus vs SM San Juan", "SM San Juan vs Rosario Central", "T<PERSON> de Cordoba vs Rosario Central"], "teams_affected": ["Aldosivi", "Tigre", "Defensa y J.", "Banfield", "SM San Juan", "Instituto", "River Plate", "Boca Juniors", "I. R<PERSON>", "Independiente", "Barracas C.", "<PERSON><PERSON><PERSON>", "Racing Club", "<PERSON><PERSON>rdo<PERSON>", "San Lorenzo", "Rosario Central", "Platense", "<PERSON><PERSON>", "Central Cordoba", "<PERSON><PERSON>", "Union Santa Fe", "<PERSON><PERSON>", "<PERSON><PERSON>", "Velez Sarsfield", "Estudiantes", "Sarmiento", "Gimnasia", "Argentinos Jrs", "<PERSON><PERSON><PERSON>", "Belgrano"], "total_matches": 448, "valid_matches": 344, "skipped_matches": 104}, "LITHUANIA_I_LYGA": {"warning_count": 84, "missing_matches": ["Atmosfera vs TransINVEST", "Atmosfera vs Be1 NFA", "BFA Vilnius vs Atmosfera", "Babrungas vs TransINVEST", "Be1 NFA vs Babrungas", "BFA Vilnius vs Babrungas", "Be1 NFA vs Zalgiris B", "TransINVEST vs Be1 NFA", "Be1 NFA vs Jonava", "Be1 NFA vs Babrungas", "Neptuna K. vs Be1 NFA", "Tauras vs Be1 NFA", "Be1 NFA vs Nevezis", "Atmosfera vs Be1 NFA", "Be1 NFA vs Ekranas", "BFA Vilnius vs Be1 NFA", "Be1 NFA vs Kauno Z. B", "Hegelmann B vs Be1 NFA", "Panevezys B vs Be1 NFA", "Siauliai B vs Be1 NFA", "Be1 NFA vs Minija", "Ekranas vs BFA Vilnius", "Jonava vs BFA Vilnius", "BFA Vilnius vs Kauno Z. B", "Hegelmann B vs BFA Vilnius", "BFA Vilnius vs Panevezys B", "Siauliai B vs BFA Vilnius", "BFA Vilnius vs Minija", "Zalgiris B vs BFA Vilnius", "BFA Vilnius vs TransINVEST", "BFA Vilnius vs Be1 NFA", "BFA Vilnius vs Babrungas", "Neptuna K. vs BFA Vilnius", "BFA Vilnius vs Tauras", "Nevezis vs BFA Vilnius", "BFA Vilnius vs Atmosfera", "Ekranas vs BFA Vilnius", "TransINVEST vs Ekranas", "Be1 NFA vs Ekranas", "Hegelmann B vs BFA Vilnius", "Hegelmann B vs TransINVEST", "Hegelmann B vs Be1 NFA", "TransINVEST vs Jonava", "Jonava vs BFA Vilnius", "Be1 NFA vs Jonava", "BFA Vilnius vs Kauno Z. B", "TransINVEST vs Kauno Z. B", "Be1 NFA vs Kauno Z. B", "BFA Vilnius vs Minija", "TransINVEST vs Minija", "Be1 NFA vs Minija", "TransINVEST vs Neptuna K.", "Neptuna K. vs Be1 NFA", "Neptuna K. vs BFA Vilnius", "TransINVEST vs Nevezis", "Be1 NFA vs Nevezis", "Nevezis vs BFA Vilnius", "BFA Vilnius vs Panevezys B", "TransINVEST vs Panevezys B", "Panevezys B vs Be1 NFA", "Siauliai B vs BFA Vilnius", "Siauliai B vs TransINVEST", "Siauliai B vs Be1 NFA", "Tauras vs TransINVEST", "Tauras vs Be1 NFA", "BFA Vilnius vs Tauras", "TransINVEST vs Jonava", "TransINVEST vs Be1 NFA", "Babrungas vs TransINVEST", "TransINVEST vs Neptuna K.", "Tauras vs TransINVEST", "TransINVEST vs Nevezis", "Atmosfera vs TransINVEST", "TransINVEST vs Ekranas", "BFA Vilnius vs TransINVEST", "TransINVEST vs Kauno Z. B", "Hegelmann B vs TransINVEST", "TransINVEST vs Panevezys B", "Siauliai B vs TransINVEST", "TransINVEST vs Minija", "Zalgiris B vs TransINVEST", "Be1 NFA vs Zalgiris B", "Zalgiris B vs BFA Vilnius", "Zalgiris B vs TransINVEST"], "teams_affected": ["Tauras", "TransINVEST", "BFA Vilnius", "<PERSON><PERSON><PERSON><PERSON>", "Jonava", "<PERSON><PERSON>", "Zalgiris B", "Siauliai B", "<PERSON><PERSON><PERSON><PERSON>", "Atmosfera", "Neptuna K.", "Panevezys B", "<PERSON><PERSON><PERSON>", "Hegelmann B", "<PERSON><PERSON>", "Be1 NFA"], "total_matches": 240, "valid_matches": 156, "skipped_matches": 84}, "ITALY_PRIMAVERA_2": {"warning_count": 56, "missing_matches": ["AlbinoLeffe U19 vs Brescia U19", "FeralpiSalo U19 vs Brescia U19", "Brescia U19 vs AlbinoLeffe U19", "Brescia U19 vs FeralpiSalo U19", "AlbinoLeffe U19 vs Cittadella U19", "FeralpiSalo U19 vs Cittadella U19", "Cittadella U19 vs FeralpiSalo U19", "Cittadella U19 vs AlbinoLeffe U19", "Como U19 vs FeralpiSalo U19", "AlbinoLeffe U19 vs Como U19", "FeralpiSalo U19 vs Como U19", "Como U19 vs AlbinoLeffe U19", "Modena U19 vs FeralpiSalo U19", "AlbinoLeffe U19 vs Modena U19", "Modena U19 vs AlbinoLeffe U19", "FeralpiSalo U19 vs Modena U19", "AlbinoLeffe U19 vs P. Vercelli U19", "P. Vercelli U19 vs FeralpiSalo U19", "P. Vercelli U19 vs AlbinoLeffe U19", "FeralpiSalo U19 vs P. Vercelli U19", "FeralpiSalo U19 vs Padova U19", "Padova U19 vs AlbinoLeffe U19", "Padova U19 vs FeralpiSalo U19", "AlbinoLeffe U19 vs Padova U19", "FeralpiSalo U19 vs Parma U19", "Parma U19 vs AlbinoLeffe U19", "AlbinoLeffe U19 vs Parma U19", "Parma U19 vs FeralpiSalo U19", "Reggiana U19 vs AlbinoLeffe U19", "FeralpiSalo U19 vs Reggiana U19", "Reggiana U19 vs FeralpiSalo U19", "AlbinoLeffe U19 vs Reggiana U19", "Renate U19 vs FeralpiSalo U19", "Renate U19 vs AlbinoLeffe U19", "FeralpiSalo U19 vs Renate U19", "AlbinoLeffe U19 vs Renate U19", "FeralpiSalo U19 vs Spal U19", "Spal U19 vs AlbinoLeffe U19", "Spal U19 vs FeralpiSalo U19", "AlbinoLeffe U19 vs Spal U19", "Sudtirol U19 vs AlbinoLeffe U19", "Sudtirol U19 vs FeralpiSalo U19", "AlbinoLeffe U19 vs Sudtirol U19", "FeralpiSalo U19 vs Sudtirol U19", "V. Entella U19 vs FeralpiSalo U19", "V. Entella U19 vs AlbinoLeffe U19", "FeralpiSalo U19 vs V. Entella U19", "AlbinoLeffe U19 vs V. Entella U19", "AlbinoLeffe U19 vs Venezia U19", "FeralpiSalo U19 vs Venezia U19", "Venezia U19 vs FeralpiSalo U19", "Venezia U19 vs AlbinoLeffe U19", "Vicenza V. U19 vs AlbinoLeffe U19", "Vicenza V. U19 vs FeralpiSalo U19", "AlbinoLeffe U19 vs Vicenza V. U19", "FeralpiSalo U19 vs Vicenza V. U19"], "teams_affected": ["AlbinoLeffe U19", "Spal U19", "Modena U19", "Renate U19", "Reggiana U19", "Vicenza V. U19", "<PERSON><PERSON> U19", "Como U19", "<PERSON><PERSON> U19", "Parma U19", "FeralpiSalo U19", "Cittadella U19", "Padova U19", "Brescia U19", "Sudtirol U19", "Venezia U19"], "total_matches": 900, "valid_matches": 844, "skipped_matches": 56}, "GIBRALTAR_PREMIER_DIVISION": {"warning_count": 57, "missing_matches": ["Lions FC vs College 1975", "Europa FC vs College 1975", "Lincoln RI vs College 1975", "College 1975 vs Europa FC", "College 1975 vs Lions FC", "College 1975 vs Lincoln RI", "Europa Point vs Europa FC", "Europa Point vs Lions FC", "Europa Point vs Lincoln RI", "Lincoln RI vs Europa Point", "Europa FC vs Europa Point", "Lions FC vs Europa Point", "Glacis <PERSON> vs Europa FC", "<PERSON><PERSON>cis <PERSON> vs Lions FC", "<PERSON><PERSON><PERSON> vs Lincoln RI", "Europa FC vs Glacis Utd", "Lions FC vs Glacis Utd", "Lincoln RI vs Glacis <PERSON>d", "Lynx vs Europa FC", "Lincoln RI vs Lynx", "Lynx vs Lions FC", "Lynx vs Lincoln RI", "Europa FC vs Lynx", "Lions FC vs Lynx", "Magpies vs Lions FC", "Magpies vs Lincoln RI", "Magpies vs Europa FC", "Lincoln RI vs Magpies", "Europa FC vs Magpies", "Lions FC vs Magpies", "Lions FC vs Magpies", "Lincoln RI vs Magpies", "Europa FC vs Magpies", "Europa FC vs Manchester 62", "Lions FC vs Manchester 62", "Lincoln RI vs Manchester 62", "Manchester 62 vs Lincoln RI", "Manchester 62 vs Europa FC", "Manchester 62 vs Lions FC", "Manchester 62 vs Europa FC", "Manchester 62 vs Lions FC", "Manchester 62 vs Lincoln RI", "Europa FC vs Mons Calpe", "Lions FC vs Mons Calpe", "Lincoln RI vs Mons Calpe", "Mons Calpe vs Lincoln RI", "Mons Calpe vs Europa FC", "Mons Calpe vs Lions FC", "St Josephs vs Lions FC", "Lincoln RI vs St Josephs", "Europa FC vs St Josephs", "Lions FC vs St Josephs", "St Josephs vs Lincoln RI", "St Josephs vs Europa FC", "St Josephs vs Lincoln RI", "St Josephs vs Europa FC", "St Josephs vs Lions FC"], "teams_affected": ["Europa FC", "Europa Point", "Magpies", "Lynx", "St Josephs", "Lincoln RI", "Mons Calpe", "College 1975", "Manchester 62", "<PERSON><PERSON><PERSON>", "Lions FC"], "total_matches": 175, "valid_matches": 118, "skipped_matches": 57}, "CHINA_SUPER_LEAGUE": {"warning_count": 1, "missing_matches": ["Yunnan Yukun vs Shanghai Port"], "teams_affected": ["Yunnan Yukun", "Shanghai Port"], "total_matches": 12, "valid_matches": 11, "skipped_matches": 1}, "ITALY_SERIE_A_WOMEN": {"warning_count": 44, "missing_matches": ["Como W vs AC Milan W", "Como W vs AS Roma W", "AC Milan W vs Como W", "AS Roma W vs Como W", "AC Milan W vs Fiorentina W", "AS Roma W vs Fiorentina W", "Fiorentina W vs AC Milan W", "Fiorentina W vs AS Roma W", "Fiorentina W vs AC Milan W", "AS Roma W vs Fiorentina W", "AC Milan W vs Fiorentina W", "Fiorentina W vs AS Roma W", "Inter Milan W vs AC Milan W", "Inter Milan W vs AS Roma W", "AC Milan W vs Inter Milan W", "AS Roma W vs Inter Milan W", "AS Roma W vs Inter Milan W", "Inter Milan W vs AC Milan W", "Inter Milan W vs AS Roma W", "AC Milan W vs Inter Milan W", "Juventus W vs AS Roma W", "Juventus W vs AC Milan W", "AS Roma W vs Juventus W", "AC Milan W vs Juventus W", "Juventus W vs AS Roma W", "AC Milan W vs Juventus W", "AS Roma W vs Juventus W", "Juventus W vs AC Milan W", "Lazio W vs AS Roma W", "AC Milan W vs Lazio W", "AS Roma W vs Lazio W", "Lazio W vs AC Milan W", "AS Roma W vs Napoli W", "Napoli W vs AC Milan W", "Napoli W vs AS Roma W", "AC Milan W vs Napoli W", "AC Milan W vs Sampdoria W", "Sampdoria W vs AS Roma W", "Sampdoria W vs AC Milan W", "AS Roma W vs Sampdoria W", "AS Roma W vs Sassuolo W", "AC Milan W vs Sassuolo W", "Sassuolo W vs AS Roma W", "Sassuolo W vs AC Milan W"], "teams_affected": ["AC Milan W", "Juventus W", "Como W", "AS Roma W", "Sassuolo W", "Fiorentina W", "Napoli W", "Lazio W", "Inter Milan W", "Sampdoria W"], "total_matches": 208, "valid_matches": 164, "skipped_matches": 44}, "ITALY_SERIE_D_GROUP_F": {"warning_count": 34, "missing_matches": ["Ancona 1905 vs LAquila", "LAquila vs Ancona 1905", "Atletico Ascoli vs LAquila", "LAquila vs Atletico Ascoli", "Avezzano vs LAquila", "LAquila vs Avezzano", "LAquila vs Castelfidardo", "<PERSON><PERSON><PERSON><PERSON> vs LAquila", "Chieti vs LAquila", "LAquila vs Chieti", "LAquila vs Civitanovese", "Civitanovese vs LAquila", "LAquila vs Fermana", "Fermana vs LAquila", "LAquila vs Fossombrone", "Fossombrone vs LAquila", "LAquila vs Isernia", "Isernia vs LAquila", "LAquila vs Recanatese", "Recanatese vs LAquila", "Roma City vs LAquila", "LAquila vs Roma City", "Sambenedettese vs LAquila", "LAquila vs Sambenedettese", "LAquila vs San Nicolo", "San Nicolo vs LAquila", "LAquila vs Sora", "Sora vs LAquila", "Teramo vs LAquila", "LAquila vs Teramo", "Termoli Calcio vs LAquila", "LAquila vs Termoli Calcio", "<PERSON><PERSON> vs LAquila", "LAquila vs V. Senigallia"], "teams_affected": ["Avezzano", "Teramo", "Civitanovese", "<PERSON><PERSON><PERSON><PERSON>", "LAquila", "Ancona 1905", "Atletico Ascoli", "Roma City", "Termoli Calcio", "Isernia", "San Nicolo", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Chieti", "Sambenedettese", "<PERSON>ra", "<PERSON><PERSON><PERSON><PERSON>"], "total_matches": 578, "valid_matches": 544, "skipped_matches": 34}, "SAUDI_ARABIA_DIVISION_1": {"warning_count": 32, "missing_matches": ["<PERSON><PERSON> vs Neom SC", "Neom SC vs Abha", "Neom SC vs Al Adalha", "Neom SC vs Al Ain", "Al Ain vs Neom SC", "Neom SC vs Al Arabi", "Al Arabi vs Neom SC", "Neom SC vs Al Batin", "<PERSON> vs Neom SC", "Al Bukayriyah vs Neom SC", "Neom SC vs Al Bukayriyah", "Al Faisaly vs Neom SC", "Neom SC vs Al Faisaly", "Neom SC vs Al Hazm", "Al Hazm vs Neom SC", "<PERSON> vs Neom SC", "Neom SC vs Al Jabalain", "Neom SC vs Al <PERSON>", "<PERSON> vs Neom SC", "Al Jubail vs Neom SC", "Neom SC vs Al Jubail", "Al <PERSON>j<PERSON> vs Neom SC", "Neom SC vs Al Najma", "Al Safa vs Neom SC", "Neom SC vs Al Safa", "Al Tai vs Neom SC", "Neom SC vs Al Tai", "Al Zulfi vs Neom SC", "Neom SC vs Jeddah", "Jeddah vs Neom SC", "Neom SC vs Ohod", "<PERSON>od vs Neom SC"], "teams_affected": ["<PERSON>", "Al Safa", "Al Tai", "Al Adalha", "Al Bukayriyah", "Al Arabi", "Al Najma", "<PERSON><PERSON>", "Al Ain", "<PERSON>", "Al Faisaly", "Al J<PERSON>lain", "Al Jubail", "Al Zulfi", "Al Hazm", "Jeddah", "<PERSON><PERSON>", "Neom SC"], "total_matches": 544, "valid_matches": 512, "skipped_matches": 32}, "EGYPT_PREMIER_LEAGUE": {"warning_count": 1, "missing_matches": ["Ghazl El M. vs Ceramica C."], "teams_affected": ["Ghazl El M.", "Ceramica C."], "total_matches": 13, "valid_matches": 12, "skipped_matches": 1}, "PORTUGAL_FIRST_DIVISION_WOMEN": {"warning_count": 20, "missing_matches": ["Albergaria W vs SL Benfica W", "SL Benfica W vs Braga W", "SL Benfica W vs Damaiense W", "Damaiense W vs SL Benfica W", "Estoril Praia W vs SL Benfica W", "SL Benfica W vs Estoril Praia W", "SL Benfica W vs Famalicao W", "Famalicao W vs SL Benfica W", "SL Benfica W vs Maritimo W", "Maritimo W vs SL Benfica W", "Racing Power W vs SL Benfica W", "SL Benfica W vs Racing Power W", "Sporting W vs SL Benfica W", "SL Benfica W vs Sporting W", "SL Benfica W vs Torreense W", "Torreense W vs SL Benfica W", "SL Benfica W vs Valadares G. W", "Valadares G. W vs SL Benfica W", "Vilaverdense W vs SL Benfica W", "SL Benfica W vs Vilaverdense W"], "teams_affected": ["Maritimo W", "SL Benfica W", "Sporting W", "Torreense W", "Albergaria W", "Famalicao W", "Racing Power W", "Vilaverdense W", "Valadares G. W", "Damaiense W", "Estoril Praia W", "Braga W"], "total_matches": 220, "valid_matches": 200, "skipped_matches": 20}, "SLOVAKIA_2._LIGA": {"warning_count": 46, "missing_matches": ["Humenne vs Zlate Moravce", "Zlate Moravce vs Humenne", "<PERSON><PERSON> vs Zlate Moravce", "<PERSON><PERSON> vs <PERSON><PERSON>", "Zlate Moravce vs Malzenice", "Zlate Moravce vs P. Bystrica", "P. Bystrica vs Zlate Moravce", "Petrzalka vs Zlate Moravce", "Zlate Moravce vs Petrzalka", "Pohronie vs Zlate Moravce", "Zlate Moravce vs Pohronie", "Zlate Moravce vs Presov", "Presov vs Zlate Moravce", "Puchov vs Zlate Moravce", "S. Bratislava B vs Zlate Moravce", "Zlate Moravce vs S. Bratislava B", "S. Lubovna vs Zlate Moravce", "Zlate Moravce vs S. Lubovna", "Zlate Moravce vs Samorin", "Samorin vs Zlate Moravce", "Zlate Moravce vs Zilina B", "Zilina B vs Zlate Moravce", "S. Lubovna vs Zlate Moravce", "Petrzalka vs Zlate Moravce", "Zlate Moravce vs Presov", "Humenne vs Zlate Moravce", "Zlate Moravce vs P. Bystrica", "<PERSON><PERSON> vs Zlate Moravce", "Zlate Moravce vs Zilina B", "Pohronie vs Zlate Moravce", "Zlate Moravce vs Samorin", "S. Bratislava B vs Zlate Moravce", "Zlate Moravce vs Malzenice", "Puchov vs Zlate Moravce", "Zlate Moravce vs Zvolen", "Zlate Moravce vs S. Lubovna", "Zlate Moravce vs Petrzalka", "Presov vs Zlate Moravce", "Zlate Moravce vs Humenne", "P. Bystrica vs Zlate Moravce", "<PERSON><PERSON> vs <PERSON><PERSON>", "Zilina B vs Zlate Moravce", "Zlate Moravce vs Pohronie", "Samorin vs Zlate Moravce", "Zlate Moravce vs S. Bratislava B", "Zlate Moravce vs Zvolen"], "teams_affected": ["Pohronie", "Zlate Moravce", "Petrzalka", "S. Bratislava B", "<PERSON><PERSON>", "Zvolen", "<PERSON><PERSON>", "Presov", "<PERSON><PERSON><PERSON>", "S. Lubovna", "P. <PERSON>", "Zilina B", "Malzenice", "Samorin"], "total_matches": 320, "valid_matches": 274, "skipped_matches": 46}, "HUNGARY_NB_II": {"warning_count": 28, "missing_matches": ["<PERSON><PERSON><PERSON> vs BVSC", "Bekescsaba vs BVSC", "BVSC vs Bekescsaba", "BVSC vs Budafoki", "Budafoki vs BVSC", "Csakvari vs BVSC", "BVSC vs Csakvari", "Gyirmot vs BVSC", "BVSC vs Gyirmot", "Honved vs BVSC", "BVSC vs Honved", "Kazincbarcika vs BVSC", "BVSC vs Kazincbarcika", "BVSC vs Kisvarda", "<PERSON><PERSON><PERSON><PERSON> vs BVSC", "Kozarmisleny vs BVSC", "BVSC vs Kozarmisleny", "Mezokovesd vs BVSC", "BVSC vs Mezokovesd", "BVSC vs Soroksar", "BVSC vs Szeged", "Szeged vs BVSC", "BVSC vs Szentlorinc", "Szentlorinc vs BVSC", "Tatabanya vs BVSC", "BVSC vs Tatabanya", "Vasas vs BVSC", "BVSC vs Vasas"], "teams_affected": ["Bekescsaba", "Budafoki", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "BVSC", "<PERSON><PERSON><PERSON>", "Kozarmisleny", "Tatabanya", "<PERSON><PERSON><PERSON><PERSON>", "V<PERSON><PERSON>", "Szeged", "<PERSON><PERSON><PERSON>", "Szentlorinc", "Mezokovesd", "Kazincbarcika", "Csakvari"], "total_matches": 412, "valid_matches": 384, "skipped_matches": 28}, "TURKEY_3_LIG_GROUP_2": {"warning_count": 30, "missing_matches": ["<PERSON><PERSON><PERSON> vs Tire 2021 FK", "Tire 2021 FK vs Adiyaman", "Amasyaspor vs Tire 2021 FK", "Tire 2021 FK vs Amasyaspor", "Tire 2021 FK vs Balikesirspor", "Balikesirspor vs Tire 2021 FK", "Beykoz Ishaklis vs Tire 2021 FK", "Tire 2021 FK vs Beykoz Ishaklis", "Tire 2021 FK vs Cayelispor", "Cayelispor vs Tire 2021 FK", "Etimesgut vs Tire 2021 FK", "Tire 2021 FK vs Etimesgut", "Tire 2021 FK vs Fatsa B.", "Fatsa B. vs Tire 2021 FK", "Tire 2021 FK vs Inegol Kafkas", "<PERSON><PERSON><PERSON> vs Tire 2021 FK", "Tire 2021 FK vs Kelkit", "Kelkit vs Tire 2021 FK", "Mazidagi vs Tire 2021 FK", "Tire 2021 FK vs Mazidagi", "Tire 2021 FK vs Muglaspor", "Muglaspor vs Tire 2021 FK", "Tire 2021 FK vs Nevsehirspor", "Nevsehirspor vs Tire 2021 FK", "Silivrispor vs Tire 2021 FK", "Tire 2021 FK vs Silivrispor", "Turk Metal 1963 vs Tire 2021 FK", "Tire 2021 FK vs Turk Metal 1963", "Usakspor vs Tire 2021 FK", "Tire 2021 FK vs Usakspor"], "teams_affected": ["<PERSON><PERSON><PERSON>", "Silivrispor", "Turk Metal 1963", "Cayelispor", "<PERSON><PERSON><PERSON>", "Amasyaspor", "Fatsa B.", "Usakspor", "<PERSON><PERSON><PERSON>", "Nevsehirspor", "Etimesgut", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Balikesirspor", "Tire 2021 FK"], "total_matches": 450, "valid_matches": 420, "skipped_matches": 30}, "BRAZIL_SERIE_D": {"warning_count": 5, "missing_matches": ["Uberlandia vs Cascavel", "Cascavel vs Goiatuba EC", "Manauara vs Independencia", "GAS vs Manauara", "Santa Cruz de N vs Sousa"], "teams_affected": ["Goiatuba EC", "Sousa", "GAS", "Santa Cruz de N", "Independencia", "Cascavel", "<PERSON><PERSON><PERSON>", "Uberlandia"], "total_matches": 6, "valid_matches": 1, "skipped_matches": 5}, "LUXEMBOURG_NATIONAL_DIVISION": {"warning_count": 27, "missing_matches": ["Bettembourg vs UNA Strassen", "Differdange vs UNA Strassen", "UNA Strassen vs Dudelange", "Dudelange vs UNA Strassen", "<PERSON><PERSON> vs UNA Strassen", "UNA Strassen vs Fola Esch", "UNA Strassen vs Hesperange", "Hesperange vs UNA Strassen", "UNA Strassen vs Hostert", "Hostert vs UNA Strassen", "UNA Strassen vs Jeunesse Esch", "Jeunesse Esch vs UNA Strassen", "Mondercange vs UNA Strassen", "UNA Strassen vs Mondercange", "Mondorf vs UNA Strassen", "UNA Strassen vs Mondorf", "UNA Strassen vs Niedercorn", "Niedercorn vs UNA Strassen", "Petange vs UNA Strassen", "UNA Strassen vs Petange", "UNA Strassen vs Racing", "Racing vs UNA Strassen", "UNA Strassen vs Rodange", "Rodange vs UNA Strassen", "UNA Strassen vs V. Rosport", "Wiltz vs UNA Strassen", "UNA Strassen vs Wiltz"], "teams_affected": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jeunesse Esch", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "UNA Strassen", "Mondorf", "Racing", "<PERSON><PERSON>", "Mondercange", "V. R<PERSON>port", "<PERSON><PERSON><PERSON><PERSON>", "Differdange", "Bettembourg", "Dudelange"], "total_matches": 409, "valid_matches": 382, "skipped_matches": 27}, "ENGLAND_LEAGUE_2": {"warning_count": 92, "missing_matches": ["AFC Wimbledon vs Accrington", "Accrington vs AFC Wimbledon", "AFC Wimbledon vs Colchester Utd", "Bromley vs AFC Wimbledon", "Cheltenham vs AFC Wimbledon", "AFC Wimbledon vs Fleetwood", "AFC Wimbledon vs Milton Keynes", "Bradford vs AFC Wimbledon", "Salford City vs AFC Wimbledon", "AFC Wimbledon vs Carlisle Utd", "Notts County vs AFC Wimbledon", "AFC Wimbledon vs Morecambe", "Port Vale vs AFC Wimbledon", "AFC Wimbledon vs Grimsby", "Barrow vs AFC Wimbledon", "AFC Wimbledon vs Accrington", "AFC Wimbledon vs Walsall", "Tranmere vs AFC Wimbledon", "AFC Wimbledon vs Newport", "Harrogate vs AFC Wimbledon", "AFC Wimbledon vs Doncaster", "Chesterfield vs AFC Wimbledon", "AFC Wimbledon vs Swindon Town", "AFC Wimbledon vs Gillingham", "Newport vs AFC Wimbledon", "AFC Wimbledon vs Tranmere", "Milton Keynes vs AFC Wimbledon", "Crewe Alexandra vs AFC Wimbledon", "AFC Wimbledon vs Bradford", "Accrington vs AFC Wimbledon", "AFC Wimbledon vs Crewe Alexandra", "AFC Wimbledon vs Salford City", "Fleetwood vs AFC Wimbledon", "Colchester Utd vs AFC Wimbledon", "AFC Wimbledon vs Bromley", "Morecambe vs AFC Wimbledon", "AFC Wimbledon vs Notts County", "AFC Wimbledon vs Cheltenham", "Carlisle Utd vs AFC Wimbledon", "AFC Wimbledon vs Barrow", "Walsall vs AFC Wimbledon", "Swindon Town vs AFC Wimbledon", "AFC Wimbledon vs Harrogate", "Doncaster vs AFC Wimbledon", "AFC Wimbledon vs Chesterfield", "Gillingham vs AFC Wimbledon", "AFC Wimbledon vs Port Vale", "Grimsby vs AFC Wimbledon", "Barrow vs AFC Wimbledon", "AFC Wimbledon vs Barrow", "Bradford vs AFC Wimbledon", "AFC Wimbledon vs Bradford", "Bromley vs AFC Wimbledon", "AFC Wimbledon vs Bromley", "AFC Wimbledon vs Carlisle Utd", "Carlisle Utd vs AFC Wimbledon", "Cheltenham vs AFC Wimbledon", "AFC Wimbledon vs Cheltenham", "Chesterfield vs AFC Wimbledon", "AFC Wimbledon vs Chesterfield", "AFC Wimbledon vs Colchester Utd", "Colchester Utd vs AFC Wimbledon", "Crewe Alexandra vs AFC Wimbledon", "AFC Wimbledon vs Crewe Alexandra", "AFC Wimbledon vs Doncaster", "Doncaster vs AFC Wimbledon", "AFC Wimbledon vs Fleetwood", "Fleetwood vs AFC Wimbledon", "AFC Wimbledon vs Gillingham", "Gillingham vs AFC Wimbledon", "AFC Wimbledon vs Grimsby", "Grimsby vs AFC Wimbledon", "Harrogate vs AFC Wimbledon", "AFC Wimbledon vs Harrogate", "AFC Wimbledon vs Milton Keynes", "Milton Keynes vs AFC Wimbledon", "AFC Wimbledon vs Morecambe", "Morecambe vs AFC Wimbledon", "AFC Wimbledon vs Newport", "Newport vs AFC Wimbledon", "Notts County vs AFC Wimbledon", "AFC Wimbledon vs Notts County", "Port Vale vs AFC Wimbledon", "AFC Wimbledon vs Port Vale", "Salford City vs AFC Wimbledon", "AFC Wimbledon vs Salford City", "AFC Wimbledon vs Swindon Town", "Swindon Town vs AFC Wimbledon", "Tranmere vs AFC Wimbledon", "AFC Wimbledon vs Tranmere", "AFC Wimbledon vs Walsall", "Walsall vs AFC Wimbledon"], "teams_affected": ["Salford City", "Doncaster", "Morecambe", "Chesterfield", "Newport", "<PERSON><PERSON><PERSON>", "Crewe Alexandra", "Cheltenham", "Milton Keynes", "Fleetwood", "AFC Wimbledon", "Accrington", "Harrogate", "Bradford", "Swindon Town", "Grimsby", "Port Vale", "Walsall", "Notts County", "Carlisle Utd", "Barrow", "Colchester Utd", "Gillingham", "Tranmere"], "total_matches": 1104, "valid_matches": 1012, "skipped_matches": 92}, "SOUTH_KOREA_K_LEAGUE_2": {"warning_count": 7, "missing_matches": ["<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "Gimpo vs Bucheon FC", "Incheon Utd vs Gyeongnam", "Busan IPark vs Gyeongnam", "<PERSON><PERSON><PERSON>g vs Gyeongnam", "Seoul E-Land vs Gyeongnam", "Gyeongnam vs Bucheon FC"], "teams_affected": ["Gyeongnam", "Bucheon FC", "Seoul E-Land", "Busan IPark", "<PERSON><PERSON><PERSON>", "Incheon Utd", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "total_matches": 16, "valid_matches": 9, "skipped_matches": 7}, "COSTA_RICA_PRIMERA_DIVISION": {"warning_count": 22, "missing_matches": ["Sporting SJ vs Alajuelense", "Alajuelense vs Sporting SJ", "Cartagines vs Sporting SJ", "Sporting SJ vs Cartagines", "Sporting SJ vs Guanacasteca", "Guanacasteca vs Sporting SJ", "Herediano vs Sporting SJ", "Sporting SJ vs Herediano", "Sporting SJ vs M. Liberia", "M. Liberia vs Sporting SJ", "Puntarenas vs Sporting SJ", "Sporting SJ vs Puntarenas", "Sporting SJ vs San Carlos", "San Carlos vs Sporting SJ", "Santa Ana vs Sporting SJ", "Sporting SJ vs Santa Ana", "Sporting SJ vs Santos Guapiles", "Santos Guapiles vs Sporting SJ", "Sporting SJ vs Saprissa", "Saprissa vs Sporting SJ", "Zeledon vs Sporting SJ", "Sporting SJ vs Zeledon"], "teams_affected": ["Puntarenas", "<PERSON><PERSON><PERSON>", "Guanacasteca", "Sporting SJ", "Alajuelense", "M. <PERSON>", "Santos Guapiles", "Saprissa", "San Carlos", "Santa Ana", "Cartagines", "Herediano"], "total_matches": 242, "valid_matches": 220, "skipped_matches": 22}, "BELGIUM_FIRST_DIVISION_A": {"warning_count": 110, "missing_matches": ["Anderlecht vs Sint-Truiden", "Anderlecht vs OH Leuven", "KV Mechelen vs Anderlecht", "Royale Union SG vs Anderlecht", "Anderlecht vs KRC Genk", "OH Leuven vs Anderlecht", "Sint-Truiden vs Anderlecht", "KRC Genk vs Anderlecht", "Anderlecht vs KV Mechelen", "Anderlecht vs Royale Union SG", "Antwerp vs Sint-Truiden", "Antwerp vs KV Mechelen", "Antwerp vs Royale Union SG", "OH Leuven vs Antwerp", "KRC Genk vs Antwerp", "Royale Union SG vs Antwerp", "KV Mechelen vs Antwerp", "Antwerp vs KRC Genk", "Sint-Truiden vs Antwerp", "Antwerp vs OH Leuven", "Beerschot vs OH Leuven", "Royale Union SG vs Beerschot", "Beerschot vs KRC Genk", "Beerschot vs Sint-Truiden", "KV Mechelen vs Beerschot", "OH Leuven vs Beerschot", "Beerschot vs Royale Union SG", "KRC Genk vs Beerschot", "Beerschot vs KV Mechelen", "Sint-Truiden vs Beerschot", "OH Leuven vs Cercle Brugge", "Cercle Brugge vs KRC Genk", "KV Mechelen vs Cercle Brugge", "Cercle Brugge vs Sint-Truiden", "Royale Union SG vs Cercle Brugge", "Cercle Brugge vs Royale Union SG", "Cercle Brugge vs OH Leuven", "Sint-Truiden vs Cercle Brugge", "Cercle Brugge vs KV Mechelen", "KRC Genk vs Cercle Brugge", "Sint-<PERSON><PERSON><PERSON> vs Charleroi", "Royale Union SG vs Charleroi", "KV Mechelen vs Charleroi", "Charleroi vs OH Leuven", "KRC Genk vs Charleroi", "OH Leuven vs Charleroi", "Charleroi vs Sint-Truiden", "Charleroi vs Royale Union SG", "Charleroi vs KRC Genk", "Charleroi vs KV Mechelen", "Club Brugge vs KV Mechelen", "KRC Genk vs Club Brugge", "Club Brugge vs Royale Union SG", "OH Leuven vs Club Brugge", "Club Brugge vs Sint-Truiden", "KV Mechelen vs Club Brugge", "Club Brugge vs KRC Genk", "Royale Union SG vs Club Brugge", "Club Brugge vs OH Leuven", "Sint-Truiden vs Club Brugge", "Dender vs Royale Union SG", "Sint-Truiden vs Dender", "KRC Genk vs Dender", "Dender vs KV Mechelen", "Dender vs OH Leuven", "Dender vs Sint-Truiden", "OH Leuven vs Dender", "Royale Union SG vs Dender", "<PERSON><PERSON> vs KRC Genk", "KV Mechelen vs Dender", "Gent vs KV Mechelen", "Gent vs OH Leuven", "Sint-Tru<PERSON> vs Gent", "Royale Union SG vs Gent", "Gent vs KRC Genk", "Gent vs Sint-Truiden", "Gent vs Royale Union SG", "OH Leuven vs Gent", "KV Mechelen vs Gent", "KRC Genk vs Gent", "Kortrijk vs Sint-Truiden", "OH Leuven vs Kortrijk", "Royale Union SG vs Kortrijk", "Kortrijk vs KRC Genk", "KV Mechelen vs Kortrijk", "Kortrijk vs KV Mechelen", "KRC Genk vs Kortrijk", "Kortrijk vs Royale Union SG", "Sint-Truiden vs Kortrijk", "Kortrijk vs OH Leuven", "KRC Genk vs Standard Liege", "Standard Liege vs KV Mechelen", "OH Leuven vs Standard Liege", "Standard Liege vs Royale Union SG", "Standard Liege vs Sint-Truiden", "Standard Liege vs OH Leuven", "KV Mechelen vs Standard Liege", "Sint-Truiden vs Standard Liege", "Standard Liege vs KRC Genk", "Royale Union SG vs Standard Liege", "KV Mechelen vs Westerlo", "Westerlo vs Royale Union SG", "Westerlo vs OH Leuven", "KRC Genk vs Westerlo", "Sint-Truiden vs Westerlo", "Royale Union SG vs Westerlo", "Westerlo vs KV Mechelen", "Westerlo vs Sint-Truiden", "Westerlo vs KRC Genk", "OH Leuven vs Westerlo"], "teams_affected": ["KV Mechelen", "Club Brugge", "Royale Union SG", "OH Leuven", "Cercle Brugge", "Anderlecht", "KRC Genk", "Gent", "Kortrijk", "<PERSON><PERSON>", "Standard Liege", "Beerschot", "Charleroi", "Sint-Truiden", "Westerlo", "Antwerp"], "total_matches": 330, "valid_matches": 220, "skipped_matches": 110}, "GERMANY_2_BUNDESLIGA_WOMEN": {"warning_count": 42, "missing_matches": ["Hamburger SV W vs Andernach W", "Andernach W vs FC Bayern B W", "Andernach W vs Hamburger SV W", "FC Bayern B W vs Andernach W", "Hamburger SV W vs Bochum W", "FC Bayern B W vs Bochum W", "Bochum W vs FC Bayern B W", "Bochum W vs Hamburger SV W", "Frankfurt B W vs Hamburger SV W", "FC Bayern B W vs Frankfurt B W", "Frankfurt B W vs FC Bayern B W", "Hamburger SV W vs Frankfurt B W", "Freiburg B W vs FC Bayern B W", "Freiburg B W vs Hamburger SV W", "FC Bayern B W vs Freiburg B W", "Hamburger SV W vs Gutersloh W", "FC Bayern B W vs Gutersloh W", "Gutersloh W vs Hamburger SV W", "Gutersloh W vs FC Bayern B W", "Ingolstadt W vs Hamburger SV W", "Ingolstadt W vs FC Bayern B W", "Hamburger SV W vs Ingolstadt W", "Meppen W vs FC Bayern B W", "Hamburger SV W vs Meppen W", "FC Bayern B W vs Meppen W", "Hamburger SV W vs Mgladbach W", "FC Bayern B W vs Mgladbach W", "Mgladbach W vs Hamburger SV W", "Hamburger SV W vs Nurnberg W", "FC Bayern B W vs Nurnberg W", "Hamburger SV W vs Sand W", "FC Bayern B W vs Sand W", "Sand W vs Hamburger SV W", "Sand W vs FC Bayern B W", "Union Berlin W vs Hamburger SV W", "Union Berlin W vs FC Bayern B W", "Hamburger SV W vs Union Berlin W", "FC Bayern B W vs Union Berlin W", "Weinberg W vs Hamburger SV W", "Weinberg W vs FC Bayern B W", "Hamburger SV W vs Weinberg W", "FC Bayern B W vs Weinberg W"], "teams_affected": ["Meppen W", "Hamburger SV W", "<PERSON><PERSON> W", "Gutersloh W", "Ingolstadt W", "Freiburg B W", "Bochum W", "Nurnberg W", "Mgladbach W", "Frankfurt B W", "FC Bayern B W", "Sand W", "Union Berlin W", "Andernach W"], "total_matches": 276, "valid_matches": 234, "skipped_matches": 42}, "GUATEMALA_LIGA_NACIONAL": {"warning_count": 16, "missing_matches": ["Achuapa vs Antigua GFC", "Coban Imperial vs Antigua GFC", "Antigua GFC vs Coban Imperial", "Comunicaciones vs Antigua GFC", "Antigua GFC vs Comunicaciones", "Antigua GFC vs Guastatoya", "Guastatoya vs Antigua GFC", "Antigua GFC vs Malacateco", "Antigua GFC vs Marquense", "Antigua GFC vs Mixco", "Municipal vs Antigua GFC", "Xelaju vs Antigua GFC", "Xinabajul vs Antigua GFC", "Antigua GFC vs Xinabajul", "Antigua GFC vs Zacapa Tellioz", "Zacapa Tellioz vs Antigua GFC"], "teams_affected": ["Municipal", "Xelaju", "Antigua GFC", "Comunicaciones", "<PERSON><PERSON>apa", "Malacateco", "Coban Imperial", "Xinabajul", "Zacapa Tellioz", "Marquense", "Guastatoya", "Mixco"], "total_matches": 176, "valid_matches": 160, "skipped_matches": 16}, "AUSTRALIA_A_LEAGUE": {"warning_count": 61, "missing_matches": ["WS Wanderers vs Adelaide Utd", "Adelaide Utd vs Sydney FC", "Adelaide Utd vs WS Wanderers", "Macarthur FC vs Adelaide Utd", "Sydney FC vs Adelaide Utd", "Adelaide Utd vs Macarthur FC", "Adelaide Utd vs Sydney FC", "Auckland vs Sydney FC", "Macarthur FC vs Auckland", "WS Wanderers vs Auckland", "Auckland vs Macarthur FC", "Auckland vs WS Wanderers", "Sydney FC vs Auckland", "Brisbane Roar vs Sydney FC", "Macarthur FC vs Brisbane Roar", "WS Wanderers vs Brisbane Roar", "Sydney FC vs Brisbane Roar", "Brisbane Roar vs WS Wanderers", "Brisbane Roar vs Macarthur FC", "Central Coast vs Sydney FC", "Macarthur FC vs Central Coast", "Sydney FC vs Central Coast", "WS Wanderers vs Central Coast", "Central Coast vs WS Wanderers", "Central Coast vs Macarthur FC", "Melbourne City vs WS Wanderers", "Macarthur FC vs Melbourne City", "Melbourne City vs Macarthur FC", "Sydney FC vs Melbourne City", "WS Wanderers vs Melbourne City", "Melbourne V. vs Macarthur FC", "Sydney FC vs Melbourne V.", "Melbourne V. vs WS Wanderers", "Melbourne V. vs Sydney FC", "WS Wanderers vs Melbourne V.", "Macarthur FC vs Melbourne V.", "Macarthur FC vs Newcastle Jets", "WS Wanderers vs Newcastle Jets", "Newcastle Jets vs Sydney FC", "Newcastle Jets vs Macarthur FC", "Macarthur FC vs Newcastle Jets", "Sydney FC vs Newcastle Jets", "Newcastle Jets vs WS Wanderers", "Macarthur FC vs Perth Glory", "Perth Glory vs Macarthur FC", "Sydney FC vs Perth Glory", "Perth Glory vs WS Wanderers", "Perth Glory vs Sydney FC", "WS Wanderers vs Perth Glory", "Wellington vs Macarthur FC", "WS Wanderers vs Wellington", "Wellington vs Sydney FC", "Macarthur FC vs Wellington", "Sydney FC vs Wellington", "Wellington vs WS Wanderers", "Western United vs WS Wanderers", "Western United vs Macarthur FC", "Sydney FC vs Western United", "Macarthur FC vs Western United", "WS Wanderers vs Western United", "Western United vs Sydney FC"], "teams_affected": ["Perth Glory", "Sydney FC", "Brisbane Roar", "Newcastle Jets", "Melbourne V.", "Macarthur FC", "Adelaide Utd", "Melbourne City", "Western United", "Central Coast", "WS Wanderers", "Auckland", "Wellington"], "total_matches": 251, "valid_matches": 190, "skipped_matches": 61}, "SWITZERLAND_SUPER_LEAGUE": {"warning_count": 81, "missing_matches": ["Grasshopper vs FC Basel", "BSC Young Boys vs Grasshopper", "Grasshopper vs FC Zurich", "FC Zurich vs Grasshopper", "FC Basel vs Grasshopper", "Grasshopper vs BSC Young Boys", "Grasshopper vs BSC Young Boys", "Grasshopper vs FC Zurich", "FC Basel vs Grasshopper", "Lausanne Sport vs FC Basel", "FC Zurich vs Lausanne Sport", "BSC Young Boys vs Lausanne Sport", "FC Basel vs Lausanne Sport", "Lausanne Sport vs FC Zurich", "Lausanne Sport vs BSC Young Boys", "FC Basel vs Lausanne Sport", "BSC Young Boys vs Lausanne Sport", "FC Zurich vs Lausanne Sport", "FC Basel vs Lugano", "FC Zurich vs Lugano", "Lugano vs BSC Young Boys", "BSC Young Boys vs Lugano", "Lugano vs FC Zurich", "Lugano vs FC Basel", "BSC Young Boys vs Lugano", "Lugano vs FC Zurich", "FC Basel vs Lugano", "FC Zurich vs Luzern", "Luzern vs FC Basel", "BSC Young Boys vs Luzern", "Luzern vs BSC Young Boys", "Luzern vs FC Zurich", "FC Basel vs Luzern", "Luzern vs FC Basel", "FC Zurich vs Luzern", "Luzern vs BSC Young Boys", "<PERSON><PERSON><PERSON> vs BSC Young Boys", "Servette vs FC Basel", "FC Zurich vs Servette", "Servette vs FC Zurich", "FC Basel vs Servette", "BSC Young Boys vs Servette", "Servette vs FC Basel", "FC Zurich vs Servette", "<PERSON><PERSON><PERSON> vs BSC Young Boys", "BSC Young Boys vs Sion", "Sion vs FC Basel", "FC Zurich vs Sion", "Sion vs FC Zurich", "Sion vs BSC Young Boys", "FC Basel vs Sion", "BSC Young Boys vs Sion", "Sion vs FC Zurich", "FC Basel vs Sion", "<PERSON><PERSON> Galle<PERSON> vs BSC Young Boys", "St. Gallen vs FC Zurich", "FC Basel vs St. Gallen", "BSC Young Boys vs St. Gallen", "St. Gallen vs FC Basel", "FC Zurich vs St. Gallen", "FC Zurich vs St. Gallen", "St. Gallen vs FC Basel", "BSC Young Boys vs St. Gallen", "FC Zurich vs Winterthur", "<PERSON><PERSON><PERSON> vs BSC Young Boys", "Winterthur vs FC Basel", "FC Basel vs Winterthur", "BSC Young Boys vs Winterthur", "Winterthur vs FC Zurich", "<PERSON><PERSON><PERSON> vs BSC Young Boys", "Winterthur vs FC Basel", "Winterthur vs FC Zurich", "Yverdon vs FC Zurich", "Yverdon vs BSC Young Boys", "FC Basel vs Yverdon", "Yverdon vs FC Basel", "FC Zurich vs Yverdon", "BSC Young Boys vs Yverdon", "FC Zurich vs Yverdon", "BSC Young Boys vs Yverdon", "FC Basel vs Yverdon"], "teams_affected": ["Luzern", "<PERSON><PERSON><PERSON>", "Lausanne Sport", "Lugano", "Yverdon", "FC Basel", "Grasshopper", "St. Gallen", "FC Zurich", "Winterthur", "Sion", "BSC Young Boys"], "total_matches": 297, "valid_matches": 216, "skipped_matches": 81}, "SWEDEN_DIV_2_NORRA_GOTALAND": {"warning_count": 3, "missing_matches": ["Grebbestad vs Tidaholms", "Vanersborgs IF vs Lidkoping", "Motala vs FBK Karlstad"], "teams_affected": ["<PERSON><PERSON><PERSON>", "Tidaholms", "Vanersborgs IF", "<PERSON><PERSON><PERSON><PERSON>", "Lidkoping", "FBK Karlstad"], "total_matches": 6, "valid_matches": 3, "skipped_matches": 3}, "PARAGUAY_DIVISION_INTERMEDIA": {"warning_count": 114, "missing_matches": ["S. Cara<PERSON>gua vs 12 de Junio VH", "12 de Junio VH vs Guairena", "Resistencia vs 12 de Junio VH", "12 de Junio VH vs Tacuary", "<PERSON><PERSON> vs 12 de Junio VH", "12 de Junio VH vs Pastoreo", "<PERSON><PERSON><PERSON>u vs 12 de Junio VH", "12 de Junio VH vs C. River Plate", "Guarani de Fram vs 12 de Junio VH", "12 de Junio VH vs D. Capiata", "San Lorenzo vs 12 de Junio VH", "Encarnacion vs 12 de Junio VH", "12 de Junio VH vs F. de la Mora", "Sol de America vs 12 de Junio VH", "12 de Junio VH vs Independiente", "12 de Junio VH vs S. Carapegua", "Guarani de Fram vs C. River Plate", "12 de Junio VH vs C. River Plate", "F. de la Mora vs C. River Plate", "C. River Plate vs Sol de America", "<PERSON><PERSON> Cap<PERSON> vs Guarani de Fram", "12 de Junio VH vs D. Capiata", "<PERSON><PERSON> <PERSON> la Mora vs D<PERSON>", "<PERSON><PERSON> vs Sol de America", "<PERSON><PERSON> vs Guarani de Fram", "<PERSON><PERSON> vs 12 de Junio VH", "<PERSON><PERSON> vs F. de la Mora", "Sol de America vs <PERSON><PERSON>", "Encarnacion vs Guarani de Fram", "Encarnacion vs 12 de Junio VH", "F. de la Mora vs Encarnacion", "Encarnacion vs Sol de America", "Sol de America vs F. de la Mora", "F. de la Mora vs Independiente", "<PERSON><PERSON> vs F. de la Mora", "F<PERSON> de la Mora vs Guairena", "Resistencia vs F. de la Mora", "<PERSON><PERSON> <PERSON> la Mora vs Tacuary", "<PERSON><PERSON> vs F. de la Mora", "<PERSON><PERSON> <PERSON> la Mora vs Pastoreo", "<PERSON><PERSON><PERSON> vs F<PERSON> de la Mora", "F. de la Mora vs C. River Plate", "Guarani de Fram vs F. de la Mora", "<PERSON><PERSON> <PERSON> la Mora vs D<PERSON>", "12 de Junio VH vs F. de la Mora", "F. de la Mora vs Encarnacion", "San Lorenzo vs F. de la Mora", "F<PERSON> de la Mora vs Sol de America", "12 de Junio VH vs Guairena", "F<PERSON> de la Mora vs Guairena", "Guairena vs Sol de America", "Guarani de Fram vs Guairena", "Resistencia vs Guarani de Fram", "<PERSON><PERSON><PERSON> de Fram vs Tacuary", "<PERSON><PERSON> vs Guarani de Fram", "<PERSON><PERSON><PERSON> de Fram vs Pastoreo", "<PERSON><PERSON><PERSON> vs G<PERSON>rani de Fram", "Guarani de Fram vs C. River Plate", "San Lorenzo vs Guarani de Fram", "<PERSON><PERSON> Cap<PERSON> vs Guarani de Fram", "Guarani de Fram vs 12 de Junio VH", "Encarnacion vs Guarani de Fram", "Guarani de Fram vs F. de la Mora", "Sol de America vs Guarani de Fram", "Guarani de Fram vs Independiente", "S. Cara<PERSON>gua vs Guarani de Fram", "Guarani de Fram vs Guairena", "Guarani de Fram vs Resistencia", "F. de la Mora vs Independiente", "Independiente vs Sol de America", "Guarani de Fram vs Independiente", "12 de Junio VH vs Independiente", "<PERSON><PERSON><PERSON> de Fram vs Pastoreo", "12 de Junio VH vs Pastoreo", "<PERSON><PERSON> <PERSON> la Mora vs Pastoreo", "Pastoreo vs Sol de America", "Resistencia vs Guarani de Fram", "Resistencia vs 12 de Junio VH", "Resistencia vs F. de la Mora", "Sol de America vs Resistencia", "Guarani de Fram vs Resistencia", "<PERSON><PERSON><PERSON> vs G<PERSON>rani de Fram", "<PERSON><PERSON><PERSON>u vs 12 de Junio VH", "<PERSON><PERSON><PERSON> vs F<PERSON> de la Mora", "Sol de America vs Rubio Nu", "S. Cara<PERSON>gua vs 12 de Junio VH", "<PERSON><PERSON> vs F. de la Mora", "Sol de America vs S. Carapegua", "S. Cara<PERSON>gua vs Guarani de Fram", "12 de Junio VH vs S. Carapegua", "Sol de America vs San Lorenzo", "San Lorenzo vs Guarani de Fram", "San Lorenzo vs 12 de Junio VH", "San Lorenzo vs F. de la Mora", "Sol de America vs F. de la Mora", "Sol de America vs San Lorenzo", "Independiente vs Sol de America", "Sol de America vs S. Carapegua", "Guairena vs Sol de America", "Sol de America vs Resistencia", "Tacuary vs Sol de America", "Sol de America vs <PERSON><PERSON>", "Pastoreo vs Sol de America", "Sol de America vs Rubio Nu", "C. River Plate vs Sol de America", "Sol de America vs Guarani de Fram", "<PERSON><PERSON> vs Sol de America", "Sol de America vs 12 de Junio VH", "Encarnacion vs Sol de America", "F<PERSON> de la Mora vs Sol de America", "<PERSON><PERSON><PERSON> de Fram vs Tacuary", "12 de Junio VH vs Tacuary", "<PERSON><PERSON> <PERSON> la Mora vs Tacuary", "Tacuary vs Sol de America"], "teams_affected": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "C. <PERSON> Plate", "F. de la Mora", "<PERSON><PERSON>", "S. <PERSON>", "<PERSON><PERSON><PERSON>am", "Encarnacion", "Resistencia", "<PERSON><PERSON>", "Independiente", "12 de Junio VH", "Sol de America", "<PERSON><PERSON><PERSON><PERSON>", "San Lorenzo", "<PERSON><PERSON>"], "total_matches": 256, "valid_matches": 142, "skipped_matches": 114}, "SPAIN_LIGA_F_WOMEN": {"warning_count": 71, "missing_matches": ["Athletic Club W vs Madrid CFF W", "Athletic Club W vs L. Badalona W", "Athletic Club W vs FC Barcelona W", "L. Badalona W vs Athletic Club W", "Madrid CFF W vs Athletic Club W", "Atletico M. W vs Madrid CFF W", "Atletico M. W vs FC Barcelona W", "L. Badalona W vs Atletico M. W", "Madrid CFF W vs Atletico M. W", "FC Barcelona W vs Atletico M. W", "Deportivo W vs FC Barcelona W", "L. Badalona W vs Deportivo W", "Deportivo W vs Madrid CFF W", "Deportivo W vs L. Badalona W", "FC Barcelona W vs Eibar W", "Madrid CFF W vs Eibar W", "<PERSON><PERSON> vs Eibar W", "Eibar W vs Madrid CFF W", "Eibar W vs FC Barcelona W", "Eibar W vs L. Badalona W", "Madrid CFF W vs Espanyol W", "FC Barcelona W vs Espanyol W", "L. Badalona W vs Espanyol W", "Espanyol W vs FC Barcelona W", "Espanyol W vs Madrid CFF W", "Espanyol W vs L. Badalona W", "Madrid CFF W vs G. Tenerife W", "<PERSON><PERSON> vs G. Ten<PERSON>fe W", "FC Barcelona W vs G. Tenerife W", "G. Tenerife W vs Madrid CFF W", "<PERSON><PERSON> vs <PERSON><PERSON> Badalo<PERSON> W", "G. Tenerife W vs FC Barcelona W", "FC Barcelona W vs Granada W", "Granada W vs L. Badalona W", "Granada W vs Madrid CFF W", "L. Badalona W vs Granada W", "Madrid CFF W vs Granada W", "Granada W vs FC Barcelona W", "L. Badalona W vs Levante W", "Levante W vs FC Barcelona W", "Madrid CFF W vs Levante W", "Levante W vs L. Badalona W", "FC Barcelona W vs Levante W", "Real Betis W vs L. Badalona W", "FC Barcelona W vs Real Betis W", "Real Betis W vs Madrid CFF W", "L. Badalona W vs Real Betis W", "Madrid CFF W vs Real Betis W", "Madrid CFF W vs Real Madrid W", "L. Badalona W vs Real Madrid W", "Real Madrid W vs FC Barcelona W", "Real Madrid W vs L. Badalona W", "FC Barcelona W vs Real Madrid W", "Real Madrid W vs Madrid CFF W", "FC Barcelona W vs Real Sociedad W", "Real Sociedad W vs L. Badalona W", "Real Sociedad W vs Madrid CFF W", "Real Sociedad W vs FC Barcelona W", "Madrid CFF W vs Real Sociedad W", "L. Badalona W vs Real Sociedad W", "Sevilla W vs FC Barcelona W", "Sevilla W vs L. Badalona W", "Madrid CFF W vs Sevilla W", "<PERSON>. Badalona W vs Sevilla W", "Sevilla W vs Madrid CFF W", "FC Barcelona W vs Sevilla W", "Valencia W vs L. Badalona W", "Valencia W vs FC Barcelona W", "Madrid CFF W vs Valencia W", "FC Barcelona W vs Valencia W", "Valencia W vs Madrid CFF W"], "teams_affected": ["FC Barcelona W", "Valencia W", "Madrid CFF W", "Real Sociedad W", "L. <PERSON> W", "<PERSON><PERSON>", "Eibar W", "Levante W", "Sevilla W", "Real Betis W", "Real Madrid W", "Atletico M. W", "Deportivo W", "Granada W", "Espanyol W", "Athletic Club W"], "total_matches": 351, "valid_matches": 280, "skipped_matches": 71}, "ISRAEL_LIGA_ALEF_SOUTH": {"warning_count": 30, "missing_matches": ["A. Sport Ashdod vs Jerusalem FC", "Jerusalem FC vs A. Sport Ashdod", "Dimona vs Jerusalem FC", "Jerusalem FC vs Dimona", "Jerusalem FC vs H. Marmorek", "H. Marmorek vs Jerusalem FC", "Jerusalem FC vs Hapoel Azor", "Hapoel Azor vs Jerusalem FC", "Hapoel B. Lod vs Jerusalem FC", "Jerusalem FC vs Hapoel B. Lod", "Jerusalem FC vs Hapoel Herzliya", "Hapoel Herzliya vs Jerusalem FC", "Holon Yermiyahu vs Jerusalem FC", "Jerusalem FC vs Holon Yermiyahu", "Ironi Ashdod vs Jerusalem FC", "Jerusalem FC vs Ironi Ashdod", "Jerusalem FC vs Ironi Modiin", "Ironi Modiin vs Jerusalem FC", "Jerusalem FC vs M. Ironi Kyriat", "<PERSON><PERSON> Ironi Kyriat vs Jerusalem FC", "<PERSON><PERSON> vs Jerusalem FC", "Jerusalem FC vs <PERSON><PERSON>", "Maccabi Yavne vs Jerusalem FC", "Jerusalem FC vs Maccabi Yavne", "Jerusalem FC vs Nordia J.", "Nordia J. vs Jerusalem FC", "Jerusalem FC vs Shimshon T.", "Shimshon T. vs Jerusalem FC", "Tzeirey Tira vs Jerusalem FC", "Jerusalem FC vs Tzeirey Tira"], "teams_affected": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Hapoel Herzliya", "Nordia J.", "Hapoel Azor", "<PERSON><PERSON><PERSON>.", "<PERSON><PERSON>", "Maccabi Yavne", "Hapoel B. Lod", "A. Sport Ashdod", "Jerusalem FC", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "total_matches": 450, "valid_matches": 420, "skipped_matches": 30}, "FRANCE_NATIONAL_2_GROUP_B": {"warning_count": 92, "missing_matches": ["Avranches vs St Co Locmine", "Le Poiré SV vs Avranches", "Avranches vs St-Pryve St-H.", "Saint-Malo vs Avranches", "St Co Locmine vs Avranches", "Avranches vs Le Poiré SV", "St-Pryve St-H. vs Avranches", "Avranches vs Saint-Malo", "St Co Locmine vs Blois", "Blois vs Le Poiré SV", "St-Pryve St-H. vs Blois", "Blois vs Saint-Malo", "Blois vs St Co Locmine", "Le Poiré SV vs Blois", "Blois vs St-Pryve St-H.", "Saint-Malo vs Blois", "Bordeaux vs St-Pryve St-H.", "Le Poiré SV vs Bordeaux", "Saint-Malo vs Bordeaux", "Bordeaux vs St Co Locmine", "Bordeaux vs Le Poiré SV", "St-Pryve St-H. vs Bordeaux", "Bordeaux vs Saint-Malo", "Bourges vs Le Poiré SV", "St-Pryve St-H. vs Bourges", "Bourges vs Saint-Malo", "St Co Locmine vs Bourges", "Bourges vs St-Pryve St-H.", "Saint-Malo vs Bourges", "Bourges vs St Co Locmine", "Chateaubriant vs Saint-Malo", "Chateaubriant vs St Co Locmine", "Chateaubriant vs Le Poiré SV", "St-Pryve St-H. vs Chateaubriant", "St Co Locmine vs Chateaubriant", "Le Poiré SV vs Chateaubriant", "Chateaubriant vs St-Pryve St-H.", "<PERSON><PERSON> vs St-Pryve St-H.", "<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs St Co Locmine", "Le Poiré SV vs <PERSON><PERSON>", "St-P<PERSON><PERSON>-H. vs <PERSON><PERSON>", "<PERSON><PERSON> vs Saint-Mal<PERSON>", "St Co Locmine vs <PERSON><PERSON>", "<PERSON><PERSON> vs Le Poiré SV", "Granville vs St-Pryve St-H.", "Saint-Malo vs Granville", "St Co Locmine vs Granville", "Granville vs Le Poiré SV", "St-Pryve St-H. vs Granville", "Granville vs Saint-Malo", "Granville vs St Co Locmine", "Le Poiré SV vs Granville", "La Roche vs St-Pryve St-H.", "Saint-Malo vs La Roche", "St Co Locmine vs La Roche", "La Roche vs Le Poiré SV", "St-Pryve St-H. vs La Roche", "La Roche vs Saint-Malo", "La Roche vs St Co Locmine", "Le Poiré SV vs La Roche", "St-Pryve St-H. vs Les Herbiers", "Les Herbiers vs Saint-Malo", "Les Herbiers vs St Co Locmine", "Le Poiré SV vs Les Herbiers", "Les Herbiers vs St-Pryve St-H.", "Saint-Malo vs Les Herbiers", "St Co Locmine vs Les Herbiers", "Les Herbiers vs Le Poiré SV", "St-Pryve St-H. vs Poitiers", "Poitiers vs Saint-Malo", "St Co Locmine vs Poitiers", "Poitiers vs Le Poiré SV", "Poitiers vs St-Pryve St-H.", "Saint-Malo vs Poitiers", "Poitiers vs St Co Locmine", "Le Poiré SV vs Poitiers", "St Co Locmine vs Saumur", "<PERSON><PERSON>ur vs Le Poiré SV", "St-Pryve St-H. vs Saumur", "Saumur vs Saint-Malo", "Saumur vs St Co Locmine", "Le Poiré SV vs Saumur", "<PERSON><PERSON>ur vs St-Pryve St-H.", "Saint-Malo vs Saumur", "St-Pryve St-H. vs Stade Briochin", "Stade Briochin vs Saint-Malo", "Stade Briochin vs St Co Locmine", "Le Poiré SV vs Stade Briochin", "Saint-Malo vs Stade Briochin", "St Co Locmine vs Stade Briochin", "Stade Briochin vs Le Poiré SV"], "teams_affected": ["Saint-Malo", "Poitiers", "Chateaubriant", "<PERSON><PERSON><PERSON>", "Bordeaux", "La Roche", "Granville", "Le Poiré SV", "St-Pryve St-H.", "Avranches", "<PERSON><PERSON>", "Stade Briochin", "St Co Locmine", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "total_matches": 348, "valid_matches": 256, "skipped_matches": 92}, "QATAR_STARS_LEAGUE": {"warning_count": 22, "missing_matches": ["Qatar SC vs Al Ahli Doha", "Al Ahli Doha vs Qatar SC", "Al Arabi vs Qatar SC", "Qatar SC vs Al Arabi", "Qatar SC vs Al Duhail", "Al Duhail vs Qatar SC", "Qatar SC vs Al Gharafa", "Al Gharafa vs Qatar SC", "Al Khor vs Qatar SC", "Qatar SC vs Al Khor", "Qatar SC vs Al Rayyan", "Al Rayyan vs Qatar SC", "Qatar SC vs Al Sadd", "Al Sadd vs Qatar SC", "Qatar SC vs Al Shahaniya", "Al Shahaniya vs Qatar SC", "Al Shamal vs Qatar SC", "Qatar SC vs Al Shamal", "Qatar SC vs Al Wakrah", "Al Wakrah vs Qatar SC", "Umm Salal vs Qatar SC", "Qatar SC vs Umm Salal"], "teams_affected": ["Al Shahaniya", "Al Ahli Doha", "Al Gharafa", "Al Sadd", "Al Arabi", "Al Wakrah", "Al Rayyan", "<PERSON><PERSON>", "Al Khor", "Qatar SC", "Al Shamal", "<PERSON>"], "total_matches": 242, "valid_matches": 220, "skipped_matches": 22}, "ALBANIA_SUPERLIGA": {"warning_count": 64, "missing_matches": ["Bylis vs KF Tirana", "Bylis vs AF Elbasani", "KF Tirana vs Bylis", "AF Elbasani vs Bylis", "Bylis vs KF Tirana", "Bylis vs AF Elbasani", "KF Tirana vs Bylis", "AF Elbasani vs Bylis", "Dinamo Tirana vs AF Elbasani", "KF Tirana vs Dinamo Tirana", "AF Elbasani vs Dinamo Tirana", "Dinamo Tirana vs KF Tirana", "Dinamo Tirana vs AF Elbasani", "KF Tirana vs Dinamo Tirana", "AF Elbasani vs Dinamo Tirana", "Dinamo Tirana vs KF Tirana", "KF Tirana vs Egnatia R.", "AF Elbasani vs Egnatia R.", "Egnatia R. vs KF Tirana", "Egnatia R. vs AF Elbasani", "KF Tirana vs Egnatia R.", "AF Elbasani vs Egnatia R.", "Egnatia R. vs KF Tirana", "Egnatia R. vs AF Elbasani", "Laci vs KF Tirana", "AF Elbasani vs Laci", "KF Tirana vs Laci", "Laci vs AF Elbasani", "Laci vs KF Tirana", "AF Elbasani vs Laci", "KF Tirana vs Laci", "Laci vs AF Elbasani", "Partizani T. vs KF Tirana", "AF Elbasani vs Partizani T.", "KF Tirana vs Partizani T.", "Partizani T. vs AF Elbasani", "Partizani T. vs KF Tirana", "AF Elbasani vs Partizani T.", "KF Tirana vs Partizani T.", "Partizani T. vs AF Elbasani", "Skenderbeu K. vs AF Elbasani", "KF Tirana vs Skenderbeu K.", "AF Elbasani vs Skenderbeu K.", "Skenderbeu K. vs KF Tirana", "Skenderbeu K. vs AF Elbasani", "KF Tirana vs Skenderbeu K.", "AF Elbasani vs Skenderbeu K.", "Skenderbeu K. vs KF Tirana", "AF Elbasani vs Teuta Durres", "Teuta Durres vs KF Tirana", "Teuta Durres vs AF Elbasani", "KF Tirana vs Teuta Durres", "AF Elbasani vs Teuta Durres", "Teuta Durres vs KF Tirana", "Teuta Durres vs AF Elbasani", "KF Tirana vs Teuta Durres", "Vllaznia S. vs KF Tirana", "Vllaznia S. vs AF Elbasani", "KF Tirana vs Vllaznia S.", "AF Elbasani vs Vllaznia S.", "Vllaznia S. vs KF Tirana", "Vllaznia S. vs AF Elbasani", "KF Tirana vs Vllaznia S.", "AF Elbasani vs Vllaznia S."], "teams_affected": ["AF Elbasani", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Dinamo Tirana", "Skenderbeu K.", "KF Tirana", "Vllaznia S.", "Partizani T.", "<PERSON><PERSON><PERSON>"], "total_matches": 288, "valid_matches": 224, "skipped_matches": 64}, "CYPRUS_SECOND_DIVISION": {"warning_count": 77, "missing_matches": ["<PERSON><PERSON> vs ASIL Lysi", "<PERSON><PERSON> vs PAEEK", "<PERSON><PERSON> vs MEAP Nisou", "<PERSON><PERSON> vs PAEEK", "ASIL Lysi vs <PERSON><PERSON>", "PAEEK vs <PERSON><PERSON>", "<PERSON><PERSON> vs ASIL Lysi", "PAEEK vs <PERSON><PERSON>", "MEAP Nisou vs <PERSON><PERSON>", "ASIL Lysi vs <PERSON><PERSON>", "<PERSON><PERSON> vs ASIL Lysi", "PAEEK vs <PERSON><PERSON>", "ASIL Lysi vs <PERSON><PERSON>", "<PERSON><PERSON> vs PAEEK", "Anagennisi D. vs PAEEK", "MEAP Nisou vs Anagennisi D.", "ASIL Lysi vs Anagennisi D.", "MEAP Nisou vs Anagennisi D.", "Anagen<PERSON>i D. vs MEAP Nisou", "<PERSON><PERSON><PERSON> vs PAEEK", "<PERSON><PERSON><PERSON> vs MEAP Nisou", "<PERSON><PERSON><PERSON> vs ASIL Lysi", "<PERSON><PERSON><PERSON> vs MEAP Nisou", "MEAP Nisou vs <PERSON><PERSON><PERSON>", "<PERSON><PERSON> vs MEAP Nisou", "<PERSON><PERSON> vs ASIL Lysi", "PAEEK vs D. <PERSON>s", "<PERSON><PERSON> vs PAEEK", "ASIL Lysi vs D. <PERSON>s", "PAEEK vs D. <PERSON>s", "<PERSON><PERSON> vs ASIL Lysi", "Digenis vs MEAP Nisou", "Digenis vs ASIL Lysi", "Digenis vs PAEEK", "Digenis vs PAEEK", "Digenis vs ASIL Lysi", "PAEEK vs Digenis", "ASIL Lysi vs Digenis", "MEAP Nisou vs Doxa Kat<PERSON>pia", "<PERSON><PERSON> Kat<PERSON> vs PAEEK", "ASIL Lysi vs Doxa Katokopia", "MEAP Nisou vs Doxa Kat<PERSON>pia", "<PERSON><PERSON> vs MEAP Nisou", "MEAP Nisou vs Halkanoras", "ASIL Lysi vs Halkanoras", "PAEEK vs Halkanoras", "MEAP Nisou vs Halkanoras", "Halkanoras vs MEAP Nisou", "PAEEK vs Kitiou", "Kit<PERSON>u vs MEAP Nisou", "Kitiou vs ASIL Lysi", "Kit<PERSON>u vs MEAP Nisou", "MEAP Nisou vs Kitiou", "Olympiakos N. vs PAEEK", "MEAP Nisou vs Olympiakos N.", "ASIL Lysi vs Olympiakos N.", "Olympiakos N. vs ASIL Lysi", "Olympiakos N. vs PAEEK", "ASIL Lysi vs Olympiakos N.", "PAEEK vs Olympiakos N.", "ASIL Lysi vs Othellos", "PAEEK vs Othellos", "MEAP Nisou vs Othellos", "Othellos vs MEAP Nisou", "MEAP Nisou vs Othellos", "MEAP Nisou vs Peyia", "ASIL Lysi vs Peyia", "Peyia vs PAEEK", "MEAP Nisou vs Peyia", "Peyia vs MEAP Nisou", "MEAP Nisou vs Zaka<PERSON>ou", "Zakakiou vs ASIL Lysi", "<PERSON><PERSON><PERSON><PERSON> vs PAEEK", "ASIL Lysi vs Zakakiou", "PAEEK vs Zakakiou", "Zakakiou vs ASIL Lysi", "<PERSON><PERSON><PERSON><PERSON> vs PAEEK"], "teams_affected": ["Kit<PERSON><PERSON>", "<PERSON><PERSON>", "Olympiakos N.", "PAEEK", "<PERSON><PERSON><PERSON>", "MEAP Nisou", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "P<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Anagennisi D.", "ASIL Lysi", "<PERSON><PERSON>", "Hal<PERSON><PERSON><PERSON>"], "total_matches": 377, "valid_matches": 300, "skipped_matches": 77}, "SLOVENIA_PRVA_LIGA": {"warning_count": 54, "missing_matches": ["NK Celje vs Bravo", "NK Olimpija vs Bravo", "Bravo vs NK Celje", "Bravo vs NK Olimpija", "NK Celje vs Bravo", "NK Olimpija vs Bravo", "Bravo vs NK Celje", "Domzale vs NK Olimpija", "Domzale vs NK Celje", "NK Olimpija vs Domzale", "NK Celje vs Domzale", "Domzale vs NK Olimpija", "Domzale vs NK Celje", "NK Olimpija vs Domzale", "<PERSON><PERSON> vs NK Olimpija", "<PERSON><PERSON> vs NK Celje", "NK Olimpija vs Koper", "NK Celje vs Koper", "<PERSON><PERSON> vs NK Olimpija", "NK Olimpija vs Koper", "Maribor vs NK Olimpija", "NK Celje vs Maribor", "Maribor vs NK Celje", "NK Olimpija vs Maribor", "NK Celje vs Maribor", "Maribor vs NK Olimpija", "Mura vs NK Celje", "NK Olimpija vs Mura", "NK Celje vs Mura", "Mura vs NK Olimpija", "Mura vs NK Celje", "NK Olimpija vs Mura", "NK Celje vs Mura", "NK Olimpija vs Nafta", "NK Celje vs Nafta", "Nafta vs NK Olimpija", "Nafta vs NK Celje", "NK Olimpija vs Nafta", "NK Celje vs Nafta", "Nafta vs NK Olimpija", "NK Olimpija vs Primorje", "NK Celje vs Primorje", "Primorje vs NK Olimpija", "Primorje vs NK Celje", "NK Olimpija vs Primorje", "NK Celje vs Primorje", "Primorje vs NK Olimpija", "NK Celje vs Radomlje", "Radomlje vs NK Olimpija", "Radomlje vs NK Celje", "NK Olimpija vs Radomlje", "NK Celje vs Radomlje", "Radomlje vs NK Olimpija", "Radomlje vs NK Celje"], "teams_affected": ["NK Olimpija", "Bravo", "NK Celje", "<PERSON><PERSON>", "<PERSON><PERSON>", "Dom<PERSON><PERSON>", "Primorje", "<PERSON><PERSON><PERSON>", "Maribor", "Radomlje"], "total_matches": 252, "valid_matches": 198, "skipped_matches": 54}, "ENGLAND_PROFESSIONAL_DEVELOPMENT_LEAGUE": {"warning_count": 31, "missing_matches": ["Barnsley U21 vs QP Rangers U21", "QP Rangers U21 vs Birmingham U21", "Bournemouth U21 vs QP Rangers U21", "QP Rangers U21 vs Bournemouth U21", "Brentford U21 vs QP Rangers U21", "QP Rangers U21 vs Brentford U21", "QP Rangers U21 vs Bristol C. U21", "Bristol C. U21 vs QP Rangers U21", "Burnley U21 vs QP Rangers U21", "Cardiff C. U21 vs QP Rangers U21", "QP Rangers U21 vs Cardiff C. U21", "Charlton U21 vs QP Rangers U21", "QP Rangers U21 vs Charlton U21", "Colchester U21 vs QP Rangers U21", "QP Rangers U21 vs Colchester U21", "QP Rangers U21 vs Coventry U21", "Crewe A. U21 vs QP Rangers U21", "QP Rangers U21 vs Fleetwood U21", "Hull City U21 vs QP Rangers U21", "Ipswich U21 vs QP Rangers U21", "QP Rangers U21 vs Ipswich U21", "Millwall U21 vs QP Rangers U21", "QP Rangers U21 vs Millwall U21", "QP Rangers U21 vs Peterb. U21", "QP Rangers U21 vs Sheffield U U21", "Sheffield W U21 vs QP Rangers U21", "Swansea U21 vs QP Rangers U21", "QP Rangers U21 vs Swansea U21", "Watford U21 vs QP Rangers U21", "QP Rangers U21 vs Watford U21", "QP Rangers U21 vs Wigan U21"], "teams_affected": ["Watford U21", "Barnsley U21", "Bristol C. U21", "Bournemouth U21", "QP Rangers U21", "Burnley U21", "Hull City U21", "Colchester U21", "Coventry U21", "Millwall U21", "Wigan U21", "Brentford U21", "Peterb. U21", "Crewe A. U21", "Fleetwood U21", "Sheffield W U21", "Cardiff C. U21", "Charlton U21", "Ipswich U21", "Swansea U21", "Birmingham U21", "Sheffield U U21"], "total_matches": 655, "valid_matches": 624, "skipped_matches": 31}, "POLAND_EKSTRALIGA_WOMEN": {"warning_count": 43, "missing_matches": ["GKS Katowice W vs C. Sosnowiec W", "Skra W vs C. Sosnowiec W", "<PERSON><PERSON> W vs UKS SMS Lodz W", "C. Sosnowiec W vs GKS Katowice W", "UKS SMS Lodz W vs C. Sosnowiec W", "UKS SMS Lodz W vs Gdansk W", "Gdansk W vs GKS Katowice W", "Gdansk W vs Skra W", "Gdansk W vs UKS SMS Lodz W", "GKS Katowice W vs Gdansk W", "UKS SMS Lodz W vs Leczna W", "GKS Katowice W vs Leczna W", "Skra W vs Leczna W", "Leczna W vs UKS SMS Lodz W", "Leczna W vs GKS Katowice W", "P. Szczecin W vs GKS Katowice W", "<PERSON><PERSON>z<PERSON> W vs Skra W", "<PERSON>. <PERSON>zcz<PERSON>in W vs UKS SMS Lodz W", "GKS Katowice W vs P. Szczecin W", "UKS SMS Lodz W vs P. Tczew W", "<PERSON><PERSON> W vs GKS Katowice W", "<PERSON><PERSON> vs Skra W", "<PERSON><PERSON> vs UKS SMS Lodz W", "Skra W vs Rekord B. W", "UKS SMS Lodz W vs Rekord B. W", "GKS Katowice W vs Rekord B. W", "Rekord B. W vs Skra W", "Rekord B. W vs UKS SMS Lodz W", "GKS Katowice W vs Resovia R. W", "Resovia R. W vs UKS SMS Lodz W", "Resovia R. W vs Skra W", "Resovia R. W vs GKS Katowice W", "UKS SMS Lodz W vs Resovia R. W", "Skra W vs Resovia R. W", "GKS Katowice W vs Slask Wroclaw W", "Skra W vs Slask Wroclaw W", "Slask Wroclaw W vs UKS SMS Lodz W", "Slask Wroclaw W vs GKS Katowice W", "Slask Wroclaw W vs Skra W", "Stomilanki O. W vs Skra W", "UKS SMS Lodz W vs Stomilanki O. W", "Stomilanki O. W vs GKS Katowice W", "Skra W vs Stomilanki O. W"], "teams_affected": ["<PERSON><PERSON>", "Stomilanki O. W", "<PERSON><PERSON>", "<PERSON><PERSON> W", "UKS SMS Lodz W", "Rekord B. W", "Slask Wroclaw W", "Gdansk W", "Resovia R. W", "GKS Katowice W", "Leczna W", "Skra W"], "total_matches": 167, "valid_matches": 124, "skipped_matches": 43}, "GERMANY_OBERLIGA_NIEDERSACHSEN": {"warning_count": 124, "missing_matches": ["<PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON><PERSON>", "BSV Rehden vs <PERSON><PERSON>", "<PERSON><PERSON> vs VfL Oldenburg", "Spelle<PERSON><PERSON><PERSON>haus vs <PERSON><PERSON>", "HSC Hannover vs <PERSON><PERSON>", "<PERSON><PERSON> vs Heeslinger SC", "<PERSON><PERSON> vs BSV Rehden", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs Spelle-Venhaus", "<PERSON><PERSON> vs HSC Hannover", "VfL Oldenburg vs Bersenbruck", "HSC Hannover vs Bersenbruck", "Bersenbruck vs Lupo-Martini", "BSV Rehden vs Bersenbruck", "Spelle-Venhaus vs Bersenbruck", "Bersenbruck vs Heeslinger SC", "Bersenbruck vs HSC Hannover", "Heeslinger SC vs Bersenbruck", "Lupo-Martini vs Bersenbruck", "Bersenbruck vs VfL Oldenburg", "Bersenbruck vs BSV Rehden", "Spelle-Venhaus vs Braunschweig B", "Braunschweig B vs Heeslinger SC", "VfL Oldenburg vs Braunschweig B", "BSV Rehden vs Braunschweig B", "HSC Hannover vs Braunschweig B", "Braunschweig B vs Spelle-Venhaus", "Braunschweig B vs Lupo-Martini", "Heeslinger SC vs Braunschweig B", "Braunschweig B vs VfL Oldenburg", "Delmenhorst vs BSV Rehden", "Delmenhorst vs Spelle-Venhaus", "<PERSON>eslinger SC vs Delmenhorst", "Delmenhorst vs VfL Oldenburg", "Delmenhorst vs HSC Hannover", "<PERSON><PERSON><PERSON>st vs <PERSON><PERSON><PERSON><PERSON><PERSON>", "BSV Rehden vs Delmenhorst", "Spelle-Venhaus vs Delmenhorst", "<PERSON><PERSON><PERSON><PERSON> vs Heeslinger SC", "VfL Oldenburg vs Delmenhorst", "HSC Hannover vs Delmenhorst", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs Egestorf L.", "Egestorf L. vs BSV Rehden", "Egestorf L. vs Spelle-Venhaus", "Egestorf L. vs HSC Hannover", "Egestorf L. vs Heeslinger SC", "VfL Oldenburg vs Egestorf L.", "Egestorf L. vs <PERSON><PERSON><PERSON><PERSON><PERSON>", "BSV Rehden vs Egestorf L.", "Spelle-Venhaus vs Egestorf L.", "HSC Hannover vs Egestorf L.", "Heeslinger SC vs Egestorf L.", "BSV Rehden vs Eintracht Celle", "Eintracht Celle vs HSC Hannover", "Eintracht Celle vs Spelle-Venhaus", "Heeslinger SC vs Eintracht Celle", "Eintracht Celle vs VfL Oldenburg", "Eintracht Celle vs Lupo<PERSON><PERSON><PERSON>", "HSC Hannover vs Eintracht Celle", "Spelle-Venhaus vs Eintracht Celle", "Eintracht Celle vs Heeslinger SC", "VfL Oldenburg vs Eintracht Celle", "VfL Oldenburg vs Hildesheim", "Heeslinger SC vs Hildesheim", "Spelle-Venhaus vs Hildesheim", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON>", "HSC Hannover vs Hildesheim", "BSV Rehden vs Hildesheim", "Hildesheim vs VfL Oldenburg", "<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>sheim vs HSC Hannover", "Hildesheim vs Spelle-Venhaus", "Meppen B vs VfL Oldenburg", "HSC Hannover vs Meppen B", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs Meppen B", "Heeslinger SC vs Meppen B", "Meppen B vs BSV Rehden", "Meppen B vs Spelle-Venhaus", "VfL Oldenburg vs Meppen B", "Meppen B vs HSC Hannover", "Meppen B vs Lupo-Martini", "Meppen B vs Heeslinger SC", "BSV Rehden vs Meppen B", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs Schoningen", "Schoningen vs BSV Rehden", "Schoningen vs Spelle-Venhaus", "Heeslinger SC vs Schoningen", "VfL Oldenburg vs Schoningen", "Schoningen vs HSC Hannover", "Schoningen vs Lupo-Martini", "BSV Rehden vs Schoningen", "Spelle-Venhaus vs Schoningen", "Schoningen vs VfL Oldenburg", "Verden 04 vs Heeslinger SC", "VfL Oldenburg vs Verden 04", "HSC Hannover vs Verden 04", "Verden 04 vs Lupo-Martini", "BSV Rehden vs Verden 04", "Spelle-Venhaus vs Verden 04", "Heeslinger SC vs Verden 04", "Verden 04 vs HSC Hannover", "Lupo-Martini vs Verden 04", "Verden 04 vs VfL Oldenburg", "BSV Rehden vs Vorsfelde", "Spelle-Venhaus vs Vorsfelde", "Vorsfelde vs HSC Hannover", "Vorsfelde vs Lupo-Martini", "Heeslinger SC vs Vorsfelde", "Vorsfelde vs VfL Oldenburg", "Vorsfelde vs BSV Rehden", "Vorsfelde vs Spelle-Venhaus", "HSC Hannover vs Vorsfelde", "Vorsfelde vs Heeslinger SC", "Wilhelmshaven vs Spelle-Venhaus", "Heeslinger SC vs Wilhelmshaven", "Wilhelmshaven vs VfL Oldenburg", "Wilhelmshaven vs Lupo-Martini", "HSC Hannover vs Wilhelmshaven", "Wilhelmshaven vs BSV Rehden", "Spelle-Venhaus vs Wilhelmshaven", "VfL Oldenburg vs Wilhelmshaven", "Wilhelmshaven vs Heeslinger SC", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs Wilhelmshaven", "BSV Rehden vs Wilhelmshaven"], "teams_affected": ["<PERSON><PERSON>", "VfL Oldenburg", "HSC Hannover", "Schoningen", "Verden 04", "Spelle-Venhaus", "Heeslinger SC", "Bersenbruck", "Egestorf L.", "Eintracht Celle", "<PERSON><PERSON><PERSON><PERSON>", "Wilhelmshaven", "Braunschweig B", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Meppen B", "Vorsfelde", "<PERSON><PERSON><PERSON><PERSON>", "BSV Rehden"], "total_matches": 358, "valid_matches": 234, "skipped_matches": 124}, "PORTUGAL_LIGA_PRO": {"warning_count": 58, "missing_matches": ["Academico Viseu vs FC Porto B", "<PERSON><PERSON> vs Uniao de Leiria", "FC Porto B vs Academico Viseu", "Uniao de Leiria vs Academico Viseu", "FC Porto B vs Alverca", "Uniao de Leiria vs Alverca", "Alverca vs FC Porto B", "Alverca vs Uniao de Leiria", "Benfica B vs Uniao de Leiria", "Benfica B vs FC Porto B", "Uniao de Leiria vs Benfica B", "Chaves vs FC Porto B", "Uniao de Leiria vs Chaves", "FC Porto B vs Chaves", "Uniao de Leiria vs Feirense", "FC Porto B vs Feirense", "Feirense vs Uniao de Leiria", "Feirense vs FC Porto B", "FC Porto B vs Felgueiras", "Uniao de Leiria vs Felgueiras", "Felgueiras vs FC Porto B", "Felgueiras vs Uniao de Leiria", "Leixoes vs FC Porto B", "Uniao de Leiria vs Leixoes", "FC Porto B vs Leixoes", "Leixoes vs Uniao de Leiria", "FC Porto B vs Mafra", "Mafra vs Uniao de Leiria", "Mafra vs FC Porto B", "Uniao de Leiria vs Mafra", "Uniao de Leiria vs Maritimo", "Maritimo vs FC Porto B", "Maritimo vs Uniao de Leiria", "FC Porto B vs Maritimo", "FC Porto B vs Oliveirense", "Oliveirense vs Uniao de Leiria", "Oliveirense vs FC Porto B", "Pacos Ferreira vs FC Porto B", "Uniao de Leiria vs Pacos Ferreira", "FC Porto B vs Pacos Ferreira", "Pacos Ferreira vs Uniao de Leiria", "Penafiel vs FC Porto B", "Penafiel vs Uniao de Leiria", "FC Porto B vs Penafiel", "Uniao de Leiria vs Penafiel", "Portimonense vs Uniao de Leiria", "FC Porto B vs Portimonense", "Uniao de Leiria vs Portimonense", "Tondela vs FC Porto B", "Tondela vs Uniao de Leiria", "FC Porto B vs Tondela", "FC Porto B vs Torreense", "Torreense vs Uniao de Leiria", "Torreense vs FC Porto B", "Uniao de Leiria vs Torreense", "Uniao de Leiria vs Vizela", "Vizela vs FC Porto B", "V<PERSON>la vs Uniao de Leiria"], "teams_affected": ["To<PERSON><PERSON>", "FC Porto B", "Benfica B", "Felgueiras", "Portimonense", "<PERSON><PERSON><PERSON>", "Alverca", "<PERSON><PERSON>", "Leixoes", "<PERSON><PERSON>", "<PERSON><PERSON>", "Uniao de Leiria", "Penafiel", "Torreense", "<PERSON><PERSON><PERSON>", "Feirense", "<PERSON><PERSON>", "Oliveirense"], "total_matches": 492, "valid_matches": 434, "skipped_matches": 58}, "GREECE_SUPER_LEAGUE": {"warning_count": 112, "missing_matches": ["Olympiakos vs <PERSON><PERSON>", "<PERSON><PERSON> vs AEK Athens", "OFI Crete vs <PERSON><PERSON>", "PAOK vs <PERSON><PERSON>", "<PERSON><PERSON> vs Olympiakos", "AEK Athens vs <PERSON><PERSON>", "<PERSON><PERSON> vs OFI Crete", "<PERSON><PERSON> vs PAOK", "OFI Crete vs Aris", "Aris vs Olympiakos", "PAOK vs Aris", "AEK Athens vs Aris", "Aris vs OFI Crete", "Olympiakos vs Aris", "Aris vs PAOK", "Aris vs AEK Athens", "Asteras T. vs Olympiakos", "Asteras T. vs OFI Crete", "AEK Athens vs Asteras T.", "Asteras T. vs PAOK", "Olympiakos vs Asteras T.", "OFI Crete vs Asteras T.", "Asteras T. vs AEK Athens", "PAOK vs Asteras T.", "OFI Crete vs Atromitos", "Atromitos vs PAOK", "Olympiakos vs Atromitos", "Atromitos vs AEK Athens", "Atromitos vs OFI Crete", "PAOK vs Atromitos", "Atromitos vs Olympiakos", "AEK Athens vs Atromitos", "AEK Athens vs Lamia", "Lamia vs Olympiakos", "Lamia vs PAOK", "Lamia vs OFI Crete", "Lamia vs AEK Athens", "Olympiakos vs Lamia", "PAOK vs Lamia", "OFI Crete vs Lamia", "Levadiakos vs AEK Athens", "Levadiakos vs OFI Crete", "Levadiakos vs PAOK", "Olympiakos vs Levadiakos", "AEK Athens vs Levadiakos", "OFI Crete vs Levadiakos", "PAOK vs Levadiakos", "Levadiakos vs Olympiakos", "Volos vs Olympiakos", "Olympiakos vs <PERSON><PERSON>", "Lamia vs Olympiakos", "Olympiakos vs Panaitolikos", "Aris vs Olympiakos", "Olympiakos vs Atromitos", "Panathinaikos vs Olympiakos", "Olympiakos vs Levadiakos", "Asteras T. vs Olympiakos", "Olympiakos vs Panserraikos", "PAOK vs Olympiakos", "Olympiakos vs AEK Athens", "OFI Crete vs Olympiakos", "Olympiakos vs Volos", "<PERSON><PERSON> vs Olympiakos", "Olympiakos vs Lamia", "Panaitolikos vs Olympiakos", "Olympiakos vs Aris", "Atromitos vs Olympiakos", "Olympiakos vs Panathinaikos", "Levadiakos vs Olympiakos", "Olympiakos vs Asteras T.", "Panserraikos vs Olympiakos", "Olympiakos vs PAOK", "AEK Athens vs Olympiakos", "Olympiakos vs OFI Crete", "Olympiakos vs Panathinaikos", "PAOK vs Olympiakos", "Olympiakos vs AEK Athens", "PAOK vs Panaitolikos", "Olympiakos vs Panaitolikos", "OFI Crete vs Panaitolikos", "Panaitolikos vs AEK Athens", "Panaitolikos vs PAOK", "Panaitolikos vs Olympiakos", "Panaitolikos vs OFI Crete", "AEK Athens vs Panaitolikos", "PAOK vs Panathinaikos", "AEK Athens vs Panathinaikos", "Panathinaikos vs Olympiakos", "OFI Crete vs Panathinaikos", "Panathinaikos vs PAOK", "Panathinaikos vs AEK Athens", "Olympiakos vs Panathinaikos", "Panathinaikos vs OFI Crete", "Olympiakos vs Panathinaikos", "Panathinaikos vs AEK Athens", "Panathinaikos vs PAOK", "PAOK vs Panserraikos", "Panserraikos vs OFI Crete", "Panserraikos vs AEK Athens", "Olympiakos vs Panserraikos", "Panserraikos vs PAOK", "OFI Crete vs Panserraikos", "AEK Athens vs Panserraikos", "Panserraikos vs Olympiakos", "Volos vs Olympiakos", "Volos vs OFI Crete", "AEK Athens vs Volos", "Volos vs PAOK", "Olympiakos vs Volos", "OFI Crete vs Volos", "Volos vs AEK Athens", "PAOK vs Volos"], "teams_affected": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Panathinaikos", "PAOK", "AEK Athens", "<PERSON><PERSON>", "Atromitos", "Panaitolikos", "OFI Crete", "<PERSON><PERSON><PERSON>.", "Levadiakos", "Panserraikos", "Volos", "<PERSON><PERSON>"], "total_matches": 316, "valid_matches": 204, "skipped_matches": 112}, "AUSTRALIA_NEW_SOUTH_WALES_NPL": {"warning_count": 1, "missing_matches": ["Manly Utd vs Mount Druitt"], "teams_affected": ["Manly Utd", "Mount Druitt"], "total_matches": 20, "valid_matches": 19, "skipped_matches": 1}, "ISRAEL_LEUMIT_LEAGUE": {"warning_count": 36, "missing_matches": ["<PERSON><PERSON><PERSON> vs <PERSON><PERSON> <PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs B<PERSON><PERSON>", "<PERSON><PERSON> <PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON> <PERSON>", "<PERSON><PERSON> <PERSON><PERSON>Fahm vs Hapoel Afula", "Hapoel Afula vs H. Umm al-Fahm", "Hapoel Afula vs H. Umm al-Fahm", "<PERSON><PERSON> <PERSON>m al-Fahm vs Hapoel Akko", "Hapoel Akko vs H. Umm al-Fahm", "Hapoel Akko vs H. Umm al-Fahm", "Hapoel Raanana vs H. Umm al-Fahm", "<PERSON><PERSON> <PERSON><PERSON> al-Fahm vs Hapoel Raanana", "<PERSON><PERSON> <PERSON><PERSON> al-Fahm vs Hapoel Tel Aviv", "Hapoel Tel Aviv vs H. Umm al-Fahm", "<PERSON><PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON>", "<PERSON><PERSON> <PERSON> vs <PERSON><PERSON>", "Maccabi Jaffa vs H. Umm al-Fahm", "<PERSON><PERSON> <PERSON><PERSON> al-Fahm vs Maccabi Jaffa", "Maccabi Jaffa vs H. Umm al-Fahm", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>"], "teams_affected": ["<PERSON><PERSON>", "Hapoel Akko", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON>", "Hapoel Tel Aviv", "Maccabi Jaffa", "Hapoel Afula", "<PERSON><PERSON>", "<PERSON><PERSON>", "Hapoel Raanana", "<PERSON><PERSON>", "Bnei <PERSON>", "<PERSON><PERSON><PERSON>"], "total_matches": 534, "valid_matches": 498, "skipped_matches": 36}, "SERBIA_SUPERLIGA": {"warning_count": 85, "missing_matches": ["<PERSON><PERSON> vs IMT N. Beograd", "Backa Topola vs OFK Beograd", "<PERSON><PERSON> vs <PERSON><PERSON> Topola", "OFK Beograd vs Backa Topola", "IMT N. <PERSON> vs Backa Topola", "<PERSON><PERSON> vs <PERSON><PERSON>", "OFK Beograd vs Cukaricki", "<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "IMT N. <PERSON> vs <PERSON><PERSON><PERSON><PERSON>", "Cukaricki vs OFK Beograd", "<PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> vs IMT N. <PERSON>rad", "<PERSON><PERSON> vs Jedinstvo U.", "Jedinstvo U. vs IMT N. Beograd", "Jedinstvo U. vs OFK Beograd", "Jedinstvo U. vs <PERSON><PERSON>", "IMT N. Beograd vs Jedinstvo U.", "OFK Beograd vs Jedinstvo U.", "<PERSON><PERSON><PERSON> Lucani vs OFK Beograd", "<PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON><PERSON> vs IMT N. <PERSON>", "OFK Beograd vs Mladost Lucani", "<PERSON><PERSON> vs Mladost Lucani", "IMT N. <PERSON> vs Mlad<PERSON>i", "<PERSON><PERSON><PERSON>ak vs IMT N. Beograd", "Napredak vs OFK Beograd", "<PERSON><PERSON> vs Na<PERSON><PERSON><PERSON>", "IMT N. <PERSON> vs Na<PERSON>redak", "OFK Beograd vs Napredak", "<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "Novi Pazar vs OFK Beograd", "<PERSON><PERSON> vs Novi Pazar", "Novi Pazar vs IMT N. Beograd", "OFK Beograd vs Novi Pazar", "<PERSON> Pazar vs <PERSON><PERSON>", "IMT N. Beograd vs Novi Pazar", "IMT N. Beograd vs Partizan", "OFK Beograd vs Partizan", "Partizan vs <PERSON><PERSON>", "Partizan vs IMT N. Beograd", "Partizan vs OFK Beograd", "<PERSON><PERSON> vs Partizan", "Partizan vs OFK Beograd", "OFK Beograd vs Radnicki 1923", "IMT N. <PERSON> vs Radnicki 1923", "<PERSON><PERSON><PERSON><PERSON> 1923 vs <PERSON><PERSON>", "Radn<PERSON>i 1923 vs IMT N. <PERSON>rad", "Radnicki 1923 vs OFK Beograd", "<PERSON><PERSON> vs Radnicki 1923", "OFK Beograd vs Radnicki 1923", "IMT N. <PERSON> vs <PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Nis vs OFK Beograd", "<PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> vs IMT N. <PERSON>", "OFK Beograd vs Radnicki Nis", "<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> vs IMT N. <PERSON>", "<PERSON><PERSON> vs Red Star", "IMT N. Beograd vs Red Star", "OFK Beograd vs Red Star", "Red Star vs <PERSON><PERSON>", "Red Star vs IMT N. Beograd", "Red Star vs OFK Beograd", "Red Star vs OFK Beograd", "<PERSON><PERSON> vs IMT N. <PERSON>rad", "OFK Beograd vs S. Subotica", "<PERSON><PERSON> vs <PERSON><PERSON>", "IMT N. <PERSON> vs S. <PERSON>otica", "S. Subotica vs OFK Beograd", "<PERSON><PERSON> vs S. Subotica", "IMT N. <PERSON> vs S. <PERSON>otica", "<PERSON><PERSON> vs Vojvodina", "IMT N. Beograd vs Vojvodina", "Vojvodina vs OFK Beograd", "Vojvodina vs T<PERSON>", "Vojvodina vs IMT N. Beograd", "OFK Beograd vs Vojvodina", "<PERSON><PERSON> vs IMT N. <PERSON>rad", "<PERSON><PERSON> vs OFK Beograd", "<PERSON><PERSON> vs <PERSON><PERSON>", "IMT N. <PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "OFK Beograd vs Z. Pancevo", "<PERSON><PERSON> vs <PERSON><PERSON>"], "teams_affected": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Jedinstvo U.", "Novi Pazar", "<PERSON><PERSON>", "S. Subotica", "Napredak", "IMT N. Beograd", "Radnicki 1923", "Red Star", "<PERSON><PERSON><PERSON><PERSON>", "Vojvodina", "Partizan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "OFK Beograd"], "total_matches": 421, "valid_matches": 336, "skipped_matches": 85}, "SINGAPORE_S.LEAGUE": {"warning_count": 30, "missing_matches": ["A. <PERSON> (S) vs DPMM FC", "DPMM FC vs A. Niigata (S)", "A. <PERSON> (S) vs DPMM FC", "DPMM FC vs A. Niigata (S)", "DPMM FC vs Geylang", "Geylang vs DPMM FC", "DPMM FC vs Geylang", "Geylang vs DPMM FC", "DPMM FC vs Hougang", "Hougang vs DPMM FC", "DPMM FC vs Hougang", "Khalsa vs DPMM FC", "DPMM FC vs Khalsa", "DPMM FC vs Khalsa", "Khalsa vs DPMM FC", "DPMM FC vs Lion City", "Lion City vs DPMM FC", "DPMM FC vs Lion City", "Lion City vs DPMM FC", "Tampines Rovers vs DPMM FC", "DPMM FC vs Tampines Rovers", "Tampines Rovers vs DPMM FC", "Tanjong Pagar vs DPMM FC", "DPMM FC vs Tanjong Pagar", "Tanjong Pagar vs DPMM FC", "DPMM FC vs Tanjong Pagar", "Young Lions vs DPMM FC", "DPMM FC vs Young Lions", "DPMM FC vs Young Lions", "Young Lions vs DPMM FC"], "teams_affected": ["Tanjong Pagar", "Young Lions", "<PERSON><PERSON> (S)", "Khalsa", "Geylang", "Tampines Rovers", "Lion City", "DPMM FC", "<PERSON><PERSON><PERSON>"], "total_matches": 238, "valid_matches": 208, "skipped_matches": 30}, "UZBEKISTAN_SUPER_LEAGUE": {"warning_count": 6, "missing_matches": ["Shurtan vs Bunyodkor", "Buxoro vs Bunyodkor", "Xorazm vs Bunyodkor", "Bunyodkor vs AGMK", "Bunyodkor vs Kokand-1912", "Pakhtakor vs <PERSON><PERSON>"], "teams_affected": ["<PERSON><PERSON><PERSON>", "Shurtan", "AGMK", "Pakhtakor", "Kokand-1912", "<PERSON><PERSON>", "Xorazm", "Bunyodkor"], "total_matches": 12, "valid_matches": 6, "skipped_matches": 6}, "WALES_PREMIER_LEAGUE": {"warning_count": 32, "missing_matches": ["Aberystwyth vs Cardiff MU", "Cardiff MU vs Aberystwyth", "Bala Town vs Cardiff MU", "Cardiff MU vs Bala Town", "Cardiff MU vs Bala Town", "Bala Town vs Cardiff MU", "Cardiff MU vs Barry", "Barry vs Cardiff MU", "Briton Ferry vs Cardiff MU", "Cardiff MU vs Briton Ferry", "Caernarfon vs Cardiff MU", "Cardiff MU vs Caernarfon", "Cardiff MU vs Caernarfon", "Caernarfon vs Cardiff MU", "Cardiff MU vs Connahs Quay", "Connahs Quay vs Cardiff MU", "Flint vs Cardiff MU", "Cardiff MU vs Flint", "Cardiff MU vs Haverfordwest", "Haverfordwest vs Cardiff MU", "Haverfordwest vs Cardiff MU", "Cardiff MU vs Haverfordwest", "Newtown vs Cardiff MU", "Cardiff MU vs Newtown", "Cardiff MU vs Penybont", "Penybont vs Cardiff MU", "Cardiff MU vs Penybont", "Penybont vs Cardiff MU", "The New Saints vs Cardiff MU", "Cardiff MU vs The New Saints", "The New Saints vs Cardiff MU", "Cardiff MU vs The New Saints"], "teams_affected": ["Bala Town", "C<PERSON>rna<PERSON><PERSON>", "Newtown", "The New Saints", "<PERSON>", "Penybont", "Connahs Quay", "Briton Ferry", "Aberystwyth", "Haverfordwest", "Flint", "Cardiff MU"], "total_matches": 352, "valid_matches": 320, "skipped_matches": 32}, "ICELAND_URVALSDEILD": {"warning_count": 6, "missing_matches": ["Hafnarfjordur vs KR Reykjavik", "KA Akureyri vs Hafnarfjordur", "Afturelding vs Stjarnan", "IA Akranes vs Vestri", "IBV vs Vestri", "Vestri vs Afturelding"], "teams_affected": ["Stjar<PERSON>", "IBV", "Vestri", "IA Akranes", "KA Akureyri", "Hafnarfjordur", "Afturelding", "KR Reykjavik"], "total_matches": 14, "valid_matches": 8, "skipped_matches": 6}, "USA_USL_CHAMPIONSHIP": {"warning_count": 6, "missing_matches": ["Loudoun Utd vs Lexington", "Miami FC vs New Mexico", "FC Tulsa vs North Carolina", "Miami FC vs North Carolina", "FC Tulsa vs Tampa Bay", "Miami FC vs Tampa Bay"], "teams_affected": ["Lexington", "New Mexico", "Miami FC", "Tampa Bay", "Loudoun Utd", "FC Tulsa", "North Carolina"], "total_matches": 33, "valid_matches": 27, "skipped_matches": 6}, "AUSTRIA_BUNDESLIGA_WOMEN": {"warning_count": 32, "missing_matches": ["Austria Wien W vs LASK W", "Austria Wien W vs SCR Altach W", "LASK W vs Austria Wien W", "SCR Altach W vs Austria Wien W", "SCR Altach W vs Bergheim W", "LASK W vs Bergheim W", "Bergheim W vs SCR Altach W", "Bergheim W vs LASK W", "Dornbirn W vs LASK W", "Dornbirn W vs SCR Altach W", "LASK W vs Dornbirn W", "SCR Altach W vs Dornbirn W", "SCR Altach W vs First Vienna W", "LASK W vs First Vienna W", "First Vienna W vs SCR Altach W", "First Vienna W vs LASK W", "LASK W vs Kleinmunchen W", "Kleinmunchen W vs SCR Altach W", "Kleinmunchen W vs LASK W", "SCR Altach W vs Kleinmunchen W", "LASK W vs Neulengbach W", "Neulengbach W vs SCR Altach W", "Neulengbach W vs LASK W", "SCR Altach W vs Neulengbach W", "SCR Altach W vs St. Polten W", "LASK W vs St. Polten W", "St. Pol<PERSON> W vs SCR Altach W", "St. <PERSON> W vs LASK W", "Sturm Graz W vs SCR Altach W", "Sturm Graz W vs LASK W", "SCR Altach W vs Sturm Graz W", "LASK W vs Sturm Graz W"], "teams_affected": ["Sturm Graz W", "Kleinmunchen W", "First Vienna W", "SCR Altach W", "Neulengbach W", "Austria Wien W", "Dornbirn W", "Bergheim W", "St. Polten W", "LASK W"], "total_matches": 144, "valid_matches": 112, "skipped_matches": 32}, "BELGIUM_FIRST_DIVISION_B": {"warning_count": 108, "missing_matches": ["RFC Liege vs Anderlecht B", "Lommel SK vs Anderlecht B", "Anderlecht B vs Zulte-Waregem", "KRC Genk B vs Anderlecht B", "Anderlecht B vs RWDM", "Lokeren-Temse vs Anderlecht B", "Anderlecht B vs KRC Genk B", "RWDM vs Anderlecht B", "Anderlecht B vs Lommel SK", "Anderlecht B vs RFC Liege", "Zulte-Waregem vs Anderlecht B", "Anderlecht B vs Lokeren-Temse", "Beveren vs Lommel SK", "RFC Liege vs Beveren", "Beveren vs KRC Genk B", "Beveren vs Lokeren-Temse", "RWDM vs Beveren", "Zulte-Waregem vs Beveren", "Lokeren-Temse vs Beveren", "KRC Genk B vs Beveren", "Beveren vs RFC Liege", "Lommel SK vs Beveren", "Beveren vs Zulte-Waregem", "Beveren vs RWDM", "Club Brugge B vs Lokeren-Temse", "Zulte-Waregem vs Club Brugge B", "RFC Liege vs Club Brugge B", "Club Brugge B vs Lommel SK", "KRC Genk B vs Club Brugge B", "RWDM vs Club Brugge B", "Club Brugge B vs Zulte-Waregem", "Lommel SK vs Club Brugge B", "Club Brugge B vs RWDM", "Lokeren-Temse vs Club Brugge B", "Club Brugge B vs KRC Genk B", "Club Brugge B vs RFC Liege", "RFC Liege vs Eupen", "RWDM vs Eupen", "Eupen vs Zulte-Waregem", "Eupen vs KRC Genk B", "Lommel SK vs Eupen", "Eupen vs Lokeren-Temse", "Eupen vs RFC Liege", "Eupen vs RWDM", "Zulte-Waregem vs Eupen", "Lokeren-Temse vs Eupen", "Eupen vs Lommel SK", "KRC Genk B vs Eupen", "Lokeren-Temse vs Francs Borains", "KRC Genk B vs Francs Borains", "Francs Borains vs Lommel SK", "Francs Borains vs RWDM", "Francs Borains vs RFC Liege", "Zulte-Waregem vs Francs Borains", "Francs Borains vs KRC Genk B", "Lommel SK vs Francs Borains", "RFC Liege vs Francs Borains", "Francs Borains vs Lokeren-Temse", "RWDM vs Francs Borains", "Francs Borains vs Zulte-Waregem", "La Louviere vs Lokeren-Temse", "RWDM vs La Louviere", "La Louviere vs RFC Liege", "Zulte-Waregem vs La Louviere", "La Louviere vs Lommel SK", "La Louviere vs KRC Genk B", "La Louviere vs RWDM", "La Louviere vs Zulte-Waregem", "Lokeren-Temse vs La Louviere", "RFC Liege vs La Louviere", "KRC Genk B vs La Louviere", "Lommel SK vs La Louviere", "Zulte-Waregem vs Lierse K.", "RWDM vs Lierse K.", "Lommel SK vs Lierse K.", "Lierse K. vs Lokeren-Temse", "Lierse K. vs KRC Genk B", "Lierse K. vs RFC Liege", "Lierse K. vs Lommel SK", "Lokeren-Temse vs Lierse K.", "Lierse K. vs RWDM", "Lierse K. vs Zulte-Waregem", "RFC Liege vs Lierse K.", "KRC Genk B vs Lierse K.", "RWDM vs Patro <PERSON>", "Lokeren-<PERSON>mse vs Patro <PERSON>", "<PERSON><PERSON> vs Zulte-Waregem", "<PERSON><PERSON> vs RFC Liege", "<PERSON><PERSON> vs Lommel SK", "KRC Genk B vs Patro <PERSON>", "Zulte-Waregem vs Patro <PERSON>", "<PERSON><PERSON> vs Lokeren-Temse", "<PERSON><PERSON> vs KRC Genk B", "Lommel SK vs Patro <PERSON>den", "RFC Liege vs Patro <PERSON>", "<PERSON><PERSON> vs RWDM", "Seraing vs KRC Genk B", "Seraing vs Lommel SK", "Seraing vs RFC Liege", "Lokeren-<PERSON>ms<PERSON> vs Seraing", "Zulte-Waregem vs Seraing", "Seraing vs RWDM", "KRC Genk B vs Seraing", "RFC Liege vs Seraing", "Lommel SK vs Seraing", "RWDM vs Seraing", "Seraing vs Zulte-Waregem", "Seraing vs Lokeren-Temse"], "teams_affected": ["RFC Liege", "Zulte-Waregem", "Anderlecht B", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Francs Bo<PERSON>s", "RWDM", "Lokeren-Temse", "KRC Genk B", "La Louviere", "<PERSON><PERSON>", "Club Brugge B", "Lommel SK", "<PERSON><PERSON> K.", "Eupen"], "total_matches": 252, "valid_matches": 144, "skipped_matches": 108}, "GERMANY_REGIONALLIGA_SUDWEST": {"warning_count": 110, "missing_matches": ["<PERSON><PERSON><PERSON> vs Goppinger SV", "FSV Frankfurt vs Bahlinger", "<PERSON><PERSON><PERSON> vs Fulda-Lehnerz", "FSV Mainz B vs Bahlinger", "Goppinger SV vs Bahlinger", "<PERSON><PERSON><PERSON> vs FSV Frankfurt", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "Bahlinger vs FSV Mainz B", "Eintracht F. B vs FSV Mainz B", "Eintracht F. B vs Goppinger SV", "FSV Frankfurt vs Eintracht F. B", "Fulda-Lehnerz vs Eintracht F. B", "FSV Mainz B vs Eintracht F. B", "Goppinger SV vs Eintracht F. B", "Eintracht F. B vs FSV Frankfurt", "Eintracht F. B vs Fulda-Lehnerz", "Eintracht Trier vs Goppinger SV", "FSV Frankfurt vs Eintracht Trier", "Fulda-Lehnerz vs Eintracht Trier", "FSV Mainz B vs Eintracht Trier", "Goppinger SV vs Eintracht Trier", "Eintracht Trier vs FSV Frankfurt", "Eintracht Trier vs Fulda-Lehnerz", "Eintracht Trier vs FSV Mainz B", "Freiberg vs Goppinger SV", "FSV Frankfurt vs Freiberg", "Freiberg vs Fulda-Lehnerz", "FSV Mainz B vs Freiberg", "Goppinger SV vs Freiberg", "Freiberg vs FSV Frankfurt", "Fulda-Lehnerz vs Freiberg", "Freiberg vs FSV Mainz B", "Freiburg B vs FSV Frankfurt", "Freiburg B vs Fulda-Lehnerz", "Goppinger SV vs Freiburg B", "FSV Mainz B vs Freiburg B", "FSV Frankfurt vs Freiburg B", "Fulda-Lehnerz vs Freiburg B", "Freiburg B vs Goppinger SV", "<PERSON><PERSON><PERSON> vs Fulda-Lehnerz", "FSV Mainz B vs Giessen", "Goppinger SV vs Giessen", "Giessen vs FSV Frankfurt", "Fulda-Lehnerz vs <PERSON><PERSON>sen", "Giessen vs FSV Mainz B", "<PERSON><PERSON><PERSON> vs Goppinger SV", "FSV Frankfurt vs Giessen", "FSV Mainz B vs Hessen Kassel", "Goppinger SV vs Hessen Kassel", "Hessen Kassel vs FSV Frankfurt", "Hessen Kassel vs Fulda-Lehnerz", "Hessen Kassel vs FSV Mainz B", "Hessen Kassel vs Goppinger SV", "FSV Frankfurt vs Hessen Kassel", "Fulda-Lehnerz vs Hessen Kassel", "Goppinger SV vs Hoffenheim B", "Hoffenheim B vs FSV Frankfurt", "Fulda-Lehnerz vs Hoffenheim B", "Hoffenheim B vs FSV Mainz B", "Hoffenheim B vs Goppinger SV", "FSV Frankfurt vs Hoffenheim B", "Hoffenheim B vs Fulda-Lehnerz", "FSV Mainz B vs Hoffenheim B", "FSV Mainz B vs Homburg", "Goppinger SV vs Homburg", "Homburg vs FSV Frankfurt", "Homburg vs Fulda-Lehnerz", "Homburg vs FSV Mainz B", "Homburg vs Goppinger SV", "FSV Frankfurt vs Homburg", "Kickers Offenb. vs FSV Frankfurt", "Kickers Offenb. vs <PERSON><PERSON><PERSON>Lehnerz", "FSV Mainz B vs Kickers Offenb.", "Kickers Offenb. vs Goppinger SV", "FSV Frankfurt vs Kickers Offenb.", "Fulda-Lehnerz vs Kickers Offenb.", "Kickers Offenb. vs FSV Mainz B", "Goppinger SV vs Kickers Offenb.", "Goppinger SV vs S. Kickers", "S. Kickers vs FSV Frankfurt", "S. Kickers vs Fulda-Lehnerz", "S. Kickers vs FSV Mainz B", "S. Kickers vs Goppinger SV", "FSV Frankfurt vs S. Kickers", "<PERSON>lda<PERSON><PERSON><PERSON><PERSON><PERSON> vs S. Kickers", "FSV Mainz B vs S. Kickers", "FSV Frankfurt vs Steinbach", "Fulda-Lehnerz vs Steinbach", "Steinbach vs FSV Mainz B", "Goppinger SV vs Steinbach", "Steinbach vs FSV Frankfurt", "Steinbach vs Fulda-Lehnerz", "FSV Mainz B vs Steinbach", "Steinbach vs Goppinger SV", "Fulda-Lehnerz vs Villingen", "Villingen vs FSV Mainz B", "Villingen vs Goppinger SV", "FSV Frankfurt vs Villingen", "Villingen vs Fulda-Lehnerz", "FSV Mainz B vs Villingen", "Goppinger SV vs Villingen", "Villingen vs FSV Frankfurt", "Fulda-Lehnerz vs Walldorf", "Walldorf vs FSV Mainz B", "Walldorf vs Goppinger SV", "FSV Frankfurt vs Walldorf", "Walldorf vs Fulda-Lehnerz", "FSV Mainz B vs Walldorf", "Goppinger SV vs Walldorf", "Walldorf vs FSV Frankfurt"], "teams_affected": ["FSV Frankfurt", "<PERSON><PERSON><PERSON>", "Eintracht F. B", "Villingen", "Kickers Offenb.", "S. Kickers", "Walldorf", "Homburg", "Steinbach", "Eintracht Trier", "<PERSON><PERSON><PERSON>", "Hoffenheim B", "<PERSON><PERSON><PERSON>", "Freiburg B", "Goppinger SV", "Fulda-Lehnerz", "FSV Mainz B", "Hessen Kassel"], "total_matches": 462, "valid_matches": 352, "skipped_matches": 110}, "URUGUAY_SEGUNDA_DIVISION": {"warning_count": 7, "missing_matches": ["<PERSON><PERSON> vs Racing CM", "Racing CM vs Defensor S.", "Racing CM vs Fenix", "Penarol vs Racing CM", "Progreso vs Racing CM", "River Plate vs Racing CM", "Racing CM vs Wanderers"], "teams_affected": ["<PERSON><PERSON>", "Fenix", "Wanderers", "Defensor S.", "Progreso", "Racing CM", "River Plate", "Penarol"], "total_matches": 105, "valid_matches": 98, "skipped_matches": 7}, "IRELAND_PREMIER_DIVISION": {"warning_count": 2, "missing_matches": ["Cork City vs Bohemians", "Bohemians vs Cork City"], "teams_affected": ["Bohemians", "Cork City"], "total_matches": 13, "valid_matches": 11, "skipped_matches": 2}, "BELGIUM_U21_PRO_LEAGUE": {"warning_count": 53, "missing_matches": ["Union SG U21 vs Kortrijk U21", "Kortrijk U21 vs RFC Liege U21", "Waasland-B. U21 vs Kortrijk U21", "Kortrijk U21 vs RAAL LL U21", "Kortrijk U21 vs Lokeren-T. U21", "Kortrijk U21 vs RWDM U21", "RFC Liege U21 vs Kortrijk U21", "Kortrijk U21 vs Union SG U21", "Kortrijk U21 vs Waasland-B. U21", "RAAL LL U21 vs Kortrijk U21", "Kortrijk U21 vs RFC Liege U21", "Kortrijk U21 vs Union SG U21", "Kortrijk U21 vs RAAL LL U21", "Waasland-B. U21 vs Kortrijk U21", "Kortrijk U21 vs Lokeren-T. U21", "Kortrijk U21 vs RWDM U21", "Lokeren-T. U21 vs Kortrijk U21", "Lierse K. U21 vs Union SG U21", "RFC Liege U21 vs Lierse K. U21", "Waasland-B. U21 vs Lierse K. U21", "Lokeren-T. U21 vs Lierse K. U21", "RWDM U21 vs Lierse K. U21", "RAAL LL U21 vs Lierse K. U21", "Union SG U21 vs Lierse K. U21", "Lierse K. U21 vs RFC Liege U21", "Lierse K. U21 vs Waasland-B. U21", "Lierse K. U21 vs Lokeren-T. U21", "Lierse K. U21 vs RWDM U21", "Lierse K. U21 vs RAAL LL U21", "RFC Liege U21 vs Lierse K. U21", "Waasland-B. U21 vs Lierse K. U21", "Lokeren-T. U21 vs Lierse K. U21", "RWDM U21 vs Lierse K. U21", "Lierse K. U21 vs Union SG U21", "RAAL LL U21 vs Lierse K. U21", "RWDM U21 vs Westerlo U21", "Westerlo U21 vs RAAL LL U21", "Westerlo U21 vs RFC Liege U21", "Union SG U21 vs Westerlo U21", "Westerlo U21 vs Waasland-B. U21", "Lokeren-T. U21 vs Westerlo U21", "Westerlo U21 vs RWDM U21", "RAAL LL U21 vs Westerlo U21", "RFC Liege U21 vs Westerlo U21", "Westerlo U21 vs Union SG U21", "Waasland-B. U21 vs Westerlo U21", "Westerlo U21 vs Lokeren-T. U21", "RWDM U21 vs Westerlo U21", "Westerlo U21 vs RAAL LL U21", "Westerlo U21 vs RFC Liege U21", "Westerlo U21 vs Waasland-B. U21", "Lokeren-T. U21 vs Westerlo U21", "Union SG U21 vs Westerlo U21"], "teams_affected": ["Westerlo U21", "Kortrijk U21", "RFC Liege U21", "Union SG U21", "Lokeren-T. U21", "Lierse K. U21", "RAAL LL U21", "RWDM U21", "Waasland-B. U21"], "total_matches": 71, "valid_matches": 18, "skipped_matches": 53}, "ISRAEL_LIGA_ALEF_NORTH": {"warning_count": 58, "missing_matches": ["Kiryat Yam SC vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON><PERSON>k", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON>", "<PERSON><PERSON> vs <PERSON> Tira", "<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> vs MS Tira", "Kiryat Yam SC vs H. Bnei Mu<PERSON>", "<PERSON><PERSON><PERSON> vs H. Bnei <PERSON>", "<PERSON><PERSON> vs H. Bne<PERSON>", "H. Bnei Zalfa vs MS Tira", "Kiryat Yam SC vs H. Bnei Zalfa", "H. Bnei Zalfa vs Migdal HaEmek", "<PERSON>i Araba vs H. Bnei Zalfa", "MS Tira vs H. Bnei Zalfa", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON> Tira vs <PERSON><PERSON> <PERSON><PERSON><PERSON>", "Kiryat Yam SC vs <PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON><PERSON> HaEmek", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON><PERSON>", "Hapoel Bueine vs Kiryat Yam SC", "Migdal HaEmek vs Hapoel Bueine", "Hapoel Bueine vs Ironi Araba", "MS Tira vs Hapoel Bueine", "MS Tira vs Ironi Baka", "<PERSON><PERSON><PERSON> vs Ironi Baka", "Ironi Baka vs Kiryat Yam SC", "<PERSON><PERSON> Baka vs <PERSON><PERSON> Araba", "<PERSON><PERSON> vs MS Tira", "Kiryat Yam SC vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON><PERSON> Ha<PERSON>k", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON> Tira", "<PERSON><PERSON> vs Kiryat Yam SC", "<PERSON><PERSON> vs Kiryat Yam SC", "<PERSON><PERSON><PERSON> vs K. Ata Bialik", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON> Tira vs K. Ata Bialik", "<PERSON><PERSON> <PERSON><PERSON> vs Kiryat Yam SC", "<PERSON><PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON> Tira vs <PERSON><PERSON> <PERSON><PERSON>", "Kiryat Yam SC vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "<PERSON><PERSON> vs Kiryat Yam SC", "<PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs MS Tira", "Kiryat Yam SC vs <PERSON><PERSON>", "<PERSON><PERSON> vs Migdal HaEmek", "Kiryat Yam SC vs <PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON><PERSON> HaEmek", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON> Tira", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> El Fahm vs MS Tira", "<PERSON><PERSON> <PERSON><PERSON> El Fahm vs Kiryat Yam SC", "<PERSON><PERSON><PERSON> vs T<PERSON> <PERSON><PERSON> El Fahm", "<PERSON><PERSON> <PERSON><PERSON> Fahm vs <PERSON><PERSON> Araba"], "teams_affected": ["Hapoel Bueine", "<PERSON><PERSON>", "H. Bnei Zalfa", "MS Tira", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>ik", "<PERSON><PERSON>", "<PERSON><PERSON>", "Kiryat Yam SC", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> El Fahm", "<PERSON><PERSON> <PERSON><PERSON>"], "total_matches": 232, "valid_matches": 174, "skipped_matches": 58}, "HUNGARY_NB_I": {"warning_count": 73, "missing_matches": ["Kecskemeti TE vs Debrecen", "Debrecen vs DVTK", "Debrecen vs MTK Budapest", "Debrecen vs Kecskemeti TE", "DVTK vs Debrecen", "MTK Budapest vs Debrecen", "Kecskemeti TE vs Debrecen", "Debrecen vs DVTK", "Kecskemeti TE vs Fehervar", "Fe<PERSON>var vs DVTK", "<PERSON><PERSON>var vs MTK Budapest", "<PERSON><PERSON><PERSON> vs Kecskemeti TE", "DVTK vs Fehervar", "MTK Budapest vs Fehervar", "Kecskemeti TE vs Fehervar", "Fe<PERSON>var vs DVTK", "<PERSON><PERSON>var vs MTK Budapest", "Ferencvaros vs Kecskemeti TE", "DVTK vs Ferencvaros", "MTK Budapest vs Ferencvaros", "Kecskemeti TE vs Ferencvaros", "Ferencvaros vs DVTK", "Ferencvaros vs MTK Budapest", "Ferencvaros vs Kecskemeti TE", "DVTK vs Ferencvaros", "MTK Budapest vs Ferencvaros", "Gyor vs MTK Budapest", "Kecskemeti TE vs Gyor", "DVTK vs Gyor", "MTK Budapest vs Gyor", "G<PERSON>r vs Kecskemeti TE", "Gyor vs DVTK", "Gyor vs MTK Budapest", "Kecskemeti TE vs Gyor", "DVTK vs Gyor", "MTK Budapest vs Nyiregyhaza", "Kecskemeti TE vs Nyiregyhaza", "Nyiregyhaza vs DVTK", "Nyiregyhaza vs MTK Budapest", "Nyiregyhaza vs Kecskemeti TE", "DVTK vs Nyiregyhaza", "MTK Budapest vs Nyiregyhaza", "DVTK vs Paks", "Paks vs Kecskemeti TE", "Paks vs DVTK", "MTK Budapest vs Paks", "Paks vs MTK Budapest", "Kecskemeti TE vs Paks", "DVTK vs Paks", "MTK Budapest vs Paks", "Puskas A. vs MTK Budapest", "Kecskemeti TE vs Puskas A.", "Puskas A. vs DVTK", "MTK Budapest vs Puskas A.", "Puskas A. vs Kecskemeti TE", "DVTK vs Puskas A.", "Puskas A. vs MTK Budapest", "Kecskemeti TE vs Ujpest", "Ujpest vs DVTK", "MTK Budapest vs Ujpest", "Ujpest vs Kecskemeti TE", "DVTK vs Ujpest", "Ujpest vs MTK Budapest", "Kecskemeti TE vs Ujpest", "Ujpest vs DVTK", "Zalaegerszegi vs MTK Budapest", "Zalaegerszegi vs Kecskemeti TE", "DVTK vs Zalaegerszegi", "MTK Budapest vs Zalaegerszegi", "Kecskemeti TE vs Zalaegerszegi", "Zalaegerszegi vs DVTK", "Zalaegerszegi vs MTK Budapest", "Zalaegerszegi vs Kecskemeti TE"], "teams_affected": ["MTK Budapest", "Debrecen", "<PERSON><PERSON><PERSON>", "<PERSON>s", "Kecskemeti TE", "Nyiregyhaza", "Ujpest", "Zalaegerszegi", "Puskas A.", "DVTK", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "G<PERSON>r"], "total_matches": 261, "valid_matches": 188, "skipped_matches": 73}, "GEORGIA_EROVNULI_LIGA": {"warning_count": 4, "missing_matches": ["<PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON><PERSON> vs Gagra", "<PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON>"], "teams_affected": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Gagra", "<PERSON><PERSON><PERSON>"], "total_matches": 41, "valid_matches": 37, "skipped_matches": 4}, "BRAZIL_TOCANTINENSE": {"warning_count": 1, "missing_matches": ["<PERSON><PERSON><PERSON> vs Ava<PERSON>"], "teams_affected": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "total_matches": 5, "valid_matches": 4, "skipped_matches": 1}, "ITALY_SERIE_A": {"warning_count": 65, "missing_matches": ["AS Roma vs Atalanta", "Atalanta vs AC Milan", "AC Milan vs Atalanta", "AS Roma vs Bologna", "Bologna vs AS Roma", "Bologna vs AC Milan", "Cagliari vs AS Roma", "Cagliari vs AC Milan", "AC Milan vs Cagliari", "AS Roma vs Cagliari", "Como vs AS Roma", "Como vs AC Milan", "AS Roma vs Como", "AC Milan vs Como", "AS Roma vs Empoli", "AC Milan vs Empoli", "Empoli vs AC Milan", "Empoli vs AS Roma", "Fiorentina vs AC Milan", "Fiorentina vs AS Roma", "AC Milan vs Fiorentina", "Genoa vs AS Roma", "AC Milan vs Genoa", "AS Roma vs Genoa", "Hellas Verona vs AS Roma", "Hellas Verona vs AC Milan", "AC Milan vs Hellas Verona", "AS Roma vs Hellas Verona", "Inter Milan vs AC Milan", "AS Roma vs Inter Milan", "AC Milan vs Inter Milan", "Juventus vs AS Roma", "AC Milan vs Juventus", "Juventus vs AC Milan", "AS Roma vs Juventus", "Lazio vs AC Milan", "AS Roma vs Lazio", "AC Milan vs Lazio", "Lazio vs AS Roma", "AC Milan vs Lecce", "AS Roma vs Lecce", "Lecce vs AC Milan", "Lecce vs AS Roma", "Monza vs AS Roma", "Monza vs AC Milan", "AS Roma vs Monza", "AC Milan vs Napoli", "Napoli vs AS Roma", "AS Roma vs Napoli", "Napoli vs AC Milan", "Parma vs AC Milan", "AS Roma vs Parma", "AC Milan vs Parma", "Parma vs AS Roma", "AC Milan vs Torino", "AS Roma vs Torino", "Torino vs AC Milan", "AS Roma vs Udinese", "AC Milan vs Udinese", "Udinese vs AS Roma", "Udinese vs AC Milan", "AC Milan vs Venezia", "AS Roma vs Venezia", "Venezia vs AS Roma", "Venezia vs AC Milan"], "teams_affected": ["Udinese", "Monza", "Atalanta", "Genoa", "Cagliari", "Napoli", "Juventus", "Torino", "Lecce", "AC Milan", "Como", "Fiorentina", "Venezia", "Hellas Verona", "AS Roma", "Empoli", "Parma", "Inter Milan", "Lazio", "Bologna"], "total_matches": 597, "valid_matches": 532, "skipped_matches": 65}, "SPAIN_PRIMERA_RFEF_GROUP_1": {"warning_count": 92, "missing_matches": ["Amorebieta vs Ourense CF", "Amorebieta vs FC Barcelona B", "FC Andorra vs Amorebieta", "Amorebieta vs FC Andorra", "FC Barcelona B vs Amorebieta", "Ourense CF vs Arenteiro", "Arenteiro vs FC Barcelona B", "Arenteiro vs FC Andorra", "FC Barcelona B vs Arenteiro", "FC Andorra vs Arenteiro", "Arenteiro vs Ourense CF", "Athletic B. B vs FC Andorra", "Athletic B. B vs Ourense CF", "FC Barcelona B vs Athletic B. B", "FC Andorra vs Athletic B. B", "Athletic B. B vs FC Barcelona B", "Ourense CF vs Athletic B. B", "Ourense CF vs Barakaldo", "FC Barcelona B vs Barakaldo", "FC Andorra vs Barakaldo", "Barakaldo vs Ourense CF", "Barakaldo vs FC Andorra", "Celta Vigo B vs Ourense CF", "FC Barcelona B vs Celta Vigo B", "Celta Vigo B vs FC Andorra", "Ourense CF vs Celta Vigo B", "FC Andorra vs Celta Vigo B", "Celta Vigo B vs FC Barcelona B", "Gimnastic T. vs Ourense CF", "Gimnastic T. vs FC Andorra", "FC Barcelona B vs Gimnastic T.", "Ourense CF vs Gimnastic T.", "Gimnastic T. vs FC Barcelona B", "Ourense CF vs Gimnastica", "Gimnastica vs FC Barcelona B", "Gimnastica vs FC Andorra", "FC Barcelona B vs Gimnastica", "Gimnastica vs Ourense CF", "FC Andorra vs Gimnastica", "Leonesa vs Ourense CF", "FC Andorra vs Leonesa", "Leonesa vs FC Barcelona B", "FC Barcelona B vs Leonesa", "Lugo vs FC Andorra", "FC Barcelona B vs Lugo", "Ourense CF vs Lugo", "FC Andorra vs Lugo", "Lugo vs Ourense CF", "Ourense CF vs Osasuna B", "FC Andorra vs Osasuna B", "Osasuna B vs FC Barcelona B", "FC Barcelona B vs Osasuna B", "Osasuna B vs FC Andorra", "FC Andorra vs Ponferradina", "Ponferradina vs FC Barcelona B", "Ourense CF vs Ponferradina", "FC Barcelona B vs Ponferradina", "Ponferradina vs FC Andorra", "Ponferradina vs Ourense CF", "FC Andorra vs Real Sociedad B", "FC Barcelona B vs Real Sociedad B", "Real Sociedad B vs Ourense CF", "Real Sociedad B vs FC Barcelona B", "Ourense CF vs Real Sociedad B", "Real Sociedad B vs FC Andorra", "FC Barcelona B vs Real Union", "FC Andorra vs Real Union", "Real Union vs Ourense CF", "Ourense CF vs Real Union", "Real Union vs FC Andorra", "Real Union vs FC Barcelona B", "Sestao River vs FC Andorra", "Sestao River vs FC Barcelona B", "Ourense CF vs Sestao River", "FC Barcelona B vs Sestao River", "Sestao River vs Ourense CF", "Tarazona vs FC Andorra", "Tarazona vs FC Barcelona B", "Ourense CF vs Tarazona", "FC Barcelona B vs Tarazona", "FC Andorra vs Tarazona", "Unionistas vs FC Barcelona B", "Ourense CF vs Unionistas", "FC Andorra vs Unionistas", "Unionistas vs Ourense CF", "Unionistas vs FC Andorra", "FC Barcelona B vs Zamora", "Zamora vs FC Andorra", "Zamora vs Ourense CF", "Zamora vs FC Barcelona B", "FC Andorra vs Zamora", "Ourense CF vs Zamora"], "teams_affected": ["Ourense CF", "<PERSON><PERSON><PERSON>", "Ponferradina", "Leonesa", "Baraka<PERSON>", "Gimnastica", "Real Sociedad B", "Unionistas", "FC Andorra", "Sestao River", "Athletic B. B", "Lugo", "Tarazona", "Gimnastic T.", "Osasuna B", "FC Barcelona B", "Amorebieta", "Celta Vigo B", "Real Union", "Zamora"], "total_matches": 572, "valid_matches": 480, "skipped_matches": 92}}