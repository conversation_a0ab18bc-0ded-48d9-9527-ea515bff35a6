{"SPAIN_SEGUNDA_RFEF_GROUP_5": {"warning_count": 26, "missing_matches": ["Colonia M. vs Atletico Paso", "Atletico Paso vs Colonia M.", "Colonia M. vs Cacereno", "Cacereno vs Colonia M.", "Colonia M. vs Conquense", "Conquense vs Colonia M.", "Colonia M. vs Getafe B", "Getafe B vs Colonia M.", "Colonia M. vs Guadalajara", "Guadalajara vs Colonia M.", "Illescas vs Colonia M.", "Colonia M. vs Illescas", "Melilla vs Colonia M.", "Colonia M. vs Melilla", "Navalcarnero vs Colonia M.", "Colonia M. vs Navalcarnero", "<PERSON><PERSON> vs Colonia M.", "Colonia M. vs <PERSON><PERSON>", "Real Madrid C vs Colonia M.", "Colonia M. vs Real Madrid C", "<PERSON><PERSON> vs Colonia M.", "Colonia M. vs <PERSON><PERSON>.", "Colonia M. vs Tenerife B", "Tenerife B vs Colonia M.", "Colonia M. vs Union Adarve", "Union Adarve vs Colonia M."], "teams_affected": ["Getafe B", "Real Madrid C", "Colonia M.", "Guadalajara", "Conquense", "Cacereno", "Navalcarnero", "<PERSON><PERSON>", "Union Adarve", "Melilla", "Atletico Paso", "Illescas", "Tenerife B", "<PERSON><PERSON>"], "total_matches": 442, "valid_matches": 416, "skipped_matches": 26}, "GERMANY_OBERLIGA_RHEINLAND_PFALZ_SAAR": {"warning_count": 29, "missing_matches": ["<PERSON><PERSON> vs Idar-Oberstein", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> vs Idar-Oberstein", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs Auersmacher", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs Diefflen", "<PERSON><PERSON><PERSON> vs Idar-Oberstein", "Idar-Oberstein vs Eisbachtal", "Eisbachtal vs Idar-Oberstein", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs Engers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs Eppelborn", "E<PERSON>born vs Idar-Oberstein", "Gonsenheim vs Idar-Oberstein", "Idar-Oberstein vs Gonsenheim", "Kaiserslaut. B vs Idar-Oberstein", "Idar-Oberstein vs Kaiserslaut. B", "Idar<PERSON><PERSON><PERSON><PERSON> vs Karbach", "<PERSON><PERSON><PERSON> vs Idar-Oberstein", "Idar<PERSON><PERSON><PERSON><PERSON> vs Koblenz", "Koblenz vs Idar-Oberstein", "Mechtersheim vs Idar-Oberstein", "Idar-Oberstein vs Mechtersheim", "Morlautern vs Idar-Oberstein", "Idar<PERSON>Oberstein vs Morlautern", "<PERSON><PERSON><PERSON><PERSON> vs Idar-Oberstein", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs Pirmasens", "<PERSON>r<PERSON><PERSON><PERSON><PERSON> vs Schott Mainz", "<PERSON><PERSON> vs Idar-Oberstein", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs Wormatia Worms", "Wormatia Worms vs Idar-Oberstein"], "teams_affected": ["Mechtersheim", "<PERSON><PERSON><PERSON>", "Kaiserslaut. B", "<PERSON><PERSON><PERSON>", "Wormatia Worms", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Engers", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Idar-<PERSON><PERSON>tein", "<PERSON><PERSON><PERSON><PERSON>", "Gonsenheim", "Morlautern", "Ko<PERSON>nz", "Eisbachtal"], "total_matches": 496, "valid_matches": 467, "skipped_matches": 29}, "BELARUS_VYSSHAYA_LIGA": {"warning_count": 3, "missing_matches": ["Maxline vs Arsenal D.", "Maxline vs Neman Grodno", "Slutsk vs Maxline"], "teams_affected": ["Slutsk", "Maxline", "Arsenal D.", "<PERSON><PERSON> Grodno"], "total_matches": 29, "valid_matches": 26, "skipped_matches": 3}, "GEORGIA_EROVNULI_LIGA_2": {"warning_count": 8, "missing_matches": ["Bolnisi vs Iberia B", "<PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "Gonio vs Spaeri", "<PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "Spaeri vs Samtredia", "Spaeri vs Iberia B", "Spaeri vs Gonio"], "teams_affected": ["Gonio", "Meshakh<PERSON>", "<PERSON><PERSON>", "Samtredia", "<PERSON><PERSON>", "Bo<PERSON><PERSON>", "Iberia B", "<PERSON><PERSON><PERSON>"], "total_matches": 15, "valid_matches": 7, "skipped_matches": 8}, "AUSTRALIA_WESTERN_AUSTRALIA_NPL": {"warning_count": 1, "missing_matches": ["Perth SC vs Fremantle City"], "teams_affected": ["Fremantle City", "Perth SC"], "total_matches": 19, "valid_matches": 18, "skipped_matches": 1}, "TAJIKISTAN_VYSSHAYA_LIGA": {"warning_count": 2, "missing_matches": ["Regar-TadAZ vs Istiqlol D.", "Vakhsh vs Eskhata"], "teams_affected": ["Istiqlol D.", "Vakhsh", "Regar-TadAZ", "Esk<PERSON><PERSON>"], "total_matches": 12, "valid_matches": 10, "skipped_matches": 2}, "ISRAEL_LIGA_ALEF": {"warning_count": 15, "missing_matches": ["<PERSON><PERSON> vs <PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs H. Bne<PERSON>", "<PERSON>i Araba vs H. Bnei Zalfa", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON><PERSON>", "Hapoel Bueine vs Ironi Araba", "<PERSON><PERSON> Baka vs <PERSON><PERSON> Araba", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> Fahm vs <PERSON><PERSON> Araba"], "teams_affected": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> El Fahm", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>ik", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Hapoel Bueine", "<PERSON><PERSON>", "H. Bnei Zalfa"], "total_matches": 232, "valid_matches": 217, "skipped_matches": 15}, "FINLAND_YKKONEN": {"warning_count": 2, "missing_matches": ["<PERSON><PERSON><PERSON><PERSON> vs Jazz Pori", "<PERSON><PERSON><PERSON><PERSON> vs Rovaniemi"], "teams_affected": ["<PERSON><PERSON><PERSON><PERSON>", "Jazz Pori", "Rovaniemi"], "total_matches": 7, "valid_matches": 5, "skipped_matches": 2}, "AUSTRALIA_VICTORIA_NPL": {"warning_count": 1, "missing_matches": ["Melbourne K. vs M. Victory B"], "teams_affected": ["Melbourne K.", "<PERSON>. <PERSON> B"], "total_matches": 21, "valid_matches": 20, "skipped_matches": 1}, "IRELAND_WOMENS_NATIONAL_LEAGUE": {"warning_count": 2, "missing_matches": ["Cork City vs Bohemians", "Bohemians vs Cork City"], "teams_affected": ["Cork City", "Bohemians"], "total_matches": 16, "valid_matches": 14, "skipped_matches": 2}, "PERU_PRIMERA_DIVISION": {"warning_count": 5, "missing_matches": ["Juan Pablo II C vs Alianza A.", "<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON> vs <PERSON><PERSON>", "Universitario vs <PERSON><PERSON>", "Binacional vs Universitario"], "teams_affected": ["<PERSON><PERSON>", "Binacional", "Juan Pablo II C", "<PERSON>", "Alianza A.", "Cienciano", "Universitario"], "total_matches": 21, "valid_matches": 16, "skipped_matches": 5}, "AUSTRALIA_QUEENSLAND_NPL": {"warning_count": 1, "missing_matches": ["Gold C. Knights vs St George W."], "teams_affected": ["Gold C. Knights", "St George W."], "total_matches": 10, "valid_matches": 9, "skipped_matches": 1}, "COSTA_RICA_LIGA_DE_ASCENSO": {"warning_count": 2, "missing_matches": ["Alajuelense vs Santa Ana", "Santa Ana vs Alajuelense"], "teams_affected": ["Santa Ana", "Alajuelense"], "total_matches": 40, "valid_matches": 38, "skipped_matches": 2}, "JAPAN_J3_LEAGUE": {"warning_count": 4, "missing_matches": ["Kagoshima Utd vs Kamatamare S.", "<PERSON><PERSON><PERSON> Y. vs Kochi United", "<PERSON><PERSON>moto Y. vs Tochigi City", "Nara Club vs Kusatsu"], "teams_affected": ["Tochigi City", "Kamatamare S.", "Kagoshima Utd", "<PERSON><PERSON><PERSON>.", "Kochi United", "<PERSON><PERSON><PERSON>", "Nara Club"], "total_matches": 17, "valid_matches": 13, "skipped_matches": 4}, "LATVIA_1_LIGA": {"warning_count": 3, "missing_matches": ["Marupe vs Augsdaugava", "Skanste vs Augsdaugava", "Riga Mariners vs Skanste"], "teams_affected": ["Skanste", "Augsdaugava", "Riga Mariners", "Marupe"], "total_matches": 8, "valid_matches": 5, "skipped_matches": 3}, "SOUTH_AFRICA_PREMIERSHIP": {"warning_count": 20, "missing_matches": ["Marumo G. vs Cape Town City", "Cape Town City vs Marumo G.", "Chippa Utd vs Marumo G.", "Golden Arrows vs Marumo G.", "<PERSON><PERSON><PERSON> G. vs Golden Arrows", "<PERSON><PERSON><PERSON> G. vs Kaizer Chiefs", "Kaizer Chiefs vs Marumo G.", "<PERSON><PERSON><PERSON> G. vs <PERSON><PERSON><PERSON>", "Mamelodi S. vs Marumo G.", "<PERSON><PERSON><PERSON> G. vs Mamelodi S.", "Orlando Pirates vs Marumo G.", "Marumo G. vs Orlando Pirates", "Polokwane vs Marumo G.", "<PERSON>umo G. vs Polokwane", "<PERSON><PERSON><PERSON> G. vs Richards Bay", "Richards Bay vs Marumo G.", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs Marumo G.", "<PERSON><PERSON><PERSON> G. vs <PERSON><PERSON><PERSON><PERSON><PERSON>", "Stellenbosch vs Marumo G.", "Marumo G. vs Stellenbosch"], "teams_affected": ["Marumo G.", "Mamelodi S.", "Golden Arrows", "Cape Town City", "Orlando Pirates", "<PERSON><PERSON><PERSON>", "Stellenbosch", "<PERSON>", "Kaizer Chiefs", "Chippa Utd", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Polokwane"], "total_matches": 286, "valid_matches": 266, "skipped_matches": 20}, "SWEDEN_ALLSVENSKAN_WOMEN": {"warning_count": 1, "missing_matches": ["Alingsas W vs Linkoping W"], "teams_affected": ["Alingsas W", "Linkoping W"], "total_matches": 9, "valid_matches": 8, "skipped_matches": 1}, "ITALY_SERIE_D_GROUP_D": {"warning_count": 32, "missing_matches": ["Corticella vs San Marino C.", "San Marino C. vs Corticella", "San Marino C. vs Fiorenzuola", "Fiorenzuola vs San Marino C.", "Forli vs San Marino C.", "San Marino C. vs Forli", "Imolese vs San Marino C.", "San Marino C. vs Imolese", "San Marino C. vs Lentigione", "Lentigione vs San Marino C.", "San Marino C. vs Piacenza", "Piacenza vs San Marino C.", "Pistoiese vs San Marino C.", "San Marino C. vs Pistoiese", "San Marino C. vs Prato", "Prato vs San Marino C.", "Progresso vs San Marino C.", "San Marino C. vs Progresso", "Ravenna vs San Marino C.", "San Marino C. vs Ravenna", "San Marino C. vs Sammaurese", "Sammaurese vs San Marino C.", "San Marino C. vs Sasso Marconi", "Sasso Marconi vs San Marino C.", "Tau Altopascio vs San Marino C.", "San Marino C. vs Tau Altopascio", "Tuttocuoio vs San Marino C.", "San Marino C. vs Tuttocuoio", "San Marino C. vs United Riccione", "United Riccione vs San Marino C.", "San Marino C. vs Zenith Prato", "Zenith Prato vs San Marino C."], "teams_affected": ["Corticella", "Prato", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Tau Altopascio", "<PERSON><PERSON>", "Piacenza", "<PERSON><PERSON>", "San Marino C.", "Lentigione", "United Riccione", "Imolese", "<PERSON><PERSON>", "Progresso", "<PERSON><PERSON>", "Pistoiese"], "total_matches": 542, "valid_matches": 510, "skipped_matches": 32}, "SWEDEN_ALLSVENSKAN": {"warning_count": 3, "missing_matches": ["Djurgarden vs Oster", "Halmstad vs Degerfors", "Mjallby vs Degerfors"], "teams_affected": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Djurgarden", "M<PERSON>ll<PERSON>"], "total_matches": 14, "valid_matches": 11, "skipped_matches": 3}, "SWEDEN_DIV_2_SODRA_SVEALAND": {"warning_count": 7, "missing_matches": ["Far<PERSON> vs Atvidaberg", "Forward vs S<PERSON><PERSON>ner", "Arameiska-S. vs Smedby", "<PERSON><PERSON><PERSON>-S<PERSON> vs <PERSON>", "Forward vs Sylvia", "Sylvia vs Eker Orebro", "<PERSON><PERSON> vs Sylvia"], "teams_affected": ["Arameiska-S.", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Farsta", "S<PERSON><PERSON>", "<PERSON>", "Forward", "<PERSON><PERSON><PERSON><PERSON>"], "total_matches": 12, "valid_matches": 5, "skipped_matches": 7}, "AUSTRALIA_SOUTH_AUSTRALIA_NPL": {"warning_count": 2, "missing_matches": ["West Torrens vs Campbelltown", "Croydon Kings vs Playford P."], "teams_affected": ["Campbelltown", "Croydon Kings", "Playford P.", "West Torrens"], "total_matches": 6, "valid_matches": 4, "skipped_matches": 2}, "JAPAN_NADESHIKO_LEAGUE_1": {"warning_count": 2, "missing_matches": ["<PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> Ku<PERSON>ichi W"], "teams_affected": ["Iga Kunoichi W", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "total_matches": 20, "valid_matches": 18, "skipped_matches": 2}, "FAROE_ISLANDS_PREMIER_LEAGUE": {"warning_count": 2, "missing_matches": ["TB Tvoroyri vs B68 Toftir", "Su<PERSON>roy vs Klaksvik"], "teams_affected": ["Klaksvik", "<PERSON><PERSON><PERSON>", "TB Tvoroyri", "B68 Toftir"], "total_matches": 6, "valid_matches": 4, "skipped_matches": 2}, "BELARUS_FIRST_LEAGUE": {"warning_count": 1, "missing_matches": ["<PERSON><PERSON><PERSON>vichy vs Bumprom"], "teams_affected": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "total_matches": 13, "valid_matches": 12, "skipped_matches": 1}, "JAPAN_J2_LEAGUE": {"warning_count": 4, "missing_matches": ["<PERSON><PERSON> vs C. Sa<PERSON>oro", "Omiya Ardija vs <PERSON>ita <PERSON>", "<PERSON><PERSON><PERSON> Iwata vs <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>"], "teams_affected": ["V-<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Omiya Ardija", "C. <PERSON>", "<PERSON><PERSON>"], "total_matches": 15, "valid_matches": 11, "skipped_matches": 4}, "BRAZIL_SERIE_B": {"warning_count": 1, "missing_matches": ["<PERSON><PERSON><PERSON> vs Ava<PERSON>"], "teams_affected": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "total_matches": 5, "valid_matches": 4, "skipped_matches": 1}, "TURKEY_3_LIG_GROUP_3": {"warning_count": 24, "missing_matches": ["Alanya 1221 vs 1923 M.", "1923 <PERSON><PERSON> vs Alanya 1221", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs 1923 M.", "1923 <PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> vs 1923 M.", "1923 <PERSON><PERSON> vs Bayburt", "1923 M. vs Corluspor", "Corluspor vs 1923 M.", "Efeler 09 vs 1923 M.", "1923 M. vs Efeler 09", "<PERSON><PERSON> vs 1923 M.", "1923 <PERSON><PERSON> vs <PERSON><PERSON>", "1923 M. vs Kucukcekmece", "Kucukcekmece vs 1923 M.", "Orduspor vs 1923 M.", "1923 M. vs Orduspor", "1923 M. vs Osmaniyespor", "Osmaniyespor vs 1923 M.", "1923 M. vs Pazarspor", "Pazarspor vs 1923 M.", "Viransehir vs 1923 M.", "1923 M. vs Viransehir", "1923 M. vs Yozgat Bld Bozo", "Yozgat Bld Bozo vs 1923 M."], "teams_affected": ["Yozgat Bld Bozo", "1923 M.", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Efeler 09", "Bayburt", "Osmaniyespor", "Viransehir", "Orduspor", "Kucukcekmece", "Pazarspor", "Corluspor", "Alanya 1221"], "total_matches": 360, "valid_matches": 336, "skipped_matches": 24}, "FRANCE_NATIONAL_2_GROUP_A": {"warning_count": 24, "missing_matches": ["Saint-<PERSON> vs Andrezieux", "Andrezieux vs Saint-Priest", "Anglet Genets vs Saint-Priest", "Saint<PERSON><PERSON> vs Anglet Genets", "Angouleme vs Saint-Priest", "<PERSON><PERSON> vs Saint-Priest", "Saint<PERSON><PERSON> vs Bergerac", "Cannes vs Saint-Priest", "Saint-Priest vs Cannes", "G. O. A. L. vs Saint-Priest", "Saint-<PERSON> vs G. O. A. L.", "Saint<PERSON><PERSON> vs Grasse", "Grasse vs Saint-Priest", "Saint<PERSON><PERSON> vs Hyer<PERSON>", "Saint-<PERSON> vs Istres", "Istres vs Saint-Priest", "Jura Sud Foot vs Saint-Priest", "<PERSON><PERSON><PERSON> vs Jura Sud Foot", "<PERSON><PERSON><PERSON> vs <PERSON> Puy", "<PERSON> P<PERSON> vs Saint-Priest", "Saint-<PERSON> vs Marignane G.", "<PERSON><PERSON><PERSON> G. vs Saint-<PERSON>", "Saint-<PERSON> vs Toulon", "Toulon vs Saint-Priest"], "teams_affected": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Anglet Genets", "Toulon", "Grasse", "<PERSON><PERSON><PERSON>", "Marignane G.", "Cannes", "<PERSON>-<PERSON>", "G. O. A. L.", "<PERSON><PERSON>", "Istres", "Jura Sud Foot"], "total_matches": 351, "valid_matches": 327, "skipped_matches": 24}, "GERMANY_OBERLIGA_BREMEN": {"warning_count": 39, "missing_matches": ["<PERSON><PERSON><PERSON><PERSON> vs Aumund-Vegesack", "<PERSON><PERSON><PERSON><PERSON><PERSON>diek vs Blument<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> vs Blumenthaler", "<PERSON><PERSON><PERSON><PERSON> vs Aumund-Vegesack", "<PERSON><PERSON>r<PERSON>Blockdiek vs Brinkumer", "<PERSON><PERSON> vs Aumund-Vegesack", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs Vahr-Blockdiek", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "Gee<PERSON><PERSON>e vs Aumund-Vegesack", "<PERSON><PERSON>r<PERSON>Blockdiek vs Geestemunde", "<PERSON><PERSON><PERSON><PERSON> vs Geestemunde", "<PERSON><PERSON><PERSON><PERSON> vs Vahr-Blockdiek", "<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> vs Vahr-Blockdiek", "<PERSON><PERSON><PERSON><PERSON> vs Aumund-Vegesack", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON>", "Aumund-Vegesack vs Hemelingen", "Hemelingen vs Vahr-Blockdiek", "Hemelingen vs Aumund-Vegesack", "Vahr-Blockdiek vs Hemelingen", "<PERSON><PERSON><PERSON> vs Oberneuland", "Vahr<PERSON>Blockdiek vs Oberneuland", "Oberneuland vs Aumund-Vegesack", "Oberneuland vs Vahr-Blockdiek", "Union Bremen vs Aumund-Vegesack", "Union Bremen vs Vahr-Blockdiek", "Aumund-Vegesack vs Union Bremen", "Vahr-Blockdiek vs Union Bremen", "Aumund-Vegesack vs Vatan Sport", "Vatan Sport vs Vahr-Blockdiek", "Vatan Sport vs Aumund-Vegesack", "Vahr-Blockdiek vs Vatan Sport", "Werder Bremen C vs Aumund-Vegesack", "Vahr-Blockdiek vs Werder Bremen C", "Aumund-Vegesack vs Werder Bremen C", "Werder Bremen C vs Vahr-Blockdiek", "Aumund-Vegesack vs Woltmershausen", "Woltmershausen vs Vahr-Blockdiek"], "teams_affected": ["<PERSON><PERSON><PERSON><PERSON>", "Oberneuland", "Vatan Sport", "Vahr-Blockdiek", "Union Bremen", "<PERSON><PERSON><PERSON><PERSON>", "Werder Bremen C", "<PERSON><PERSON><PERSON><PERSON>", "Aumund-<PERSON>", "<PERSON><PERSON>", "Woltmershausen", "Hemelingen", "<PERSON><PERSON><PERSON><PERSON>"], "total_matches": 296, "valid_matches": 257, "skipped_matches": 39}, "URUGUAY_PRIMERA_DIVISION": {"warning_count": 5, "missing_matches": ["Boston River vs Montevideo City", "Plaza Colonia vs Boston River", "Juventud vs Boston River", "<PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "Plaza Colonia vs Wanderers"], "teams_affected": ["<PERSON><PERSON><PERSON>", "Montevideo City", "Wanderers", "Plaza Colonia", "<PERSON><PERSON><PERSON>", "Boston River"], "total_matches": 25, "valid_matches": 20, "skipped_matches": 5}, "ESTONIA_MEISTRILIIGA": {"warning_count": 2, "missing_matches": ["Harju JK vs Narva Trans", "Nomme Kalju vs Harju JK"], "teams_affected": ["Nomme Kalju", "<PERSON><PERSON>ju <PERSON>", "Narva Trans"], "total_matches": 27, "valid_matches": 25, "skipped_matches": 2}, "GERMANY_OBERLIGA_BAYERN_SUD": {"warning_count": 31, "missing_matches": ["1860 Munchen B vs Rain/Lech", "Rain/Lech vs Deisenhofen", "Deisenhofen vs Rain/Lech", "Rain/Lech vs Erlbach", "Erlbach vs Rain/Lech", "<PERSON><PERSON><PERSON> vs Rain/Lech", "Rain/Lech vs Grun<PERSON>", "Heimstetten vs Rain/Lech", "Rain/Lech vs Heimstetten", "Rain/Lech vs Ismaning", "Ismaning vs Rain/Lech", "Kirchanschoring vs Rain/Lech", "Rain/Lech vs Kirchanschoring", "<PERSON><PERSON><PERSON> vs Rain/Lech", "<PERSON>/<PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "Landsberg vs Rain/Lech", "Rain/Lech vs Landsberg", "Rain/Lech vs Memmingen", "Memmingen vs Rain/Lech", "Nordlingen vs Rain/Lech", "Rain/Lech vs Nordlingen", "Pipinsried vs Rain/Lech", "Rain/Le<PERSON> vs Pi<PERSON>sried", "Rain/Lech vs Schalding", "Schalding vs Rain/Lech", "Sonthofen vs Rain/Lech", "Rain/Lech vs Sonthofen", "T. Augsburg vs Rain/Lech", "<PERSON>/<PERSON> vs T. Augsburg", "Rain/Lech vs Unterhaching B", "Unterhaching B vs Rain/Lech"], "teams_affected": ["Deisenhofen", "Rain/Lech", "<PERSON><PERSON><PERSON>", "Landsberg", "Memmingen", "Pipinsried", "1860 Munchen B", "Heimstetten", "Ismaning", "<PERSON><PERSON><PERSON>", "Nordlingen", "Schalding", "Sonthofen", "T. <PERSON>", "Erlbach", "Unterhaching B", "Kirchanschoring"], "total_matches": 497, "valid_matches": 466, "skipped_matches": 31}, "GERMANY_OBERLIGA_SCHLESWIG_HOLSTEIN": {"warning_count": 43, "missing_matches": ["<PERSON><PERSON> vs <PERSON><PERSON>", "PSV Neumunster vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs PSV Neumunster", "<PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON><PERSON>", "PSV Neumunster vs Eckernforder", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "Eichede vs PSV Neumunster", "<PERSON><PERSON> vs E<PERSON><PERSON>", "PSV Neumunster vs Eichede", "Flensburg vs PSV Neumunster", "Flensburg vs <PERSON><PERSON>", "PSV Neumunster vs Flensburg", "<PERSON><PERSON> vs Flensburg", "PSV Neumunster vs Hohenwestedt", "<PERSON>henwestedt vs <PERSON><PERSON>", "Hohenwestedt vs PSV Neumunster", "<PERSON><PERSON> vs Hohenwestedt", "<PERSON><PERSON> vs Kilia Kiel", "PSV Neumunster vs Kilia Kiel", "<PERSON><PERSON> vs <PERSON><PERSON>", "Kilia Kiel vs PSV Neumunster", "<PERSON><PERSON> vs Lubeck B", "Lubeck B vs PSV Neumunster", "Lubeck B vs <PERSON><PERSON>", "PSV Neumunster vs Lubeck B", "Nordmark Satrup vs <PERSON><PERSON>", "Nordmark Satrup vs PSV Neumunster", "<PERSON><PERSON> vs Nordmark Satrup", "PSV Neumunster vs Nordmark Satrup", "Oldenburger vs PSV Neumunster", "Oldenburger vs <PERSON><PERSON>", "PSV Neumunster vs Oldenburger", "<PERSON><PERSON> vs Oldenburger", "<PERSON><PERSON> vs <PERSON><PERSON>", "PSV Neumunster vs P. <PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs PSV Neumunster", "<PERSON><PERSON> vs Rotenhof", "PSV Neumunster vs Rotenhof", "<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "Rotenhof vs PSV Neumunster"], "teams_affected": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "PSV Neumunster", "Rotenhof", "Hohenwestedt", "Lubeck B", "<PERSON><PERSON>", "<PERSON><PERSON>", "Nordmark Satrup", "<PERSON><PERSON><PERSON>", "Oldenburger", "Flensburg"], "total_matches": 319, "valid_matches": 276, "skipped_matches": 43}, "SPAIN_PRIMERA_F_WOMEN": {"warning_count": 22, "missing_matches": ["Atletico B W vs Alaves W", "Alaves W vs Atletico B W", "Albacete W vs Atletico B W", "Atletico B W vs Albacete W", "Alhama W vs Atletico B W", "Atletico B W vs Alhama W", "Atletico B W vs Baleares W", "Baleares W vs Atletico B W", "Atletico B W vs Barcelona B W", "Barcelona B W vs Atletico B W", "Caceres W vs Atletico B W", "Atletico B W vs Caceres W", "Getafe W vs Atletico B W", "Atletico B W vs Getafe W", "Atletico B W vs Huelva W", "Huelva W vs Atletico B W", "Osasuna W vs Atletico B W", "Atletico B W vs Osasuna W", "Atletico B W vs Real Madrid B W", "Real Madrid B W vs Atletico B W", "Atletico B W vs Villarreal W", "Villarreal W vs Atletico B W"], "teams_affected": ["Caceres W", "Villarreal W", "<PERSON><PERSON><PERSON> W", "Barcelona B W", "<PERSON><PERSON><PERSON><PERSON>", "Real Madrid B W", "Alhama W", "Atletico B W", "Albacete W", "Balea<PERSON> W", "Alaves W", "Getafe W"], "total_matches": 286, "valid_matches": 264, "skipped_matches": 22}, "KAZAKHSTAN_PREMIER_LEAGUE": {"warning_count": 2, "missing_matches": ["Tobol vs Ulytau", "Okzhetpes vs Tobol"], "teams_affected": ["<PERSON>ly<PERSON><PERSON>", "Tobol", "Okzhetpes"], "total_matches": 14, "valid_matches": 12, "skipped_matches": 2}, "IRELAND_FIRST_DIVISION": {"warning_count": 3, "missing_matches": ["Dundalk vs Athlone Town", "Dundalk vs Longford Town", "Longford Town vs Dundalk"], "teams_affected": ["Athlone Town", "Dundalk", "Longford Town"], "total_matches": 22, "valid_matches": 19, "skipped_matches": 3}, "BAHRAIN_PREMIER_LEAGUE": {"warning_count": 32, "missing_matches": ["<PERSON><PERSON><PERSON> vs Al Ahli M.", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs Al Ahli M.", "Al Ahli M. vs Al Shabbab", "Al Ahli M. vs Al-Najma", "<PERSON> vs Al-Najma", "<PERSON> vs Al-Muharraq", "Al Shabbab vs Al Riffa", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs Al Riffa", "East Riffa vs Al-Najma", "East Riffa vs Al-Muharraq", "Al Shabbab vs East Riffa", "Al-Najma vs East Riffa", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs Al Shabbab", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs Al-Muhar<PERSON><PERSON>", "Al Shabbab vs Khalidiya", "Mal<PERSON>ya vs Al-Muharraq", "Al Shabbab vs Malkiya", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs Malkiya", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs Mal<PERSON>ya", "Malkiya vs Al Shabbab", "Malkiya vs Al-Najma", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs Al Shabbab", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs Al-Muhar<PERSON><PERSON>", "<PERSON><PERSON> vs Al-Muharraq", "Al Shabbab vs Sitra", "<PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs Al Shabbab"], "teams_affected": ["Al Ahli M.", "Manama", "<PERSON><PERSON>", "Al-Muharraq", "East Riffa", "<PERSON><PERSON>", "Al R<PERSON>a", "Al-<PERSON><PERSON><PERSON>", "Malkiya", "Al Shabbab"], "total_matches": 126, "valid_matches": 94, "skipped_matches": 32}, "ESTONIA_ESILIIGA": {"warning_count": 3, "missing_matches": ["Levadia B vs Nomme Utd", "Tallinna K. B vs Nomme Utd", "Nomme Utd vs Viimsi"], "teams_affected": ["Tallinna K. B", "Viimsi", "Nomme Utd", "Levadia B"], "total_matches": 25, "valid_matches": 22, "skipped_matches": 3}, "CHILE_PRIMERA_DIVISION": {"warning_count": 2, "missing_matches": ["La Serena vs Everton", "Colo-Colo vs Palestino"], "teams_affected": ["Colo-Colo", "Everton", "La Serena", "<PERSON><PERSON><PERSON>"], "total_matches": 9, "valid_matches": 7, "skipped_matches": 2}, "ISRAEL_NATIONAL_LEAGUE": {"warning_count": 34, "missing_matches": ["<PERSON><PERSON><PERSON> vs <PERSON><PERSON> <PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs B<PERSON><PERSON>", "<PERSON><PERSON> <PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON> <PERSON>", "<PERSON><PERSON> <PERSON><PERSON>Fahm vs Hapoel Afula", "Hapoel Afula vs H. Umm al-Fahm", "Hapoel Afula vs H. Umm al-Fahm", "<PERSON><PERSON> <PERSON>m al-Fahm vs Hapoel Akko", "Hapoel Akko vs H. Umm al-Fahm", "Hapoel Raanana vs H. Umm al-Fahm", "<PERSON><PERSON> <PERSON><PERSON> al-Fahm vs Hapoel Raanana", "<PERSON><PERSON> <PERSON><PERSON> al-Fahm vs Hapoel Tel Aviv", "Hapoel Tel Aviv vs H. Umm al-Fahm", "<PERSON><PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON>", "<PERSON><PERSON> <PERSON> vs <PERSON><PERSON>", "Maccabi Jaffa vs H. Umm al-Fahm", "<PERSON><PERSON> <PERSON><PERSON> al-Fahm vs Maccabi Jaffa", "Maccabi Jaffa vs H. Umm al-Fahm", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>"], "teams_affected": ["Hapoel Afula", "<PERSON><PERSON> <PERSON>", "Maccabi Jaffa", "<PERSON><PERSON>", "Hapoel Raanana", "<PERSON><PERSON><PERSON>", "Hapoel Tel Aviv", "Hapoel Akko", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Bnei <PERSON>"], "total_matches": 504, "valid_matches": 470, "skipped_matches": 34}, "NORWAY_ELITESERIEN": {"warning_count": 1, "missing_matches": ["Fredrikstad vs Valerenga"], "teams_affected": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "total_matches": 7, "valid_matches": 6, "skipped_matches": 1}, "TURKEY_3_LIG_GROUP_4": {"warning_count": 72, "missing_matches": ["K. Istiklalspor vs Agri 1970", "Agri 1970 vs Coruhlu", "Erciyes vs Agri 1970", "Agri 1970 vs K. Istiklalspor", "Coruhlu vs Agri 1970", "Agri 1970 vs Erciyes", "Denizlispor vs Coruhlu", "Erciyes vs Denizlispor", "Denizlispor vs K. Istiklalspor", "Coruhlu vs Denizlispor", "Denizlispor vs Erciyes", "K. Istiklalspor vs Denizlispor", "Edirnespor vs K. Istiklalspor", "Coruhlu vs Edirnespor", "Erciyes vs Edirnespor", "K. Istiklalspor vs Edirnespor", "Edirnespor vs Coruhlu", "Edirnespor vs Erciyes", "Kirikkale B. vs K. Istiklalspor", "Kirikkale B. vs Coruhlu", "Erciyes vs Kirikkale B.", "K. Istiklalspor vs Kirikkale B.", "Coruhlu vs Kirikkale B.", "Kirikkale B. vs Erciyes", "Coruhlu vs Nigde Belediyes", "Nigde Belediyes vs Erciyes", "K. Istiklalspor vs Nigde Belediyes", "Nigde Belediyes vs Coruhlu", "Erciyes vs Nigde Belediyes", "Nigde Belediyes vs K. Istiklalspor", "Nilufer B. vs Coruhlu", "Nilufer B. vs Erciyes", "K. Istiklalspor vs Nilufer B.", "Erciyes vs Nilufer B.", "Coruhlu vs Nilufer B.", "Nilufer B. vs K. Istiklalspor", "Orduspor 1967 vs Coruhlu", "Erciyes vs Orduspor 1967", "Orduspor 1967 vs K. Istiklalspor", "Coruhlu vs Orduspor 1967", "K. Istiklalspor vs Orduspor 1967", "Orduspor 1967 vs Erciyes", "Coruhlu vs Polatli B.", "Erciyes vs Polatli B.", "Polatli B. vs K. Istiklalspor", "Polatli B. vs Coruhlu", "Polatli B. vs Erciyes", "K. Istiklalspor vs Polatli B.", "Erciyes vs Sebat G.", "Sebat G. vs K. Istiklalspor", "<PERSON><PERSON> G. vs Coruhlu", "<PERSON><PERSON> G. vs Erciyes", "K. Istiklalspor vs Sebat G.", "Coruhlu vs Sebat G.", "Coruhlu vs Tepecik", "Tepecik vs Erciyes", "Tepecik vs K. Istiklalspor", "Tepecik vs Coruhlu", "Erciyes vs Tepecik", "K. Istiklalspor vs Tepecik", "Turgutluspor vs Erciyes", "K. Istiklalspor vs Turgutluspor", "Coruhlu vs Turgutluspor", "Erciyes vs Turgutluspor", "Turgutluspor vs K. Istiklalspor", "Turgutluspor vs Coruhlu", "Z. Komurspor vs Erciyes", "K. Istiklalspor vs Z. Komurspor", "Coruhlu vs Z. Komurspor", "Erciyes vs Z. Komurspor", "Z. Komurspor vs K. Istiklalspor", "Z. Komurspor vs Coruhlu"], "teams_affected": ["Z. Ko<PERSON>por", "<PERSON><PERSON><PERSON>", "Nilufer B.", "<PERSON><PERSON><PERSON><PERSON>", "Denizlispor", "Polatli B.", "Sebat G.", "Tepecik", "Orduspor 1967", "Edirnespor", "Agri 1970", "Turgutluspor", "<PERSON><PERSON><PERSON><PERSON>", "K. Istiklalspor", "Kirikkale B."], "total_matches": 360, "valid_matches": 288, "skipped_matches": 72}, "ITALY_SERIE_D_GROUP_H": {"warning_count": 28, "missing_matches": ["Costa DAmalfi vs <PERSON><PERSON>", "Brindisi vs Costa DAmalfi", "Costa DAmalfi vs Brindisi", "Casarano vs Costa DAmalfi", "Costa DAmalfi vs Casarano", "Costa DAmalfi vs Fidelis Andria", "<PERSON>delis Andria vs Costa DAmalfi", "Costa DAmalfi vs Francavilla", "Francavilla vs Costa DAmalfi", "Gravina vs Costa DAmalfi", "Costa DAmalfi vs Gravina", "Costa DAmalfi vs Ischia", "Ischia vs Costa DAmalfi", "Costa DAmalfi vs Manfredonia", "Manfredonia vs Costa DAmalfi", "Martina Franca vs Costa DAmalfi", "Costa DAmalfi vs Martina Franca", "Matera vs Costa DAmalfi", "Costa DAmalfi vs Matera", "Costa DAmalfi vs Nardo", "<PERSON><PERSON> vs Costa DAmalfi", "Costa DAmalfi vs Nocerina", "Nocerina vs Costa DAmalfi", "Real Acerrana vs Costa DAmalfi", "Costa DAmalfi vs Ugento", "Ugento vs Costa DAmalfi", "V. Francavilla vs Costa DAmalfi", "Costa DAmalfi vs V. Francavilla"], "teams_affected": ["Brindisi", "<PERSON>", "Ischia", "<PERSON><PERSON><PERSON>", "Manfredonia", "Costa DAmalfi", "Ugento", "Francavilla", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "V. <PERSON>", "Real Acerrana", "<PERSON><PERSON>", "Gravina", "Nocer<PERSON>"], "total_matches": 480, "valid_matches": 452, "skipped_matches": 28}, "NORWAY_DIVISION_2_GROUP_1": {"warning_count": 1, "missing_matches": ["Sandviken vs Flekkeroy"], "teams_affected": ["Sandviken", "<PERSON><PERSON><PERSON><PERSON>"], "total_matches": 6, "valid_matches": 5, "skipped_matches": 1}, "NORTHERN_IRELAND_PREMIERSHIP_WOMEN": {"warning_count": 2, "missing_matches": ["Linfield W vs Mid-Ulster W", "Mid-Ulster W vs Linfield W"], "teams_affected": ["Linfield W", "Mid-Ulster W"], "total_matches": 18, "valid_matches": 16, "skipped_matches": 2}, "GERMANY_OBERLIGA_HAMBURG": {"warning_count": 46, "missing_matches": ["Alsterbruder vs Halstenbek-R.", "Al<PERSON>bruder vs Vorwarts-<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs Al<PERSON>bruder", "Vorwarts-<PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON>er", "Altona vs Vorwarts-<PERSON><PERSON><PERSON>", "Altona vs Halstenbek-R.", "Vorwarts-<PERSON><PERSON><PERSON> vs Altona", "<PERSON><PERSON>bek-R. vs Altona", "Buchholz vs Halstenbek-R.", "Vorwarts-<PERSON><PERSON><PERSON> vs Buchholz", "Buchholz vs Vorwarts-<PERSON><PERSON><PERSON>", "Halstenbek-R<PERSON> vs Buchholz", "Vorwarts-<PERSON><PERSON><PERSON> vs Dassendorf", "Dassendorf vs Halstenbek-R.", "Dassendorf vs Vorwarts-Wacker", "Halstenbek-R. vs Dassendorf", "<PERSON><PERSON><PERSON>-<PERSON><PERSON> vs <PERSON><PERSON><PERSON>tel<PERSON>", "<PERSON><PERSON>butteler vs Vorwarts-<PERSON><PERSON><PERSON>", "Eimsbutteler vs Halstenbek-R.", "Vorwarts-<PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON><PERSON>", "Halstenbek-R. vs Harksheide", "Vorwarts-<PERSON><PERSON><PERSON> vs Harksheide", "Harksheide vs Halstenbek-R.", "Harksheide vs Vorwarts-<PERSON><PERSON>er", "<PERSON><PERSON><PERSON> vs Halstenbek-R.", "Vorwarts-<PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "<PERSON><PERSON>bek-R<PERSON> vs Paloma", "<PERSON><PERSON> vs Halstenbek-R.", "Vorwarts-<PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "Halstenbek-R. vs <PERSON><PERSON>", "<PERSON><PERSON> vs Vorwarts-<PERSON><PERSON><PERSON>", "Halstenbek-R. vs Suderelbe", "Suderelbe vs Vorwarts-<PERSON><PERSON><PERSON>", "Suderelbe vs Halstenbek-R.", "Vorwarts-<PERSON><PERSON><PERSON> vs T. Wilhelm<PERSON>", "<PERSON><PERSON><PERSON>-<PERSON><PERSON> vs T<PERSON> Wilhelmsburg", "T. Wilhelmsburg vs Halstenbek-R.", "T. Wilhelmsburg vs Vorwarts-<PERSON><PERSON>er", "Victoria H. B vs Halstenbek-R.", "Vorwarts-<PERSON><PERSON><PERSON> vs Victoria H. B", "<PERSON><PERSON><PERSON>-<PERSON><PERSON> vs Victoria H. B", "Victoria H. B vs Vorwarts-<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Concordia vs Vorwarts-<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>-<PERSON><PERSON> vs W. Concordia", "Vorwarts-<PERSON><PERSON><PERSON> vs W. Concordia", "<PERSON><PERSON> Concord<PERSON> vs Halstenbek-R."], "teams_affected": ["Victoria H. B", "Sasel", "Dassendorf", "T. <PERSON>", "W. <PERSON>", "<PERSON><PERSON><PERSON>", "Altona", "Suderelbe", "Buchholz", "<PERSON>or<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Harksheide", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>-R<PERSON>"], "total_matches": 382, "valid_matches": 336, "skipped_matches": 46}, "CHILE_PRIMERA_B": {"warning_count": 58, "missing_matches": ["Cobreloa vs Antofagasta", "Antofagasta vs Copiapo", "CD Santa Cruz vs Copiapo", "Cobreloa vs CD Santa Cruz", "Cobreloa vs U. San Felipe", "San Marcos vs Cobreloa", "Cobreloa vs U. Concepcion", "Rangers vs Cobreloa", "Cobreloa vs Antofagasta", "Cobreloa vs Magallanes", "Copiapo vs Cobreloa", "San Luis vs Cobreloa", "Cobreloa vs Concepcion", "Recoleta vs Cobreloa", "Cobreloa vs CD Santa Cruz", "Deportes Temuco vs Cobreloa", "S. Wanderers vs Cobreloa", "Cobreloa vs Curico Unido", "Cobreloa vs S. Morning", "Concepcion vs Copiapo", "Cobreloa vs Concepcion", "Copia<PERSON> vs S. Morning", "Curico Unido vs Copiapo", "Copiapo vs Rangers", "U. San Felipe vs Copiapo", "Copiapo vs S. Wanderers", "Concepcion vs Copiapo", "Copiapo vs Cobreloa", "CD Santa Cruz vs Copiapo", "Copiapo vs Deportes Temuco", "Antofagasta vs Copiapo", "Copiapo vs Recoleta", "San Marcos vs Copiapo", "Copiapo vs San Luis", "U. Concepcion vs Copiapo", "Magallanes vs Copiapo", "Curico Unido vs Copiapo", "Cobreloa vs Curico Unido", "Copiapo vs Deportes Temuco", "Deportes Temuco vs Cobreloa", "Cobreloa vs Magallanes", "Magallanes vs Copiapo", "Copiapo vs Rangers", "Rangers vs Cobreloa", "Recoleta vs Cobreloa", "Copiapo vs Recoleta", "Copia<PERSON> vs S. Morning", "Cobreloa vs S. Morning", "Copiapo vs S. Wanderers", "S. Wanderers vs Cobreloa", "San Luis vs Cobreloa", "Copiapo vs San Luis", "San Marcos vs Cobreloa", "San Marcos vs Copiapo", "Cobreloa vs U. Concepcion", "U. Concepcion vs Copiapo", "Cobreloa vs U. San Felipe", "U. San Felipe vs Copiapo"], "teams_affected": ["Cobreloa", "Curico Unido", "CD Santa Cruz", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "U. San Felipe", "Recoleta", "Magallanes", "S. Morning", "Concepcion", "Deportes Temuco", "Rangers", "U. Concepcion", "San Marcos", "San Luis", "<PERSON><PERSON><PERSON>", "S. Wanderers"], "total_matches": 240, "valid_matches": 182, "skipped_matches": 58}, "GERMANY_REGIONALLIGA_BAYERN": {"warning_count": 31, "missing_matches": ["Hankofen-H. vs Ansbach", "Ansbach vs Hankofen-H.", "Aschaffenburg vs Hankofen-H.", "Hankofen-H. vs Aschaffenburg", "Aubstadt vs Hankofen-H.", "Hankofen-H. vs Augsburg B", "Augsburg B vs Hankofen-H.", "Hankofen-H. vs Bayern Munich B", "Bayern Munich B vs Hankofen-H.", "Bayreuth vs Hankofen-H.", "Hankofen-H. vs Buchbach", "<PERSON><PERSON> vs Hankofen-H.", "Hankofen-<PERSON>. vs <PERSON><PERSON>", "Greuther F. B vs Hankofen-H.", "Hankofen-H. vs Greuther F. B", "Hankofen-H. vs <PERSON><PERSON><PERSON>sen", "<PERSON><PERSON><PERSON><PERSON> vs Hankofen-H.", "Nurnberg B vs Hankofen-H.", "Hankofen-H. vs Nurnberg B", "Hankofen-H. vs Schwaben A.", "Schwaben A. vs Hankofen-H.", "Hankofen-H. vs Schweinfurt", "Schweinfurt vs Hankofen-H.", "Turkgu<PERSON> M. vs Hankofen-H.", "Hankofen-H. vs Turkgucu M.", "Vilzing vs Hankofen-H.", "Hankofen-H. vs Vilzing", "<PERSON><PERSON> vs Hankofen-H.", "Hankofen-H. vs <PERSON><PERSON>", "Hankofen-H. vs Wurzburger K.", "Wurzburger K. vs Hankofen-H."], "teams_affected": ["<PERSON><PERSON><PERSON><PERSON>", "Bayreuth", "Buchbach", "V<PERSON><PERSON>", "Ansbach", "Hankofen-H.", "<PERSON><PERSON>", "Greuther F. B", "<PERSON><PERSON>", "Aschaffenburg", "Nurnberg B", "Schwaben A.", "<PERSON><PERSON><PERSON><PERSON> M.", "Wurzburger K.", "Aubstadt", "Schweinfurt", "Augsburg B", "Bayern Munich B"], "total_matches": 525, "valid_matches": 494, "skipped_matches": 31}, "USA_MAJOR_LEAGUE_SOCCER": {"warning_count": 2, "missing_matches": ["<PERSON> vs San Diego", "San Diego vs St. Louis City"], "teams_affected": ["St. Louis City", "Austin", "San Diego"], "total_matches": 36, "valid_matches": 34, "skipped_matches": 2}, "SWEDEN_DIV_2_VASTRA_GOTALAND": {"warning_count": 3, "missing_matches": ["As<PERSON>o vs Hestrafor", "Bo<PERSON>jan vs Bergdalens", "Astorps FF vs Jonsered"], "teams_affected": ["Astorps FF", "<PERSON><PERSON><PERSON>", "Jonsered", "<PERSON><PERSON><PERSON>", "Bergdalens", "Hestrafor"], "total_matches": 6, "valid_matches": 3, "skipped_matches": 3}, "SWEDEN_DIV_1_NORRA": {"warning_count": 4, "missing_matches": ["<PERSON><PERSON> vs Karlberg", "Arlanda vs United Nordic", "United Nordic vs Gefle", "United Nordic vs Team TG"], "teams_affected": ["United Nordic", "<PERSON><PERSON><PERSON>", "Haninge", "Arlanda", "Karlberg", "Team TG"], "total_matches": 10, "valid_matches": 6, "skipped_matches": 4}, "FINLAND_KAKKONEN_GROUP_B": {"warning_count": 5, "missing_matches": ["Ilves B vs NJS", "Ilves-Kissat vs Poxyt", "P-Iirot vs Poxyt", "Poxyt vs Ilves-Kissat", "Poxyt vs P-Iirot"], "teams_affected": ["Poxyt", "NJS", "P-<PERSON><PERSON><PERSON>", "Ilves B", "Ilves-Kissat"], "total_matches": 21, "valid_matches": 16, "skipped_matches": 5}, "ECUADOR_SERIE_B": {"warning_count": 3, "missing_matches": ["Chacaritas vs Imbabura", "<PERSON><PERSON><PERSON> vs Cumbaya", "22 <PERSON> vs <PERSON> Torres"], "teams_affected": ["Chacaritas", "22 de <PERSON>", "<PERSON><PERSON><PERSON>", "I<PERSON>bur<PERSON>", "<PERSON>", "Cumbaya"], "total_matches": 7, "valid_matches": 4, "skipped_matches": 3}, "GERMANY_BUNDESLIGA_2": {"warning_count": 25, "missing_matches": ["Braunschweig vs Karlsruher SC", "Karlsruher SC vs Braunschweig", "Karlsruher SC vs Darmstadt", "Darmstadt vs Karlsruher SC", "Dusseldorf vs Karlsruher SC", "Karlsruher SC vs Dusseldorf", "Karlsruher SC vs Elversberg", "Elversberg vs Karlsruher SC", "<PERSON><PERSON>uth<PERSON> vs Karlsruher SC", "Karlsruher SC vs Greuther Furth", "Hannover 96 vs Karlsruher SC", "Karlsruher SC vs Hannover 96", "Karlsruher SC vs Hertha Berlin", "Hertha Berlin vs Karlsruher SC", "Kaiserslautern vs Karlsruher SC", "Magdeburg vs Karlsruher SC", "Karlsruher SC vs Magdeburg", "Paderborn vs Karlsruher SC", "Karlsruher SC vs Preuss. Munster", "Preuss. Munster vs Karlsruher SC", "Karlsruher SC vs Regensburg", "Karlsruher SC vs Schalke 04", "Schalke 04 vs Karlsruher SC", "Ulm vs Karlsruher SC", "Karlsruher SC vs Ulm"], "teams_affected": ["<PERSON><PERSON><PERSON><PERSON>", "Darmstadt", "Kaiserslautern", "Elversberg", "Paderborn", "<PERSON><PERSON>", "Preuss. Munster", "Schalke 04", "Dusseldorf", "Braunschweig", "Magdeburg", "Karlsruher SC", "Regensburg", "<PERSON><PERSON>", "Hannover 96"], "total_matches": 434, "valid_matches": 409, "skipped_matches": 25}, "BOLIVIA_LIGA_DE_FUTBOL_PROFESIONAL_BOLIVIANO": {"warning_count": 2, "missing_matches": ["<PERSON><PERSON> vs GV San Jose", "<PERSON><PERSON> Petrolero vs Real Oruro"], "teams_affected": ["GV San Jose", "Real Oruro", "<PERSON><PERSON>"], "total_matches": 9, "valid_matches": 7, "skipped_matches": 2}, "VIETNAM_V_LEAGUE_2": {"warning_count": 116, "missing_matches": ["Ba Ria Vung Tau vs <PERSON> B", "PVF-CAND vs Ba Ria Vung Tau", "<PERSON> vs Ba Ria <PERSON>", "<PERSON> R<PERSON> vs <PERSON><PERSON>", "Ba Ria Vung Tau vs PVF-CAND", "Ho Chi Minh B vs Ba Ria Vung Tau", "<PERSON><PERSON> vs Ba Ria <PERSON>", "Ba Ria <PERSON>ung Tau vs <PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON> B vs <PERSON><PERSON> Phuoc", "<PERSON> vs <PERSON><PERSON> Phuoc", "Binh Phuoc vs PVF-CAND", "<PERSON><PERSON> vs <PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> Phuoc", "<PERSON><PERSON> Ph<PERSON> vs <PERSON>", "PVF-CAND vs Binh Phuoc", "<PERSON><PERSON> vs <PERSON>", "<PERSON> vs <PERSON>", "<PERSON> vs <PERSON>", "<PERSON> vs PVF-CAND", "<PERSON> vs <PERSON><PERSON>", "<PERSON> Minh B vs <PERSON>", "<PERSON> vs <PERSON>", "PVF-CAND vs Dong Nai", "<PERSON><PERSON> vs <PERSON>", "<PERSON> vs <PERSON>", "<PERSON> An vs <PERSON>", "<PERSON> vs <PERSON><PERSON>", "<PERSON> vs <PERSON>", "<PERSON><PERSON> vs <PERSON>", "<PERSON> vs <PERSON><PERSON> Phuoc", "<PERSON><PERSON> vs <PERSON>", "<PERSON> vs Ba Ria <PERSON>", "PVF-CAND vs <PERSON> Thap", "<PERSON><PERSON> vs <PERSON>", "<PERSON> vs <PERSON> An", "<PERSON> Minh B vs <PERSON>", "<PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> Ph<PERSON> vs <PERSON>", "<PERSON> vs <PERSON><PERSON>", "<PERSON> vs <PERSON>", "<PERSON> vs PVF-CAND", "Ba Ria <PERSON>ung Tau vs <PERSON>", "<PERSON> vs <PERSON><PERSON>", "<PERSON> vs <PERSON>", "<PERSON> B vs <PERSON><PERSON> Phuoc", "Ho <PERSON> B vs PVF-CAND", "Ba Ria Vung Tau vs <PERSON> B", "<PERSON> vs Long An", "<PERSON> vs <PERSON>", "<PERSON><PERSON> vs <PERSON>", "<PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON>h vs <PERSON> B", "<PERSON> vs <PERSON><PERSON>", "PVF-CAND vs Ho <PERSON> B", "<PERSON><PERSON> vs <PERSON>", "<PERSON> Minh B vs <PERSON>", "<PERSON> Minh B vs <PERSON>", "<PERSON> An vs <PERSON>", "Ho Chi Minh B vs Ba Ria Vung Tau", "<PERSON><PERSON> vs <PERSON>", "<PERSON> B vs <PERSON><PERSON> Binh", "<PERSON><PERSON> vs <PERSON>", "<PERSON> vs <PERSON><PERSON> <PERSON>", "PVF-CAND vs Hoa Binh", "<PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON>h vs <PERSON> B", "<PERSON><PERSON> vs <PERSON>", "Ho<PERSON> Binh vs PVF-CAND", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON> B vs <PERSON><PERSON> Binh", "<PERSON><PERSON> vs <PERSON>", "Hue vs PVF-CAND", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON> vs <PERSON><PERSON>", "PVF-CAND vs Hue", "<PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON>", "Long An vs PVF-CAND", "<PERSON> An vs <PERSON>", "<PERSON> vs Long An", "<PERSON><PERSON> vs <PERSON> An", "<PERSON> vs <PERSON> An", "PVF-CAND vs Long An", "<PERSON> An vs <PERSON>", "<PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "PVF-CAND vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON>", "<PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs PVF-CAND", "<PERSON><PERSON> vs <PERSON>", "<PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON>", "PVF-CAND vs <PERSON><PERSON>", "<PERSON> R<PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON> An", "<PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> Phuoc", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs Ba Ria <PERSON>", "<PERSON><PERSON> vs PVF-CAND", "<PERSON> vs <PERSON><PERSON> <PERSON>"], "teams_affected": ["<PERSON><PERSON> Binh", "<PERSON>", "<PERSON>", "PVF-CAND", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON> An", "<PERSON> B", "<PERSON><PERSON> Ph<PERSON>", "Ba Ria Vung Tau"], "total_matches": 200, "valid_matches": 84, "skipped_matches": 116}, "SPAIN_SEGUNDA_RFEF_GROUP_3": {"warning_count": 31, "missing_matches": ["Ilicitano vs <PERSON><PERSON>", "<PERSON><PERSON> vs Ilicitano", "Alzira vs Ilicitano", "Ilicitano vs Alzira", "Ilicitano vs Andratx", "Andratx vs Ilicitano", "Ilicitano vs Badalona", "Badalona vs Ilicitano", "Cornella vs Ilicitano", "Ilicitano vs Cornella", "Espanyol B vs Ilicitano", "Ilicitano vs Espanyol B", "Europa vs Ilicitano", "Ilicitano vs Europa", "Ilicitano vs Lleida", "Lleida vs Ilicitano", "Ilicitano vs Mallorca B", "Mallorca B vs Ilicitano", "Ilicitano vs Olot", "Olot vs Ilicitano", "Ilicitano vs Pena Deportiva", "Sabadell vs Ilicitano", "Ilicitano vs Sabadell", "Ilicitano vs Sant Andreu", "<PERSON> vs Ilicitano", "Terrassa vs Ilicitano", "Ilicitano vs Terrassa", "Ilicitano vs Torrent", "Torrent vs Ilicitano", "Valencia B vs Ilicitano", "Ilicitano vs Valencia B"], "teams_affected": ["Mallorca B", "<PERSON>", "<PERSON><PERSON>", "Alzira", "Pena Deportiva", "Badalona", "Cornella", "Europa", "Sa<PERSON>ell", "Lleida", "Terrassa", "Valencia B", "Andratx", "<PERSON><PERSON>", "Espanyol B", "<PERSON><PERSON>", "Ilicitano"], "total_matches": 526, "valid_matches": 495, "skipped_matches": 31}, "NORWAY_1_DIVISION": {"warning_count": 2, "missing_matches": ["<PERSON><PERSON> vs Kongsvinger", "<PERSON><PERSON>id vs Kongsvinger"], "teams_affected": ["Kongsvinger", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "total_matches": 9, "valid_matches": 7, "skipped_matches": 2}, "CHINA_LEAGUE_ONE": {"warning_count": 4, "missing_matches": ["Nantong Zhiyun vs Chongqing T.", "Shanghai Jiadin vs Shenzhen J.", "Suzhou Dongwu vs Shaanxi Union", "<PERSON><PERSON> vs Suzhou Dongwu"], "teams_affected": ["Chongqing T.", "Shenzhen J.", "<PERSON><PERSON><PERSON>", "Shaanxi Union", "Shanghai Jiadin", "Suzhou Dongwu", "<PERSON><PERSON>"], "total_matches": 8, "valid_matches": 4, "skipped_matches": 4}, "BRAZIL_BRASILEIRO_WOMEN": {"warning_count": 2, "missing_matches": ["RB Bragantino W vs Flamengo W", "Palmeiras W vs RB Bragantino W"], "teams_affected": ["Flamengo W", "Palmeiras W", "RB Bragantino W"], "total_matches": 13, "valid_matches": 11, "skipped_matches": 2}, "PORTUGAL_CAMPEONATO_DE_PORTUGAL_GROUP_B": {"warning_count": 24, "missing_matches": ["Beira-Mar vs Alpendorada", "Alpendorada vs Beira-Mar", "Beira-Mar vs Camacha", "Camacha vs Beira-Mar", "Cinfaes vs Beira-Mar", "Beira-Mar vs Cinfaes", "Beira-Mar vs Coimbroes", "Coimbroes vs Beira-Mar", "Gondomar vs Beira-Mar", "Beira-Mar vs Gondomar", "Beira-Mar vs Guarda", "Guarda vs Beira-Mar", "Beira-Mar vs Leca", "Leca vs Beira-Mar", "Machico vs Beira-Mar", "Beira-Mar vs Machico", "Beira-Mar vs Maritimo B", "Maritimo B vs Beira-Mar", "Regua vs Beira-Mar", "Beira-Mar vs Regua", "Salgueiros vs Beira-Mar", "Beira-Mar vs Salgueiros", "Beira-Mar vs Uniao Lamas", "<PERSON><PERSON><PERSON> vs Beira-Mar"], "teams_affected": ["Salgueiros", "Beira-Mar", "Leca", "Gondomar", "Cinfaes", "Camacha", "Coim<PERSON>es", "Mac<PERSON><PERSON>", "Guarda", "Regua", "Maritimo B", "<PERSON><PERSON><PERSON>", "Alpendorada"], "total_matches": 312, "valid_matches": 288, "skipped_matches": 24}, "LATVIA_VIRSLIGA": {"warning_count": 2, "missing_matches": ["Auda vs Super Nova", "Tukums vs Super Nova"], "teams_affected": ["Tukums", "Auda", "Super Nova"], "total_matches": 18, "valid_matches": 16, "skipped_matches": 2}, "JAPAN_J1_LEAGUE": {"warning_count": 1, "missing_matches": ["<PERSON><PERSON><PERSON> vs Kyoto Sanga"], "teams_affected": ["Kyoto Sanga", "<PERSON><PERSON><PERSON>"], "total_matches": 13, "valid_matches": 12, "skipped_matches": 1}, "ESTONIA_ESILIIGA_B": {"warning_count": 4, "missing_matches": ["Paide B vs Legion", "Maardu vs Narva Trans B", "Narva Trans B vs Maardu", "Tartu Kalev vs Tabasalu"], "teams_affected": ["Tart<PERSON>", "Paide B", "Legion", "<PERSON><PERSON><PERSON>", "Tabasalu", "Narva Trans B"], "total_matches": 7, "valid_matches": 3, "skipped_matches": 4}, "GERMANY_OBERLIGA_MITTELRHEIN": {"warning_count": 36, "missing_matches": ["Bergisch G. vs Wegberg<PERSON><PERSON><PERSON>", "Bergisch G. vs Bonn-Endenich", "Wegberg<PERSON><PERSON><PERSON> vs Bergisch G.", "Fortuna Koln B vs Bonn-Endenich", "Fortuna Koln B vs Wegberg-Beeck", "Bonn-Endenich vs Fortuna Koln B", "Frechen vs Bonn-Endenich", "Frechen vs Wegberg-Beeck", "Bonn-<PERSON><PERSON><PERSON> vs Frechen", "Bonn<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> vs Bonn-Endenich", "<PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Bonn-<PERSON><PERSON><PERSON> vs Hurt<PERSON>", "<PERSON>g<PERSON>-<PERSON><PERSON> vs <PERSON><PERSON>", "Konigsdorf vs Wegberg-Beeck", "Bonn-Endenich vs Konigsdorf", "Wegberg-<PERSON><PERSON> vs Konigsdorf", "Konigsdorf vs Bonn-Endenich", "Wegberg-<PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> vs Bonn-Endenich", "<PERSON><PERSON><PERSON> vs Wegberg<PERSON><PERSON><PERSON>", "Bonn-<PERSON><PERSON><PERSON> vs <PERSON><PERSON>ch", "Bonn-Endenich vs Porz", "Wegberg-<PERSON><PERSON> vs Porz", "Porz vs Bonn-Endenich", "Schafhausen vs Bonn-Endenich", "Wegberg-Beeck vs Schafhausen", "Bonn-Endenich vs Schafhausen", "<PERSON>g<PERSON><PERSON><PERSON><PERSON> vs Siegburger", "Siegburger vs Bonn-Endenich", "Siegburger vs Wegberg-Beeck", "Wegberg<PERSON><PERSON><PERSON> vs Teutonia Weiden", "Teutonia Weiden vs Bonn-Endenich", "Teutonia Weiden vs Wegberg-Beeck", "Bonn-End<PERSON><PERSON> vs Teutonia Weiden"], "teams_affected": ["Fortuna Koln B", "<PERSON><PERSON><PERSON>", "Teutonia Weiden", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Schafhausen", "Konigsdorf", "Bergisch G.", "Wegberg-<PERSON><PERSON>", "Frechen", "<PERSON><PERSON>", "Siegburger", "Bonn-Endenich"], "total_matches": 264, "valid_matches": 228, "skipped_matches": 36}, "GERMANY_OBERLIGA_BADEN_WURTTEMBERG": {"warning_count": 25, "missing_matches": ["Aalen vs Leinfelden-E.", "Backnang vs Leinfelden-E.", "Leinfelden-E. vs Backnang", "Balingen vs Leinfelden-E.", "Leinfelden-E. vs Balingen", "Leinfelden-E. vs Bissingen", "Bissingen vs Leinfelden-E.", "Leinfelden-E. vs Fellbach", "Leinfelden-E. vs Grossaspach", "Grossaspach vs Leinfelden-E.", "Leinfelden-E. vs Hollenbach", "Normannia Gmund vs Leinfelden-E.", "Leinfelden-E. vs Normannia Gmund", "Leinfelden-E. vs Nottingen", "Nottingen vs Leinfelden-E.", "Leinfelden-E. vs Oberachern", "Oberachern vs Leinfelden-E.", "Pforzheim vs Leinfelden-E.", "Leinfelden-E. vs Ravensburg", "Ravensburg vs Leinfelden-E.", "Reutlingen vs Leinfelden-E.", "Leinfelden-E. vs Reutlingen", "Leinfelden-E. vs Villingen B", "Zuzenhausen vs Leinfelden-E.", "Leinfelden-E. vs Zuzenhausen"], "teams_affected": ["Balingen", "Hollenbach", "A<PERSON>n", "Pforzheim", "Normannia Gmund", "Oberachern", "Fellbach", "Ravensburg", "Nottingen", "Villingen B", "Zuzenhausen", "Bissingen", "Reutlingen", "Leinfelden-E.", "<PERSON><PERSON><PERSON>", "Grossaspach"], "total_matches": 433, "valid_matches": 408, "skipped_matches": 25}, "FRANCE_NATIONAL_2_GROUP_C": {"warning_count": 84, "missing_matches": ["Aubervilliers vs Fleury-Merogis", "Feignies-A. vs Aubervilliers", "Furiani-Agliani vs Aubervilliers", "Villers H. vs Aubervilliers", "Fleury-Merogis vs Aubervilliers", "Aubervilliers vs Feignies-A.", "Aubervilliers vs Furiani-Agliani", "Aubervilliers vs Villers H.", "Balagne vs Furiani-Agliani", "Balagne vs Villers H.", "Balagne vs Fleury-Merogis", "Feignies-A. vs Balagne", "Furiani<PERSON><PERSON><PERSON><PERSON> vs Balagne", "Villers H. vs Balagne", "F<PERSON>ury<PERSON><PERSON><PERSON><PERSON> vs Balagne", "Balagne vs Feignies-A.", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs Beauvais", "Villers H. vs Beauvais", "<PERSON><PERSON>ury<PERSON><PERSON><PERSON><PERSON> vs Beauvais", "Beauvais vs Feignies-A.", "Beauvais vs Furiani-Agliani", "Beauvais vs Villers H.", "Beau<PERSON><PERSON> vs Fleury-Merogis", "Feignies-A. vs Beauvais", "Biesheim vs Villers H.", "Furiani<PERSON><PERSON><PERSON>ni vs Biesheim", "Biesheim vs Fleury-Merogis", "Villers H. vs Biesheim", "Feignies-A. vs Biesheim", "Biesheim vs Furiani-Agliani", "Fleury-<PERSON><PERSON><PERSON> vs Biesheim", "Biesheim vs Feignies-A.", "Bobigny vs Feignies-A.", "Bobigny vs Furiani-Agliani", "Bobigny vs Villers H.", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs Bobigny", "<PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "<PERSON>igny vs Fleury-Merogis", "Chambly vs Fleury-Merogis", "Feignies-A. vs Chambly", "Chambly vs Furiani-Agliani", "Villers H<PERSON> vs Chambly", "F<PERSON>ury<PERSON><PERSON><PERSON><PERSON> vs Chambly", "Chambly vs Feignies-A.", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs Chambly", "Chambly vs Villers H.", "Creteil vs Villers H.", "Creteil vs Fleury-Merogis", "Feignies-A. vs Creteil", "Creteil vs Furiani-Agliani", "Villers H. vs Creteil", "Fleury-<PERSON><PERSON><PERSON> vs Creteil", "Creteil vs Feignies-A.", "Furiani-A<PERSON>ni vs Creteil", "<PERSON><PERSON>ury<PERSON><PERSON><PERSON><PERSON> vs Epinal", "Epinal vs Feignies-A.", "Epinal vs Furiani-Agliani", "Epinal vs Villers H.", "Feignies-A. vs Epinal", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs Epinal", "Epinal vs Fleury-Merogis", "Villers H. vs Epinal", "Haguenau vs Fleury-Merogis", "Feignies-A. vs Haguenau", "Villers H. vs Haguenau", "Furiani-Agliani vs Haguenau", "Haguenau vs Feignies-A.", "Haguenau vs Furiani-Agliani", "Haguenau vs Villers H.", "Furiani<PERSON><PERSON><PERSON> vs Thionville L.", "Villers H. vs Thionville L.", "Fleury<PERSON><PERSON><PERSON><PERSON> vs Thionville L.", "Thionville L. vs Feignies-A.", "Thionville L. vs Villers H.", "Thionville L. vs Fleury-Me<PERSON>is", "Feignies-A. vs Thionville L.", "<PERSON><PERSON><PERSON> vs Villers H.", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> vs Feignies-A.", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> vs Fleury-Merogis", "Feignies-A. vs <PERSON><PERSON><PERSON>", "Wasquehal vs Furiani-Agliani"], "teams_affected": ["<PERSON><PERSON><PERSON>", "Furiani<PERSON><PERSON><PERSON><PERSON>", "Aubervilliers", "Biesheim", "<PERSON><PERSON>.", "Creteil", "Thionville L.", "Haguenau", "<PERSON><PERSON><PERSON>", "Epinal", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Feignies-A.", "<PERSON><PERSON>ury<PERSON><PERSON><PERSON><PERSON>", "Cha<PERSON>ly"], "total_matches": 319, "valid_matches": 235, "skipped_matches": 84}, "SOUTH_KOREA_K_LEAGUE_1": {"warning_count": 3, "missing_matches": ["Daegu vs Anyang", "Gwangju vs Anyang", "Anyang vs Suwon City"], "teams_affected": ["Suwon City", "Daegu", "Gwangju", "Anyang"], "total_matches": 29, "valid_matches": 26, "skipped_matches": 3}, "ARGENTINA_PRIMERA_DIVISION": {"warning_count": 45, "missing_matches": ["Independiente vs SM San Juan", "Aldosivi vs Estudiantes", "<PERSON><PERSON> vs SM San Juan", "I. Rivadavia vs Aldosivi", "Instituto vs SM San Juan", "Sarmiento vs SM San Juan", "Aldosivi vs Sarmiento", "Central Cordoba vs Aldosivi", "Aldosivi vs Defensa y J.", "Newells vs Aldosivi", "Aldosivi vs Barracas C.", "Belgrano vs Aldosivi", "Aldosivi vs Estudiantes", "Boca Juniors vs Aldosivi", "Aldosivi vs Sarmiento", "Aldosivi vs Tigre", "Argentinos Jrs vs Aldosivi", "Aldosivi vs Union Santa Fe", "<PERSON><PERSON><PERSON> vs Aldosivi", "Aldosivi vs Racing Club", "I. Rivadavia vs Aldosivi", "Aldosivi vs Banfield", "SM San Juan vs Aldosivi", "SM San Juan vs Platense", "Aldosivi vs Racing Club", "Velez Sarsfield vs SM San Juan", "SM San Juan vs <PERSON><PERSON>", "Newells vs Aldosivi", "Aldosivi vs Barracas C.", "SM San Juan vs T. de Cordoba", "Aldosivi vs Defensa y J.", "Argentinos Jrs vs Aldosivi", "SM San Juan vs San Lorenzo", "Belgrano vs Aldosivi", "SM San Juan vs Belgrano", "<PERSON><PERSON><PERSON> vs Aldosivi", "Gimnasia vs SM San Juan", "Central Cordoba vs Aldosivi", "Aldosivi vs Union Santa Fe", "SM San Juan vs River Plate", "Boca Juniors vs Aldosivi", "Aldosivi vs Banfield", "Aldosivi vs Tigre", "Lanus vs SM San Juan", "SM San Juan vs Rosario Central"], "teams_affected": ["Tigre", "Platense", "Sarmiento", "Estudiantes", "Defensa y J.", "Aldosivi", "I. R<PERSON>", "Gimnasia", "<PERSON><PERSON><PERSON>", "Argentinos Jrs", "<PERSON><PERSON>", "Banfield", "Belgrano", "Union Santa Fe", "Velez Sarsfield", "River Plate", "<PERSON><PERSON>rdo<PERSON>", "San Lorenzo", "Instituto", "<PERSON><PERSON>", "Barracas C.", "Rosario Central", "Independiente", "Boca Juniors", "SM San Juan", "Central Cordoba", "<PERSON><PERSON>", "Racing Club", "<PERSON><PERSON><PERSON>"], "total_matches": 448, "valid_matches": 403, "skipped_matches": 45}, "CHINA_SUPER_LEAGUE": {"warning_count": 1, "missing_matches": ["Yunnan Yukun vs Shanghai Port"], "teams_affected": ["Yunnan Yukun", "Shanghai Port"], "total_matches": 12, "valid_matches": 11, "skipped_matches": 1}, "EGYPT_PREMIER_LEAGUE": {"warning_count": 1, "missing_matches": ["Ghazl El M. vs Ceramica C."], "teams_affected": ["Ghazl El M.", "Ceramica C."], "total_matches": 13, "valid_matches": 12, "skipped_matches": 1}, "SLOVAKIA_2._LIGA": {"warning_count": 46, "missing_matches": ["Humenne vs Zlate Moravce", "Zlate Moravce vs Humenne", "<PERSON><PERSON> vs Zlate Moravce", "<PERSON><PERSON> vs <PERSON><PERSON>", "Zlate Moravce vs Malzenice", "Zlate Moravce vs P. Bystrica", "P. Bystrica vs Zlate Moravce", "Petrzalka vs Zlate Moravce", "Zlate Moravce vs Petrzalka", "Pohronie vs Zlate Moravce", "Zlate Moravce vs Pohronie", "Zlate Moravce vs Presov", "Presov vs Zlate Moravce", "Puchov vs Zlate Moravce", "S. Bratislava B vs Zlate Moravce", "Zlate Moravce vs S. Bratislava B", "S. Lubovna vs Zlate Moravce", "Zlate Moravce vs S. Lubovna", "Zlate Moravce vs Samorin", "Samorin vs Zlate Moravce", "Zlate Moravce vs Zilina B", "Zilina B vs Zlate Moravce", "S. Lubovna vs Zlate Moravce", "Petrzalka vs Zlate Moravce", "Zlate Moravce vs Presov", "Humenne vs Zlate Moravce", "Zlate Moravce vs P. Bystrica", "<PERSON><PERSON> vs Zlate Moravce", "Zlate Moravce vs Zilina B", "Pohronie vs Zlate Moravce", "Zlate Moravce vs Samorin", "S. Bratislava B vs Zlate Moravce", "Zlate Moravce vs Malzenice", "Puchov vs Zlate Moravce", "Zlate Moravce vs Zvolen", "Zlate Moravce vs S. Lubovna", "Zlate Moravce vs Petrzalka", "Presov vs Zlate Moravce", "Zlate Moravce vs Humenne", "P. Bystrica vs Zlate Moravce", "<PERSON><PERSON> vs <PERSON><PERSON>", "Zilina B vs Zlate Moravce", "Zlate Moravce vs Pohronie", "Samorin vs Zlate Moravce", "Zlate Moravce vs S. Bratislava B", "Zlate Moravce vs Zvolen"], "teams_affected": ["Samorin", "Zlate Moravce", "P. <PERSON>", "S. Lubovna", "S. Bratislava B", "<PERSON><PERSON><PERSON>", "Malzenice", "<PERSON><PERSON>", "Petrzalka", "Zvolen", "<PERSON><PERSON>", "Presov", "Zilina B", "Pohronie"], "total_matches": 320, "valid_matches": 274, "skipped_matches": 46}, "BRAZIL_SERIE_D": {"warning_count": 4, "missing_matches": ["Uberlandia vs Cascavel", "Cascavel vs Goiatuba EC", "Manauara vs Independencia", "GAS vs Manauara"], "teams_affected": ["GAS", "Uberlandia", "<PERSON><PERSON><PERSON>", "Cascavel", "Goiatuba EC", "Independencia"], "total_matches": 6, "valid_matches": 2, "skipped_matches": 4}, "SOUTH_KOREA_K_LEAGUE_2": {"warning_count": 4, "missing_matches": ["<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "Incheon Utd vs Gyeongnam", "<PERSON><PERSON><PERSON>g vs Gyeongnam", "Seoul E-Land vs Gyeongnam"], "teams_affected": ["<PERSON><PERSON>", "Seoul E-Land", "Incheon Utd", "<PERSON><PERSON><PERSON><PERSON>", "Gyeongnam"], "total_matches": 16, "valid_matches": 12, "skipped_matches": 4}, "COSTA_RICA_PRIMERA_DIVISION": {"warning_count": 22, "missing_matches": ["Sporting SJ vs Alajuelense", "Alajuelense vs Sporting SJ", "Cartagines vs Sporting SJ", "Sporting SJ vs Cartagines", "Sporting SJ vs Guanacasteca", "Guanacasteca vs Sporting SJ", "Herediano vs Sporting SJ", "Sporting SJ vs Herediano", "Sporting SJ vs M. Liberia", "M. Liberia vs Sporting SJ", "Puntarenas vs Sporting SJ", "Sporting SJ vs Puntarenas", "Sporting SJ vs San Carlos", "San Carlos vs Sporting SJ", "Santa Ana vs Sporting SJ", "Sporting SJ vs Santa Ana", "Sporting SJ vs Santos Guapiles", "Santos Guapiles vs Sporting SJ", "Sporting SJ vs Saprissa", "Saprissa vs Sporting SJ", "Zeledon vs Sporting SJ", "Sporting SJ vs Zeledon"], "teams_affected": ["Guanacasteca", "Puntarenas", "San Carlos", "<PERSON><PERSON><PERSON>", "Herediano", "Alajuelense", "M. <PERSON>", "Sporting SJ", "Cartagines", "Santa Ana", "Saprissa", "Santos Guapiles"], "total_matches": 242, "valid_matches": 220, "skipped_matches": 22}, "BELGIUM_FIRST_DIVISION_A": {"warning_count": 22, "missing_matches": ["Anderlecht vs Sint-Truiden", "Sint-Truiden vs Anderlecht", "Antwerp vs Sint-Truiden", "Sint-Truiden vs Antwerp", "Beerschot vs Sint-Truiden", "Sint-Truiden vs Beerschot", "Cercle Brugge vs Sint-Truiden", "Sint-Truiden vs Cercle Brugge", "Sint-<PERSON><PERSON><PERSON> vs Charleroi", "Charleroi vs Sint-Truiden", "Club Brugge vs Sint-Truiden", "Sint-Truiden vs Club Brugge", "Sint-Truiden vs Dender", "Dender vs Sint-Truiden", "Sint-Tru<PERSON> vs Gent", "Gent vs Sint-Truiden", "Kortrijk vs Sint-Truiden", "Sint-Truiden vs Kortrijk", "Standard Liege vs Sint-Truiden", "Sint-Truiden vs Standard Liege", "Sint-Truiden vs Westerlo", "Westerlo vs Sint-Truiden"], "teams_affected": ["Beerschot", "<PERSON><PERSON>", "Club Brugge", "Sint-Truiden", "Charleroi", "Kortrijk", "Standard Liege", "Gent", "Westerlo", "Cercle Brugge", "Anderlecht", "Antwerp"], "total_matches": 330, "valid_matches": 308, "skipped_matches": 22}, "SWEDEN_DIV_2_NORRA_GOTALAND": {"warning_count": 3, "missing_matches": ["Grebbestad vs Tidaholms", "Vanersborgs IF vs Lidkoping", "Motala vs FBK Karlstad"], "teams_affected": ["Vanersborgs IF", "FBK Karlstad", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Lidkoping", "Tidaholms"], "total_matches": 6, "valid_matches": 3, "skipped_matches": 3}, "SPAIN_LIGA_F_WOMEN": {"warning_count": 24, "missing_matches": ["Athletic Club W vs L. Badalona W", "L. Badalona W vs Athletic Club W", "L. Badalona W vs Atletico M. W", "L. Badalona W vs Deportivo W", "Deportivo W vs L. Badalona W", "<PERSON><PERSON> vs Eibar W", "Eibar W vs L. Badalona W", "L. Badalona W vs Espanyol W", "Espanyol W vs L. Badalona W", "<PERSON><PERSON> vs G. Ten<PERSON>fe W", "<PERSON><PERSON> vs <PERSON><PERSON> Badalo<PERSON> W", "Granada W vs L. Badalona W", "L. Badalona W vs Granada W", "L. Badalona W vs Levante W", "Levante W vs L. Badalona W", "Real Betis W vs L. Badalona W", "L. Badalona W vs Real Betis W", "L. Badalona W vs Real Madrid W", "Real Madrid W vs L. Badalona W", "Real Sociedad W vs L. Badalona W", "L. Badalona W vs Real Sociedad W", "Sevilla W vs L. Badalona W", "<PERSON>. Badalona W vs Sevilla W", "Valencia W vs L. Badalona W"], "teams_affected": ["<PERSON><PERSON>", "Sevilla W", "Valencia W", "Atletico M. W", "Eibar W", "Real Madrid W", "Real Betis W", "Athletic Club W", "Real Sociedad W", "L. <PERSON> W", "Granada W", "Levante W", "Espanyol W", "Deportivo W"], "total_matches": 351, "valid_matches": 327, "skipped_matches": 24}, "FRANCE_NATIONAL_2_GROUP_B": {"warning_count": 92, "missing_matches": ["Avranches vs St Co Locmine", "Le Poiré SV vs Avranches", "Avranches vs St-Pryve St-H.", "Saint-Malo vs Avranches", "St Co Locmine vs Avranches", "Avranches vs Le Poiré SV", "St-Pryve St-H. vs Avranches", "Avranches vs Saint-Malo", "St Co Locmine vs Blois", "Blois vs Le Poiré SV", "St-Pryve St-H. vs Blois", "Blois vs Saint-Malo", "Blois vs St Co Locmine", "Le Poiré SV vs Blois", "Blois vs St-Pryve St-H.", "Saint-Malo vs Blois", "Bordeaux vs St-Pryve St-H.", "Le Poiré SV vs Bordeaux", "Saint-Malo vs Bordeaux", "Bordeaux vs St Co Locmine", "Bordeaux vs Le Poiré SV", "St-Pryve St-H. vs Bordeaux", "Bordeaux vs Saint-Malo", "Bourges vs Le Poiré SV", "St-Pryve St-H. vs Bourges", "Bourges vs Saint-Malo", "St Co Locmine vs Bourges", "Bourges vs St-Pryve St-H.", "Saint-Malo vs Bourges", "Bourges vs St Co Locmine", "Chateaubriant vs Saint-Malo", "Chateaubriant vs St Co Locmine", "Chateaubriant vs Le Poiré SV", "St-Pryve St-H. vs Chateaubriant", "St Co Locmine vs Chateaubriant", "Le Poiré SV vs Chateaubriant", "Chateaubriant vs St-Pryve St-H.", "<PERSON><PERSON> vs St-Pryve St-H.", "<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs St Co Locmine", "Le Poiré SV vs <PERSON><PERSON>", "St-P<PERSON><PERSON>-H. vs <PERSON><PERSON>", "<PERSON><PERSON> vs Saint-Mal<PERSON>", "St Co Locmine vs <PERSON><PERSON>", "<PERSON><PERSON> vs Le Poiré SV", "Granville vs St-Pryve St-H.", "Saint-Malo vs Granville", "St Co Locmine vs Granville", "Granville vs Le Poiré SV", "St-Pryve St-H. vs Granville", "Granville vs Saint-Malo", "Granville vs St Co Locmine", "Le Poiré SV vs Granville", "La Roche vs St-Pryve St-H.", "Saint-Malo vs La Roche", "St Co Locmine vs La Roche", "La Roche vs Le Poiré SV", "St-Pryve St-H. vs La Roche", "La Roche vs Saint-Malo", "La Roche vs St Co Locmine", "Le Poiré SV vs La Roche", "St-Pryve St-H. vs Les Herbiers", "Les Herbiers vs Saint-Malo", "Les Herbiers vs St Co Locmine", "Le Poiré SV vs Les Herbiers", "Les Herbiers vs St-Pryve St-H.", "Saint-Malo vs Les Herbiers", "St Co Locmine vs Les Herbiers", "Les Herbiers vs Le Poiré SV", "St-Pryve St-H. vs Poitiers", "Poitiers vs Saint-Malo", "St Co Locmine vs Poitiers", "Poitiers vs Le Poiré SV", "Poitiers vs St-Pryve St-H.", "Saint-Malo vs Poitiers", "Poitiers vs St Co Locmine", "Le Poiré SV vs Poitiers", "St Co Locmine vs Saumur", "<PERSON><PERSON>ur vs Le Poiré SV", "St-Pryve St-H. vs Saumur", "Saumur vs Saint-Malo", "Saumur vs St Co Locmine", "Le Poiré SV vs Saumur", "<PERSON><PERSON>ur vs St-Pryve St-H.", "Saint-Malo vs Saumur", "St-Pryve St-H. vs Stade Briochin", "Stade Briochin vs Saint-Malo", "Stade Briochin vs St Co Locmine", "Le Poiré SV vs Stade Briochin", "Saint-Malo vs Stade Briochin", "St Co Locmine vs Stade Briochin", "Stade Briochin vs Le Poiré SV"], "teams_affected": ["Saint-Malo", "Bordeaux", "La Roche", "<PERSON><PERSON><PERSON>", "St Co Locmine", "Le Poiré SV", "<PERSON><PERSON><PERSON>", "<PERSON>", "Poitiers", "<PERSON><PERSON><PERSON>", "St-Pryve St-H.", "Stade Briochin", "Avranches", "Chateaubriant", "<PERSON><PERSON>", "Granville"], "total_matches": 348, "valid_matches": 256, "skipped_matches": 92}, "POLAND_EKSTRALIGA_WOMEN": {"warning_count": 13, "missing_matches": ["Skra W vs C. Sosnowiec W", "Gdansk W vs Skra W", "Skra W vs Leczna W", "<PERSON><PERSON>z<PERSON> W vs Skra W", "<PERSON><PERSON> vs Skra W", "Skra W vs Rekord B. W", "Rekord B. W vs Skra W", "Resovia R. W vs Skra W", "Skra W vs Resovia R. W", "Skra W vs Slask Wroclaw W", "Slask Wroclaw W vs Skra W", "Stomilanki O. W vs Skra W", "Skra W vs Stomilanki O. W"], "teams_affected": ["Rekord B. W", "<PERSON><PERSON>", "Leczna W", "<PERSON><PERSON> W", "Resovia R. W", "Skra W", "Stomilanki O. W", "Gdansk W", "<PERSON><PERSON>", "Slask Wroclaw W"], "total_matches": 167, "valid_matches": 154, "skipped_matches": 13}, "GERMANY_OBERLIGA_NIEDERSACHSEN": {"warning_count": 41, "missing_matches": ["<PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON><PERSON>", "Spelle<PERSON><PERSON><PERSON>haus vs <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs Spelle-Venhaus", "Bersenbruck vs Lupo-Martini", "Spelle-Venhaus vs Bersenbruck", "Lupo-Martini vs Bersenbruck", "Spelle-Venhaus vs Braunschweig B", "Braunschweig B vs Spelle-Venhaus", "Braunschweig B vs Lupo-Martini", "Delmenhorst vs Spelle-Venhaus", "<PERSON><PERSON><PERSON>st vs <PERSON><PERSON><PERSON><PERSON><PERSON>", "Spelle-Venhaus vs Delmenhorst", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs Egestorf L.", "Egestorf L. vs Spelle-Venhaus", "Egestorf L. vs <PERSON><PERSON><PERSON><PERSON><PERSON>", "Spelle-Venhaus vs Egestorf L.", "Eintracht Celle vs Spelle-Venhaus", "Eintracht Celle vs Lupo<PERSON><PERSON><PERSON>", "Spelle-Venhaus vs Eintracht Celle", "Spelle-Venhaus vs Hildesheim", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON><PERSON>", "Hildesheim vs Spelle-Venhaus", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs Meppen B", "Meppen B vs Spelle-Venhaus", "Meppen B vs Lupo-Martini", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs Schoningen", "Schoningen vs Spelle-Venhaus", "Schoningen vs Lupo-Martini", "Spelle-Venhaus vs Schoningen", "Verden 04 vs Lupo-Martini", "Spelle-Venhaus vs Verden 04", "Lupo-Martini vs Verden 04", "Spelle-Venhaus vs Vorsfelde", "Vorsfelde vs Lupo-Martini", "Vorsfelde vs Spelle-Venhaus", "Wilhelmshaven vs Spelle-Venhaus", "Wilhelmshaven vs Lupo-Martini", "Spelle-Venhaus vs Wilhelmshaven", "<PERSON><PERSON><PERSON><PERSON><PERSON> vs Wilhelmshaven"], "teams_affected": ["Meppen B", "<PERSON><PERSON>", "Vorsfelde", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Verden 04", "Spelle-Venhaus", "Bersenbruck", "Wilhelmshaven", "Egestorf L.", "Eintracht Celle", "<PERSON><PERSON><PERSON><PERSON>", "Schoningen", "Braunschweig B", "<PERSON><PERSON><PERSON><PERSON>"], "total_matches": 358, "valid_matches": 317, "skipped_matches": 41}, "GREECE_SUPER_LEAGUE": {"warning_count": 50, "missing_matches": ["Olympiakos vs <PERSON><PERSON>", "<PERSON><PERSON> vs Olympiakos", "Aris vs Olympiakos", "Olympiakos vs Aris", "Asteras T. vs Olympiakos", "Olympiakos vs Asteras T.", "Olympiakos vs Atromitos", "Atromitos vs Olympiakos", "Lamia vs Olympiakos", "Olympiakos vs Lamia", "Olympiakos vs Levadiakos", "Levadiakos vs Olympiakos", "Volos vs Olympiakos", "Olympiakos vs <PERSON><PERSON>", "Lamia vs Olympiakos", "Olympiakos vs Panaitolikos", "Aris vs Olympiakos", "Olympiakos vs Atromitos", "Panathinaikos vs Olympiakos", "Olympiakos vs Levadiakos", "Asteras T. vs Olympiakos", "Olympiakos vs Panserraikos", "PAOK vs Olympiakos", "Olympiakos vs AEK Athens", "OFI Crete vs Olympiakos", "Olympiakos vs Volos", "<PERSON><PERSON> vs Olympiakos", "Olympiakos vs Lamia", "Panaitolikos vs Olympiakos", "Olympiakos vs Aris", "Atromitos vs Olympiakos", "Olympiakos vs Panathinaikos", "Levadiakos vs Olympiakos", "Olympiakos vs Asteras T.", "Panserraikos vs Olympiakos", "Olympiakos vs PAOK", "AEK Athens vs Olympiakos", "Olympiakos vs OFI Crete", "Olympiakos vs Panathinaikos", "PAOK vs Olympiakos", "Olympiakos vs AEK Athens", "Olympiakos vs Panaitolikos", "Panaitolikos vs Olympiakos", "Panathinaikos vs Olympiakos", "Olympiakos vs Panathinaikos", "Olympiakos vs Panathinaikos", "Olympiakos vs Panserraikos", "Panserraikos vs Olympiakos", "Volos vs Olympiakos", "Olympiakos vs Volos"], "teams_affected": ["PAOK", "<PERSON><PERSON>", "Atromitos", "OFI Crete", "Panathinaikos", "AEK Athens", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Panserraikos", "Volos", "Levadiakos", "Panaitolikos", "<PERSON><PERSON><PERSON>."], "total_matches": 316, "valid_matches": 266, "skipped_matches": 50}, "AUSTRALIA_NEW_SOUTH_WALES_NPL": {"warning_count": 1, "missing_matches": ["Manly Utd vs Mount Druitt"], "teams_affected": ["Manly Utd", "Mount Druitt"], "total_matches": 20, "valid_matches": 19, "skipped_matches": 1}, "ISRAEL_LEUMIT_LEAGUE": {"warning_count": 36, "missing_matches": ["<PERSON><PERSON><PERSON> vs <PERSON><PERSON> <PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs B<PERSON><PERSON>", "<PERSON><PERSON> <PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON> <PERSON>", "<PERSON><PERSON> <PERSON><PERSON>Fahm vs Hapoel Afula", "Hapoel Afula vs H. Umm al-Fahm", "Hapoel Afula vs H. Umm al-Fahm", "<PERSON><PERSON> <PERSON>m al-Fahm vs Hapoel Akko", "Hapoel Akko vs H. Umm al-Fahm", "Hapoel Akko vs H. Umm al-Fahm", "Hapoel Raanana vs H. Umm al-Fahm", "<PERSON><PERSON> <PERSON><PERSON> al-Fahm vs Hapoel Raanana", "<PERSON><PERSON> <PERSON><PERSON> al-Fahm vs Hapoel Tel Aviv", "Hapoel Tel Aviv vs H. Umm al-Fahm", "<PERSON><PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON>", "<PERSON><PERSON> <PERSON> vs <PERSON><PERSON>", "Maccabi Jaffa vs H. Umm al-Fahm", "<PERSON><PERSON> <PERSON><PERSON> al-Fahm vs Maccabi Jaffa", "Maccabi Jaffa vs H. Umm al-Fahm", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>"], "teams_affected": ["Hapoel Afula", "<PERSON><PERSON> <PERSON>", "Maccabi Jaffa", "<PERSON><PERSON>", "Hapoel Raanana", "<PERSON><PERSON><PERSON>", "Hapoel Tel Aviv", "Hapoel Akko", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Bnei <PERSON>"], "total_matches": 534, "valid_matches": 498, "skipped_matches": 36}, "SERBIA_SUPERLIGA": {"warning_count": 28, "missing_matches": ["<PERSON><PERSON> vs <PERSON><PERSON> Topola", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> vs Jedinstvo U.", "Jedinstvo U. vs <PERSON><PERSON>", "<PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs Mladost Lucani", "<PERSON><PERSON> vs Na<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs Novi Pazar", "<PERSON> Pazar vs <PERSON><PERSON>", "Partizan vs <PERSON><PERSON>", "<PERSON><PERSON> vs Partizan", "<PERSON><PERSON><PERSON><PERSON> 1923 vs <PERSON><PERSON>", "<PERSON><PERSON> vs Radnicki 1923", "<PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs Red Star", "Red Star vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs S. Subotica", "<PERSON><PERSON> vs Vojvodina", "Vojvodina vs T<PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>"], "teams_affected": ["Novi Pazar", "Partizan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jedinstvo U.", "Radnicki 1923", "Red Star", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "S. Subotica", "Napredak", "Vojvodina", "<PERSON><PERSON>"], "total_matches": 421, "valid_matches": 393, "skipped_matches": 28}, "UZBEKISTAN_SUPER_LEAGUE": {"warning_count": 5, "missing_matches": ["Shurtan vs Bunyodkor", "Buxoro vs Bunyodkor", "Xorazm vs Bunyodkor", "Bunyodkor vs Kokand-1912", "Pakhtakor vs <PERSON><PERSON>"], "teams_affected": ["<PERSON><PERSON>", "Kokand-1912", "Bunyodkor", "<PERSON><PERSON><PERSON>", "Pakhtakor", "Xorazm", "Shurtan"], "total_matches": 12, "valid_matches": 7, "skipped_matches": 5}, "ICELAND_URVALSDEILD": {"warning_count": 3, "missing_matches": ["Afturelding vs Stjarnan", "IBV vs Vestri", "Vestri vs Afturelding"], "teams_affected": ["IBV", "Vestri", "Afturelding", "Stjar<PERSON>"], "total_matches": 14, "valid_matches": 11, "skipped_matches": 3}, "USA_USL_CHAMPIONSHIP": {"warning_count": 1, "missing_matches": ["Loudoun Utd vs Lexington"], "teams_affected": ["Loudoun Utd", "Lexington"], "total_matches": 33, "valid_matches": 32, "skipped_matches": 1}, "AUSTRIA_BUNDESLIGA_WOMEN": {"warning_count": 16, "missing_matches": ["Austria Wien W vs SCR Altach W", "SCR Altach W vs Austria Wien W", "SCR Altach W vs Bergheim W", "Bergheim W vs SCR Altach W", "Dornbirn W vs SCR Altach W", "SCR Altach W vs Dornbirn W", "SCR Altach W vs First Vienna W", "First Vienna W vs SCR Altach W", "Kleinmunchen W vs SCR Altach W", "SCR Altach W vs Kleinmunchen W", "Neulengbach W vs SCR Altach W", "SCR Altach W vs Neulengbach W", "SCR Altach W vs St. Polten W", "St. Pol<PERSON> W vs SCR Altach W", "Sturm Graz W vs SCR Altach W", "SCR Altach W vs Sturm Graz W"], "teams_affected": ["Austria Wien W", "SCR Altach W", "Bergheim W", "First Vienna W", "Sturm Graz W", "Neulengbach W", "St. Polten W", "Kleinmunchen W", "Dornbirn W"], "total_matches": 144, "valid_matches": 128, "skipped_matches": 16}, "BELGIUM_FIRST_DIVISION_B": {"warning_count": 36, "missing_matches": ["Anderlecht B vs Zulte-Waregem", "Lokeren-Temse vs Anderlecht B", "Zulte-Waregem vs Anderlecht B", "Anderlecht B vs Lokeren-Temse", "Beveren vs Lokeren-Temse", "Zulte-Waregem vs Beveren", "Lokeren-Temse vs Beveren", "Beveren vs Zulte-Waregem", "Club Brugge B vs Lokeren-Temse", "Zulte-Waregem vs Club Brugge B", "Club Brugge B vs Zulte-Waregem", "Lokeren-Temse vs Club Brugge B", "Eupen vs Zulte-Waregem", "Eupen vs Lokeren-Temse", "Zulte-Waregem vs Eupen", "Lokeren-Temse vs Eupen", "Lokeren-Temse vs Francs Borains", "Zulte-Waregem vs Francs Borains", "Francs Borains vs Lokeren-Temse", "Francs Borains vs Zulte-Waregem", "La Louviere vs Lokeren-Temse", "Zulte-Waregem vs La Louviere", "La Louviere vs Zulte-Waregem", "Lokeren-Temse vs La Louviere", "Zulte-Waregem vs Lierse K.", "Lierse K. vs Lokeren-Temse", "Lokeren-Temse vs Lierse K.", "Lierse K. vs Zulte-Waregem", "Lokeren-<PERSON>mse vs Patro <PERSON>", "<PERSON><PERSON> vs Zulte-Waregem", "Zulte-Waregem vs Patro <PERSON>", "<PERSON><PERSON> vs Lokeren-Temse", "Lokeren-<PERSON>ms<PERSON> vs Seraing", "Zulte-Waregem vs Seraing", "Seraing vs Zulte-Waregem", "Seraing vs Lokeren-Temse"], "teams_affected": ["La Louviere", "<PERSON><PERSON><PERSON>", "Anderlecht B", "<PERSON><PERSON> K.", "Club Brugge B", "Francs Bo<PERSON>s", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Lokeren-Temse", "Zulte-Waregem", "Eupen"], "total_matches": 252, "valid_matches": 216, "skipped_matches": 36}, "GERMANY_REGIONALLIGA_SUDWEST": {"warning_count": 27, "missing_matches": ["<PERSON><PERSON><PERSON> vs Fulda-Lehnerz", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "Fulda-Lehnerz vs Eintracht F. B", "Eintracht F. B vs Fulda-Lehnerz", "Fulda-Lehnerz vs Eintracht Trier", "Eintracht Trier vs Fulda-Lehnerz", "Freiberg vs Fulda-Lehnerz", "Fulda-Lehnerz vs Freiberg", "Freiburg B vs Fulda-Lehnerz", "Fulda-Lehnerz vs Freiburg B", "<PERSON><PERSON><PERSON> vs Fulda-Lehnerz", "Fulda-Lehnerz vs <PERSON><PERSON>sen", "Hessen Kassel vs Fulda-Lehnerz", "Fulda-Lehnerz vs Hessen Kassel", "Fulda-Lehnerz vs Hoffenheim B", "Hoffenheim B vs Fulda-Lehnerz", "Homburg vs Fulda-Lehnerz", "Kickers Offenb. vs <PERSON><PERSON><PERSON>Lehnerz", "Fulda-Lehnerz vs Kickers Offenb.", "S. Kickers vs Fulda-Lehnerz", "<PERSON>lda<PERSON><PERSON><PERSON><PERSON><PERSON> vs S. Kickers", "Fulda-Lehnerz vs Steinbach", "Steinbach vs Fulda-Lehnerz", "Fulda-Lehnerz vs Villingen", "Villingen vs Fulda-Lehnerz", "Fulda-Lehnerz vs Walldorf", "Walldorf vs Fulda-Lehnerz"], "teams_affected": ["Eintracht F. B", "Hessen Kassel", "<PERSON><PERSON><PERSON>", "S. Kickers", "Steinbach", "Hoffenheim B", "Fulda-Lehnerz", "Villingen", "Eintracht Trier", "Walldorf", "Kickers Offenb.", "<PERSON><PERSON><PERSON>", "Freiburg B", "<PERSON><PERSON><PERSON>", "Homburg"], "total_matches": 462, "valid_matches": 435, "skipped_matches": 27}, "IRELAND_PREMIER_DIVISION": {"warning_count": 2, "missing_matches": ["Cork City vs Bohemians", "Bohemians vs Cork City"], "teams_affected": ["Cork City", "Bohemians"], "total_matches": 13, "valid_matches": 11, "skipped_matches": 2}, "BELGIUM_U21_PRO_LEAGUE": {"warning_count": 18, "missing_matches": ["Waasland-B. U21 vs Kortrijk U21", "Kortrijk U21 vs Lokeren-T. U21", "Kortrijk U21 vs Waasland-B. U21", "Waasland-B. U21 vs Kortrijk U21", "Kortrijk U21 vs Lokeren-T. U21", "Lokeren-T. U21 vs Kortrijk U21", "Waasland-B. U21 vs Lierse K. U21", "Lokeren-T. U21 vs Lierse K. U21", "Lierse K. U21 vs Waasland-B. U21", "Lierse K. U21 vs Lokeren-T. U21", "Waasland-B. U21 vs Lierse K. U21", "Lokeren-T. U21 vs Lierse K. U21", "Westerlo U21 vs Waasland-B. U21", "Lokeren-T. U21 vs Westerlo U21", "Waasland-B. U21 vs Westerlo U21", "Westerlo U21 vs Lokeren-T. U21", "Westerlo U21 vs Waasland-B. U21", "Lokeren-T. U21 vs Westerlo U21"], "teams_affected": ["Kortrijk U21", "Lokeren-T. U21", "Lierse K. U21", "Waasland-B. U21", "Westerlo U21"], "total_matches": 71, "valid_matches": 53, "skipped_matches": 18}, "ISRAEL_LIGA_ALEF_NORTH": {"warning_count": 15, "missing_matches": ["<PERSON><PERSON> vs <PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs H. Bne<PERSON>", "<PERSON>i Araba vs H. Bnei Zalfa", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON><PERSON>", "Hapoel Bueine vs Ironi Araba", "<PERSON><PERSON> Baka vs <PERSON><PERSON> Araba", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON> vs <PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> Fahm vs <PERSON><PERSON> Araba"], "teams_affected": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON> El Fahm", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>ik", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Hapoel Bueine", "<PERSON><PERSON>", "H. Bnei Zalfa"], "total_matches": 232, "valid_matches": 217, "skipped_matches": 15}, "GEORGIA_EROVNULI_LIGA": {"warning_count": 4, "missing_matches": ["<PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> vs <PERSON><PERSON>", "<PERSON><PERSON><PERSON> vs Gagra", "<PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON>"], "teams_affected": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Gagra", "<PERSON><PERSON><PERSON>"], "total_matches": 41, "valid_matches": 37, "skipped_matches": 4}, "BRAZIL_TOCANTINENSE": {"warning_count": 1, "missing_matches": ["<PERSON><PERSON><PERSON> vs Ava<PERSON>"], "teams_affected": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "total_matches": 5, "valid_matches": 4, "skipped_matches": 1}}