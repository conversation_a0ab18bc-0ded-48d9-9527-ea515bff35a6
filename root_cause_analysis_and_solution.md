# Root Cause Analysis: Missing Team Stats Warnings

## Executive Summary

The investigation reveals that **93% of missing team stats warnings (51 out of 55 cases)** are caused by **case sensitivity differences** between team names in different data sources. This is a systematic issue affecting 202 out of 288 successful leagues (70.1%).

## Root Cause Identified

### The Problem
The feature engineering process performs **exact string matching** to find team statistics:

```python
# From src/feature_engineering/utils.py line 30-34
home_team_stats = team_stats[team_stats["Team"] == home_team]
away_team_stats = team_stats[team_stats["Team"] == away_team]

if home_team_stats.empty or away_team_stats.empty:
    logger.warning(f"Missing team stats for match: {home_team} vs {away_team}")
    return None, None
```

### Data Source Inconsistency

**Two different data sources with different case handling:**

1. **FootyStats** (for match results) → Preserves original case
   - Examples: `"NK Celje"`, `"FC Utrecht"`, `"ADO Den Haag"`

2. **SoccerStats** (for team statistics) → Converts to title case
   - Examples: `"Nk <PERSON>lje"`, `"Fc Utrecht"`, `"Ado Den Haag"`

### Evidence from Investigation

| League | Case Differences | Total Mismatches | Percentage |
|--------|------------------|------------------|------------|
| NETHERLANDS_EERSTE_DIVISIE | 12/12 | 12 | 100% |
| NETHERLANDS_DERDE_DIVISIE_SATURDAY | 8/8 | 8 | 100% |
| NETHERLANDS_TWEEDE_DIVISIE | 7/7 | 7 | 100% |
| NETHERLANDS_EREDIVISIE | 10/10 | 10 | 100% |
| SLOVENIA_PRVA_LIGA | 2/2 | 2 | 100% |
| GERMANY_OBERLIGA_HAMBURG | 4/6 | 6 | 67% |
| ARGENTINA_PRIMERA_NACIONAL | 5/5 | 5 | 100% |
| SPAIN_SEGUNDA_RFEF_GROUP_5 | 3/5 | 5 | 60% |

**Total: 51 case differences out of 55 total mismatches (93%)**

## Technical Flow Analysis

### 1. Team Statistics Scraping (SoccerStats)
```python
# From src/scrapers/main.py line 233-236
for team, url in team_urls.items():
    team_stats = get_team_stats(url, team, TEAM_SCRAPER)
    team_stats_dict[team] = team_stats
save_team_stats_to_csv(team_stats_dict, league_name, f"{league_name}_team_stats.csv")
```

- Uses team names from `TEAM_URLS` configuration (SoccerStats format)
- Team names are stored as keys in the dictionary
- Saved to CSV with these exact team names

### 2. Match Results Scraping (FootyStats)
```python
# From src/scrapers/recent_results_scraper.py line 104-109
home_team_raw = " ".join(teams[0].get_text(strip=True).split())
away_team_raw = " ".join(teams[1].get_text(strip=True).split())

home_team, home_score = extract_team_and_score(home_team_raw)
away_team, away_score = extract_team_and_score(away_team_raw)
```

- Extracts team names directly from FootyStats HTML
- Preserves original case from the website
- No normalization applied

### 3. Feature Engineering Validation
```python
# From src/feature_engineering/utils.py line 30-34
home_team_stats = team_stats[team_stats["Team"] == home_team]
away_team_stats = team_stats[team_stats["Team"] == away_team]
```

- Performs exact string matching (`==`)
- No case-insensitive comparison
- Fails when `"NK Celje"` != `"Nk Celje"`

## Geographic Pattern Analysis

### Netherlands (Systematic Issue)
- **4 leagues** in top 10 by warnings
- **100% case sensitivity issues** in all investigated leagues
- Suggests FootyStats uses different case handling for Dutch teams

### Other Affected Regions
- **Germany**: Oberliga leagues affected
- **Spain**: Segunda RFEF groups affected  
- **Slovenia**: Prva Liga affected
- **Argentina**: Primera Nacional affected

## Impact Assessment

### Quantitative Impact
- **202 leagues affected** (70.1% of successful leagues)
- **9,798 total warnings** generated
- **Average 24.9% skip rate** across affected leagues
- **2,460 unique teams** impacted

### Qualitative Impact
- **Reduced prediction accuracy** due to missing team statistics
- **Data quality degradation** in feature engineering
- **Inconsistent model performance** across leagues
- **Maintenance overhead** for manual fixes

## Solution Strategy

### Immediate Fix (High Priority)
1. **Implement case-insensitive matching** in feature engineering
2. **Add fuzzy matching fallback** for team name variations
3. **Create team name normalization** pipeline

### Medium-term Improvements
1. **Standardize team name handling** across all scrapers
2. **Implement team name mapping** system
3. **Add data validation** during scraping process

### Long-term Architecture
1. **Create master team database** with aliases
2. **Implement automated name resolution**
3. **Build redundant data source architecture**

## Recommended Implementation

### Phase 1: Quick Fix (Case-Insensitive Matching)
```python
# Modify src/feature_engineering/utils.py
def validate_team_stats_case_insensitive(home_team, away_team, team_stats):
    # Try exact match first
    home_stats = team_stats[team_stats["Team"] == home_team]
    away_stats = team_stats[team_stats["Team"] == away_team]
    
    if not home_stats.empty and not away_stats.empty:
        return home_stats.iloc[0], away_stats.iloc[0]
    
    # Fallback to case-insensitive matching
    team_stats_lower = team_stats.copy()
    team_stats_lower["Team_Lower"] = team_stats_lower["Team"].str.lower()
    
    home_stats = team_stats_lower[team_stats_lower["Team_Lower"] == home_team.lower()]
    away_stats = team_stats_lower[team_stats_lower["Team_Lower"] == away_team.lower()]
    
    if home_stats.empty or away_stats.empty:
        logger.warning(f"Missing team stats for match: {home_team} vs {away_team}")
        return None, None
    
    return home_stats.iloc[0], away_stats.iloc[0]
```

### Phase 2: Systematic Fix (Data Source Standardization)
1. Normalize team names during scraping
2. Apply consistent case handling
3. Implement team name mapping system

This solution would immediately resolve 93% of the missing team stats warnings and significantly improve the prediction system's data quality.
