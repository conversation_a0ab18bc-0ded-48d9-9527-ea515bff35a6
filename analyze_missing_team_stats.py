#!/usr/bin/env python3
"""
Script to analyze league testing log file and identify leagues with missing team stats warnings.
"""

import re
import json
from collections import defaultdict
from pathlib import Path

def analyze_log_file(log_file_path):
    """
    Analyze the log file to find leagues with missing team stats warnings.
    
    Returns:
        dict: Dictionary with league names as keys and warning details as values
    """
    leagues_with_warnings = defaultdict(lambda: {
        'warning_count': 0,
        'missing_matches': [],
        'teams_affected': set(),
        'total_matches': 0,
        'valid_matches': 0,
        'skipped_matches': 0
    })
    
    current_league = None
    
    # Patterns to match
    league_pattern = r'Successfully loaded all data files for league: (.+)'
    warning_pattern = r'WARNING - Missing team stats for match: (.+)'
    processing_pattern = r'Processing (\d+) valid matches\. Skipped (\d+) matches\.'
    feature_prep_pattern = r'Starting feature preparation for (\d+) matches'
    
    try:
        with open(log_file_path, 'r', encoding='utf-8') as f:
            for line in f:
                # Check for league identification
                league_match = re.search(league_pattern, line)
                if league_match:
                    current_league = league_match.group(1)
                    continue
                
                # Check for total matches in feature preparation
                feature_match = re.search(feature_prep_pattern, line)
                if feature_match and current_league:
                    leagues_with_warnings[current_league]['total_matches'] = int(feature_match.group(1))
                    continue
                
                # Check for missing team stats warnings
                warning_match = re.search(warning_pattern, line)
                if warning_match and current_league:
                    match_info = warning_match.group(1)
                    leagues_with_warnings[current_league]['warning_count'] += 1
                    leagues_with_warnings[current_league]['missing_matches'].append(match_info)
                    
                    # Extract team names from match
                    if ' vs ' in match_info:
                        teams = match_info.split(' vs ')
                        for team in teams:
                            leagues_with_warnings[current_league]['teams_affected'].add(team.strip())
                    continue
                
                # Check for processing summary
                processing_match = re.search(processing_pattern, line)
                if processing_match and current_league:
                    leagues_with_warnings[current_league]['valid_matches'] = int(processing_match.group(1))
                    leagues_with_warnings[current_league]['skipped_matches'] = int(processing_match.group(2))
                    continue
    
    except FileNotFoundError:
        print(f"Error: Log file not found at {log_file_path}")
        return {}
    except Exception as e:
        print(f"Error reading log file: {e}")
        return {}
    
    # Convert sets to lists for JSON serialization
    for league_data in leagues_with_warnings.values():
        league_data['teams_affected'] = list(league_data['teams_affected'])
    
    # Filter out leagues with no warnings
    return {league: data for league, data in leagues_with_warnings.items() if data['warning_count'] > 0}

def generate_report(leagues_with_warnings, output_file):
    """Generate a detailed report of leagues with missing team stats warnings."""
    
    # Sort leagues by warning count (descending)
    sorted_leagues = sorted(leagues_with_warnings.items(), 
                          key=lambda x: x[1]['warning_count'], 
                          reverse=True)
    
    total_leagues_with_warnings = len(sorted_leagues)
    total_warnings = sum(data['warning_count'] for data in leagues_with_warnings.values())
    
    report = []
    report.append("=" * 80)
    report.append("MISSING TEAM STATS ANALYSIS REPORT")
    report.append("=" * 80)
    report.append(f"Analysis Date: {Path(__file__).stat().st_mtime}")
    report.append(f"Log File Analyzed: logs/league_testing_20250718_145701.log")
    report.append("")
    report.append("SUMMARY:")
    report.append(f"  • Total leagues tested: 314")
    report.append(f"  • Successful leagues: 288")
    report.append(f"  • Leagues with missing team stats warnings: {total_leagues_with_warnings}")
    report.append(f"  • Total missing team stats warnings: {total_warnings}")
    report.append(f"  • Percentage of successful leagues with warnings: {total_leagues_with_warnings/288*100:.1f}%")
    report.append("")
    report.append("=" * 80)
    report.append("DETAILED BREAKDOWN BY LEAGUE")
    report.append("=" * 80)
    
    for i, (league, data) in enumerate(sorted_leagues, 1):
        report.append("")
        report.append(f"{i}. {league}")
        report.append("-" * len(f"{i}. {league}"))
        report.append(f"   Warning Count: {data['warning_count']}")
        report.append(f"   Total Matches: {data['total_matches']}")
        report.append(f"   Valid Matches: {data['valid_matches']}")
        report.append(f"   Skipped Matches: {data['skipped_matches']}")
        
        if data['skipped_matches'] > 0 and data['total_matches'] > 0:
            skip_percentage = (data['skipped_matches'] / data['total_matches']) * 100
            report.append(f"   Skip Percentage: {skip_percentage:.1f}%")
        
        report.append(f"   Teams Affected: {len(data['teams_affected'])}")
        if data['teams_affected']:
            teams_str = ", ".join(sorted(data['teams_affected']))
            report.append(f"   Team Names: {teams_str}")
        
        # Show first few missing matches as examples
        if data['missing_matches']:
            report.append(f"   Sample Missing Matches:")
            for match in data['missing_matches'][:5]:  # Show first 5
                report.append(f"     • {match}")
            if len(data['missing_matches']) > 5:
                report.append(f"     ... and {len(data['missing_matches']) - 5} more")
    
    # Write report to file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    return report

def generate_summary_analysis(leagues_with_warnings):
    """Generate additional summary analysis."""

    # Calculate statistics
    total_leagues = len(leagues_with_warnings)
    total_warnings = sum(data['warning_count'] for data in leagues_with_warnings.values())

    # Analyze skip percentages
    skip_percentages = []
    for data in leagues_with_warnings.values():
        if data['total_matches'] > 0:
            skip_pct = (data['skipped_matches'] / data['total_matches']) * 100
            skip_percentages.append(skip_pct)

    avg_skip_pct = sum(skip_percentages) / len(skip_percentages) if skip_percentages else 0

    # Find leagues with highest skip percentages
    leagues_by_skip_pct = []
    for league, data in leagues_with_warnings.items():
        if data['total_matches'] > 0:
            skip_pct = (data['skipped_matches'] / data['total_matches']) * 100
            leagues_by_skip_pct.append((league, skip_pct, data['warning_count']))

    leagues_by_skip_pct.sort(key=lambda x: x[1], reverse=True)

    # Analyze team patterns
    all_teams = set()
    team_frequency = defaultdict(int)

    for data in leagues_with_warnings.values():
        for team in data['teams_affected']:
            all_teams.add(team)
            team_frequency[team] += 1

    # Find most problematic teams (appearing in multiple leagues)
    problematic_teams = [(team, count) for team, count in team_frequency.items() if count > 1]
    problematic_teams.sort(key=lambda x: x[1], reverse=True)

    return {
        'total_leagues': total_leagues,
        'total_warnings': total_warnings,
        'avg_skip_percentage': avg_skip_pct,
        'leagues_by_skip_pct': leagues_by_skip_pct[:20],  # Top 20
        'total_unique_teams': len(all_teams),
        'problematic_teams': problematic_teams[:20]  # Top 20
    }

def main():
    log_file_path = "logs/league_testing_20250718_145701.log"
    output_file = "missing_team_stats_analysis.txt"
    json_output_file = "missing_team_stats_data.json"
    summary_file = "missing_team_stats_summary.txt"

    print("Analyzing log file for missing team stats warnings...")
    print(f"Log file: {log_file_path}")

    # Analyze the log file
    leagues_with_warnings = analyze_log_file(log_file_path)

    if not leagues_with_warnings:
        print("No leagues with missing team stats warnings found.")
        return

    print(f"Found {len(leagues_with_warnings)} leagues with missing team stats warnings.")

    # Generate detailed report
    print(f"Generating detailed report: {output_file}")
    report = generate_report(leagues_with_warnings, output_file)

    # Generate summary analysis
    print(f"Generating summary analysis: {summary_file}")
    summary_data = generate_summary_analysis(leagues_with_warnings)

    # Write summary analysis
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write("MISSING TEAM STATS - SUMMARY ANALYSIS\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"Total leagues with warnings: {summary_data['total_leagues']}\n")
        f.write(f"Total warnings: {summary_data['total_warnings']}\n")
        f.write(f"Average skip percentage: {summary_data['avg_skip_percentage']:.1f}%\n")
        f.write(f"Total unique teams affected: {summary_data['total_unique_teams']}\n\n")

        f.write("TOP 20 LEAGUES BY SKIP PERCENTAGE:\n")
        f.write("-" * 40 + "\n")
        for i, (league, skip_pct, warnings) in enumerate(summary_data['leagues_by_skip_pct'], 1):
            f.write(f"{i:2d}. {league}: {skip_pct:.1f}% ({warnings} warnings)\n")

        f.write(f"\nTOP 20 MOST PROBLEMATIC TEAMS (appearing in multiple leagues):\n")
        f.write("-" * 60 + "\n")
        for i, (team, count) in enumerate(summary_data['problematic_teams'], 1):
            f.write(f"{i:2d}. {team}: appears in {count} leagues\n")

    # Save raw data as JSON for further analysis
    print(f"Saving raw data: {json_output_file}")
    with open(json_output_file, 'w', encoding='utf-8') as f:
        json.dump(leagues_with_warnings, f, indent=2, ensure_ascii=False)

    # Print summary to console
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    print(f"Leagues with warnings: {len(leagues_with_warnings)}")
    print(f"Total warnings: {sum(data['warning_count'] for data in leagues_with_warnings.values())}")
    print(f"Average skip percentage: {summary_data['avg_skip_percentage']:.1f}%")

    # Show top 10 leagues with most warnings
    sorted_leagues = sorted(leagues_with_warnings.items(),
                          key=lambda x: x[1]['warning_count'],
                          reverse=True)

    print(f"\nTop 10 leagues with most warnings:")
    for i, (league, data) in enumerate(sorted_leagues[:10], 1):
        print(f"  {i:2d}. {league}: {data['warning_count']} warnings")

    print(f"\nTop 10 leagues with highest skip percentage:")
    for i, (league, skip_pct, warnings) in enumerate(summary_data['leagues_by_skip_pct'][:10], 1):
        print(f"  {i:2d}. {league}: {skip_pct:.1f}% skip rate")

    print(f"\nDetailed analysis saved to: {output_file}")
    print(f"Summary analysis saved to: {summary_file}")
    print(f"Raw data saved to: {json_output_file}")

if __name__ == "__main__":
    main()
