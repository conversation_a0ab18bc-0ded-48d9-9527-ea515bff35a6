#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix the case sensitivity issue in team name matching.
This implements a case-insensitive fallback in the feature engineering validation.
"""

import os
import shutil
from pathlib import Path

def backup_original_file():
    """Create a backup of the original utils.py file."""
    original_file = "src/feature_engineering/utils.py"
    backup_file = "src/feature_engineering/utils.py.backup"
    
    if os.path.exists(original_file):
        shutil.copy2(original_file, backup_file)
        print(f"✅ Created backup: {backup_file}")
        return True
    else:
        print(f"❌ Original file not found: {original_file}")
        return False

def create_improved_validation_function():
    """Create the improved validation function with case-insensitive fallback."""
    
    improved_function = '''def validate_team_stats(
    home_team: str,
    away_team: str,
    team_stats: pd.DataFrame
) -> Tuple[Optional[pd.Series], Optional[pd.Series]]:
    """
    Validate and retrieve team statistics with case-insensitive fallback.

    Args:
        home_team: Name of home team
        away_team: Name of away team
        team_stats: DataFrame containing team statistics

    Returns:
        Tuple of (home_stats, away_stats) or (None, None) if validation fails
    """
    try:
        # First attempt: Exact string matching (original behavior)
        home_team_stats = team_stats[team_stats["Team"] == home_team]
        away_team_stats = team_stats[team_stats["Team"] == away_team]

        if not home_team_stats.empty and not away_team_stats.empty:
            return home_team_stats.iloc[0], away_team_stats.iloc[0]

        # Second attempt: Case-insensitive matching
        home_team_stats_ci = team_stats[team_stats["Team"].str.lower() == home_team.lower()]
        away_team_stats_ci = team_stats[team_stats["Team"].str.lower() == away_team.lower()]

        if not home_team_stats_ci.empty and not away_team_stats_ci.empty:
            logger.info(f"Found teams using case-insensitive matching: {home_team} vs {away_team}")
            return home_team_stats_ci.iloc[0], away_team_stats_ci.iloc[0]

        # Third attempt: Fuzzy matching for common variations
        home_team_fuzzy = _find_fuzzy_team_match(home_team, team_stats)
        away_team_fuzzy = _find_fuzzy_team_match(away_team, team_stats)

        if home_team_fuzzy is not None and away_team_fuzzy is not None:
            home_team_stats_fuzzy = team_stats[team_stats["Team"] == home_team_fuzzy]
            away_team_stats_fuzzy = team_stats[team_stats["Team"] == away_team_fuzzy]
            
            if not home_team_stats_fuzzy.empty and not away_team_stats_fuzzy.empty:
                logger.info(f"Found teams using fuzzy matching: {home_team}→{home_team_fuzzy}, {away_team}→{away_team_fuzzy}")
                return home_team_stats_fuzzy.iloc[0], away_team_stats_fuzzy.iloc[0]

        # If all attempts fail, log warning
        logger.warning(f"Missing team stats for match: {home_team} vs {away_team}")
        return None, None

    except Exception as e:
        logger.error(f"Error validating team stats: {str(e)}")
        return None, None

def _find_fuzzy_team_match(target_team: str, team_stats: pd.DataFrame) -> Optional[str]:
    """
    Find fuzzy match for team name to handle common variations.
    
    Args:
        target_team: Team name to find match for
        team_stats: DataFrame containing team statistics
        
    Returns:
        Matched team name or None if no match found
    """
    if team_stats.empty or "Team" not in team_stats.columns:
        return None
    
    available_teams = team_stats["Team"].tolist()
    target_normalized = _normalize_team_name(target_team)
    
    # Check for normalized matches
    for team in available_teams:
        if _normalize_team_name(team) == target_normalized:
            return team
    
    # Check for partial matches (one name contains the other)
    target_lower = target_team.lower()
    for team in available_teams:
        team_lower = team.lower()
        if (target_lower in team_lower and len(target_lower) > 3) or \\
           (team_lower in target_lower and len(team_lower) > 3):
            return team
    
    return None

def _normalize_team_name(team_name: str) -> str:
    """
    Normalize team name for comparison by removing common variations.
    
    Args:
        team_name: Original team name
        
    Returns:
        Normalized team name
    """
    import re
    
    if not team_name:
        return ""
    
    # Convert to lowercase and strip whitespace
    normalized = team_name.lower().strip()
    
    # Remove common prefixes
    prefixes = ['fc ', 'ac ', 'sc ', 'cf ', 'cd ', 'nk ', 'fk ', 'ks ', 'kf ']
    for prefix in prefixes:
        if normalized.startswith(prefix):
            normalized = normalized[len(prefix):].strip()
            break
    
    # Remove common suffixes
    suffixes = [' fc', ' ac', ' sc', ' cf', ' cd']
    for suffix in suffixes:
        if normalized.endswith(suffix):
            normalized = normalized[:-len(suffix)].strip()
            break
    
    # Remove special characters and normalize spacing
    normalized = re.sub(r'[^\w\s]', '', normalized)
    normalized = re.sub(r'\s+', ' ', normalized.strip())
    
    return normalized'''
    
    return improved_function

def apply_fix():
    """Apply the case sensitivity fix to the utils.py file."""
    
    utils_file = "src/feature_engineering/utils.py"
    
    if not os.path.exists(utils_file):
        print(f"❌ File not found: {utils_file}")
        return False
    
    # Read the current file
    with open(utils_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find the validate_team_stats function
    import re
    
    # Pattern to match the entire function
    pattern = r'def validate_team_stats\([^)]*\):[^}]*?return None, None'
    
    # Find the function in the content
    match = re.search(pattern, content, re.DOTALL)
    
    if not match:
        print("❌ Could not find validate_team_stats function to replace")
        return False
    
    # Get the improved function
    improved_function = create_improved_validation_function()
    
    # Replace the function
    new_content = content[:match.start()] + improved_function + content[match.end():]
    
    # Add the helper functions if they don't exist
    if '_find_fuzzy_team_match' not in content:
        # Find a good place to insert the helper functions (before the last function or at the end)
        insert_pos = new_content.rfind('\ndef ')
        if insert_pos == -1:
            insert_pos = len(new_content)
        else:
            # Find the end of the last function
            insert_pos = new_content.find('\n\n', insert_pos)
            if insert_pos == -1:
                insert_pos = len(new_content)
        
        # The helper functions are already included in the improved_function string above
        # So we don't need to add them separately
    
    # Write the updated content
    with open(utils_file, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"✅ Applied case sensitivity fix to {utils_file}")
    return True

def test_fix():
    """Test the fix by running a simple validation."""
    print("\n🧪 Testing the fix...")
    
    try:
        # Import the updated module
        import sys
        sys.path.insert(0, 'src')
        
        from feature_engineering.utils import validate_team_stats
        import pandas as pd
        
        # Create test data
        team_stats = pd.DataFrame({
            'Team': ['Nk Celje', 'Nk Olimpija', 'Fc Utrecht', 'Ado Den Haag'],
            'goals_scored': [10, 15, 12, 8],
            'goals_conceded': [5, 8, 6, 10]
        })
        
        # Test case-insensitive matching
        home_stats, away_stats = validate_team_stats('NK Celje', 'NK Olimpija', team_stats)
        
        if home_stats is not None and away_stats is not None:
            print("✅ Case-insensitive matching works!")
            print(f"   Found: {home_stats['Team']} vs {away_stats['Team']}")
        else:
            print("❌ Case-insensitive matching failed")
            
        # Test fuzzy matching
        home_stats, away_stats = validate_team_stats('FC Utrecht', 'ADO Den Haag', team_stats)
        
        if home_stats is not None and away_stats is not None:
            print("✅ Fuzzy matching works!")
            print(f"   Found: {home_stats['Team']} vs {away_stats['Team']}")
        else:
            print("❌ Fuzzy matching failed")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")

def main():
    """Main function to apply the case sensitivity fix."""
    
    print("FIXING CASE SENSITIVITY ISSUE IN TEAM NAME MATCHING")
    print("=" * 60)
    print("This script will modify src/feature_engineering/utils.py to add")
    print("case-insensitive and fuzzy matching for team names.")
    print()
    
    # Create backup
    if not backup_original_file():
        return
    
    # Apply the fix
    if apply_fix():
        print("✅ Fix applied successfully!")
        
        # Test the fix
        test_fix()
        
        print("\n📊 Expected Impact:")
        print("   - Should resolve ~93% of missing team stats warnings")
        print("   - Affects 202 leagues with case sensitivity issues")
        print("   - Will reduce skip rates significantly")
        
        print("\n🔄 Next Steps:")
        print("   1. Run test_all_leagues.py to verify the fix")
        print("   2. Monitor logs for remaining warnings")
        print("   3. Consider implementing data source standardization")
        
    else:
        print("❌ Failed to apply fix")

if __name__ == "__main__":
    main()
