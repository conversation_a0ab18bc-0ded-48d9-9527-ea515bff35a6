#!/usr/bin/env python3
"""
Test the enhanced fuzzy matching on problematic leagues.
"""

import sys
import os
sys.path.insert(0, 'src')

import pandas as pd
from feature_engineering.utils import validate_team_stats, _normalize_team_name, _find_fuzzy_team_match
import logging

# Set up logging to see the info messages
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')

def test_vietnam_v_league_2():
    """Test enhanced matching on Vietnam V League 2 - the most problematic case."""
    
    print("🇻🇳 TESTING VIETNAM V LEAGUE 2")
    print("=" * 50)
    
    # Real data from our investigation
    teams_in_results = ['<PERSON> Chi Minh B', 'PVF-CAND', 'S. Khanh Hoa', '<PERSON> Thap']
    teams_in_stats = ['Da Nang', 'Phu Tho', 'Pvf Cand', 'Binh Thuan']
    
    # Create test DataFrame
    team_stats = pd.DataFrame({
        'Team': teams_in_stats,
        'goals_scored': [10, 15, 12, 8],
        'goals_conceded': [5, 8, 6, 10]
    })
    
    print(f"📊 Test Setup:")
    print(f"   Teams in results: {teams_in_results}")
    print(f"   Teams in stats: {teams_in_stats}")
    print()
    
    successful_matches = 0
    
    for target_team in teams_in_results:
        print(f"🔍 Testing: '{target_team}'")
        
        # Test normalization
        normalized = _normalize_team_name(target_team)
        print(f"   Normalized: '{normalized}'")
        
        # Test fuzzy matching
        match = _find_fuzzy_team_match(target_team, team_stats)
        
        if match:
            successful_matches += 1
            print(f"   ✅ MATCH FOUND: '{target_team}' → '{match}'")
            
            # Show normalization comparison
            match_normalized = _normalize_team_name(match)
            print(f"   Comparison: '{normalized}' vs '{match_normalized}'")
        else:
            print(f"   ❌ NO MATCH FOUND")
            
            # Show what each available team normalizes to
            print(f"   Available options:")
            for available_team in teams_in_stats:
                available_normalized = _normalize_team_name(available_team)
                print(f"     '{available_team}' → '{available_normalized}'")
        
        print()
    
    print(f"📊 Vietnam V League 2 Results:")
    print(f"   Successful matches: {successful_matches}/{len(teams_in_results)}")
    print(f"   Success rate: {successful_matches/len(teams_in_results)*100:.1f}%")
    
    return successful_matches == len(teams_in_results)

def test_france_national_2():
    """Test enhanced matching on France National 2 - punctuation issues."""
    
    print("\n🇫🇷 TESTING FRANCE NATIONAL 2 GROUP B")
    print("=" * 50)
    
    # Real data from our investigation
    teams_in_results = ['St-Pryve St-H.', 'Saint-Malo', 'Le Poiré SV', 'St Co Locmine']
    teams_in_stats = ['Saint Colomban ', 'Saint Malo', 'Le Poir%E9 Sv', 'St Pryve St H.']
    
    # Create test DataFrame
    team_stats = pd.DataFrame({
        'Team': teams_in_stats,
        'goals_scored': [10, 15, 12, 8],
        'goals_conceded': [5, 8, 6, 10]
    })
    
    print(f"📊 Test Setup:")
    print(f"   Teams in results: {teams_in_results}")
    print(f"   Teams in stats: {teams_in_stats}")
    print()
    
    successful_matches = 0
    
    for target_team in teams_in_results:
        print(f"🔍 Testing: '{target_team}'")
        
        # Test normalization
        normalized = _normalize_team_name(target_team)
        print(f"   Normalized: '{normalized}'")
        
        # Test fuzzy matching
        match = _find_fuzzy_team_match(target_team, team_stats)
        
        if match:
            successful_matches += 1
            print(f"   ✅ MATCH FOUND: '{target_team}' → '{match}'")
            
            # Show normalization comparison
            match_normalized = _normalize_team_name(match)
            print(f"   Comparison: '{normalized}' vs '{match_normalized}'")
        else:
            print(f"   ❌ NO MATCH FOUND")
            
            # Show what each available team normalizes to
            print(f"   Available options:")
            for available_team in teams_in_stats:
                available_normalized = _normalize_team_name(available_team)
                print(f"     '{available_team}' → '{available_normalized}'")
        
        print()
    
    print(f"📊 France National 2 Results:")
    print(f"   Successful matches: {successful_matches}/{len(teams_in_results)}")
    print(f"   Success rate: {successful_matches/len(teams_in_results)*100:.1f}%")
    
    return successful_matches == len(teams_in_results)

def test_with_real_data():
    """Test with actual league data if available."""
    
    print("\n🏆 TESTING WITH REAL LEAGUE DATA")
    print("=" * 40)
    
    # Test Vietnam V League 2
    league_name = "VIETNAM_V_LEAGUE_2"
    data_dir = f"data/raw/{league_name}"
    
    results_file = os.path.join(data_dir, f"{league_name}_results.csv")
    team_stats_file = os.path.join(data_dir, f"{league_name}_team_stats.csv")
    
    if os.path.exists(results_file) and os.path.exists(team_stats_file):
        try:
            results_df = pd.read_csv(results_file)
            team_stats_df = pd.read_csv(team_stats_file)
            
            # Get teams from results
            teams_in_results = set()
            if 'Home Team' in results_df.columns and 'Away Team' in results_df.columns:
                teams_in_results.update(results_df['Home Team'].dropna().unique())
                teams_in_results.update(results_df['Away Team'].dropna().unique())
            
            # Get teams from stats
            teams_in_stats = set()
            if 'Team' in team_stats_df.columns:
                teams_in_stats.update(team_stats_df['Team'].dropna().unique())
            
            missing_teams = teams_in_results - teams_in_stats
            
            print(f"📊 Real Data Analysis for {league_name}:")
            print(f"   Teams in results: {len(teams_in_results)}")
            print(f"   Teams in stats: {len(teams_in_stats)}")
            print(f"   Missing teams: {len(missing_teams)}")
            
            if missing_teams:
                print(f"   Testing enhanced matching on missing teams...")
                
                successful_matches = 0
                for missing_team in list(missing_teams)[:5]:  # Test first 5
                    match = _find_fuzzy_team_match(missing_team, team_stats_df)
                    if match:
                        successful_matches += 1
                        print(f"   ✅ '{missing_team}' → '{match}'")
                    else:
                        print(f"   ❌ '{missing_team}' → No match")
                
                print(f"   Enhanced matching success: {successful_matches}/{min(5, len(missing_teams))}")
                return successful_matches > 0
            else:
                print(f"   ✅ No missing teams found!")
                return True
                
        except Exception as e:
            print(f"❌ Error testing real data: {e}")
            return False
    else:
        print(f"⚠️  Real data files not found for {league_name}")
        return False

def main():
    """Main test function."""
    
    print("TESTING ENHANCED FUZZY MATCHING")
    print("=" * 60)
    print("Testing the enhanced name normalization and fuzzy matching")
    print("on the most problematic remaining cases after case sensitivity fix.")
    print()
    
    # Test synthetic cases
    vietnam_success = test_vietnam_v_league_2()
    france_success = test_france_national_2()
    
    # Test with real data
    real_data_success = test_with_real_data()
    
    print("\n" + "=" * 60)
    print("FINAL TEST RESULTS")
    print("=" * 60)
    
    if vietnam_success:
        print("✅ Vietnam V League 2 tests: PASSED")
    else:
        print("❌ Vietnam V League 2 tests: FAILED")
    
    if france_success:
        print("✅ France National 2 tests: PASSED")
    else:
        print("❌ France National 2 tests: FAILED")
    
    if real_data_success:
        print("✅ Real data tests: PASSED")
    elif real_data_success is False:
        print("❌ Real data tests: FAILED")
    else:
        print("⚠️  Real data tests: SKIPPED (data not available)")
    
    overall_success = vietnam_success and france_success
    
    if overall_success:
        print("\n🎉 Enhanced fuzzy matching is working!")
        print("   Ready to test on the full dataset.")
        print("   Expected to resolve 25-30% of remaining warnings.")
    else:
        print("\n⚠️  Enhanced matching needs further refinement.")
        print("   Some patterns may require additional handling.")

if __name__ == "__main__":
    main()
