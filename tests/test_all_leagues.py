#!/usr/bin/env python3
"""
Comprehensive League Testing Suite
==================================

This script tests all 314 leagues to identify issues with:
- Data loading
- Team name mappings
- Missing statistics
- Feature preparation
- Model training
- Prediction generation

Usage:
    python test_all_leagues.py --mode quick     # Quick validation only
    python test_all_leagues.py --mode full      # Full prediction test
    python test_all_leagues.py --league EPL     # Test specific league
    python test_all_leagues.py --resume         # Resume from last failure
"""

import sys
import os
import logging
import json
import traceback
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
import pandas as pd

# Add src to path
sys.path.append('src')

from data_loading import load_data, get_available_leagues
from scrapers.config import LEAGUE_CONFIGS
from feature_engineering import prepare_features
from model_training import train_model
from prediction import predict_match

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/league_testing_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class LeagueTestResult:
    """Class to store test results for a league."""
    
    def __init__(self, league_name: str):
        self.league_name = league_name
        self.data_loading = {"status": "not_tested", "error": None, "details": {}}
        self.feature_preparation = {"status": "not_tested", "error": None, "details": {}}
        self.model_training = {"status": "not_tested", "error": None, "details": {}}
        self.prediction_test = {"status": "not_tested", "error": None, "details": {}}
        self.overall_status = "not_tested"
        self.issues = []
        self.warnings = []
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        # Create a copy and remove non-serializable objects
        def clean_dict(d):
            if isinstance(d, dict):
                cleaned = {}
                for k, v in d.items():
                    if k in ['data', 'prepared_data', 'models']:
                        # Skip non-serializable objects
                        continue
                    elif isinstance(v, dict):
                        cleaned[k] = clean_dict(v)
                    else:
                        cleaned[k] = v
                return cleaned
            return d
        
        return {
            "league_name": self.league_name,
            "data_loading": clean_dict(self.data_loading),
            "feature_preparation": clean_dict(self.feature_preparation),
            "model_training": clean_dict(self.model_training),
            "prediction_test": clean_dict(self.prediction_test),
            "overall_status": self.overall_status,
            "issues": self.issues,
            "warnings": self.warnings
        }

class LeagueTester:
    """Main class for testing leagues."""
    
    def __init__(self, mode: str = "quick"):
        self.mode = mode  # "quick" or "full"
        self.results = {}
        self.failed_leagues = []
        self.successful_leagues = []
        
        # Create organized directory structure
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.base_log_dir = f"logs/league_testing_{self.timestamp}"
        self.summary_file = os.path.join(self.base_log_dir, "summary.json")
        
        # Create base directory
        os.makedirs(self.base_log_dir, exist_ok=True)
        
        logger.info(f"📁 Results will be saved to: {self.base_log_dir}")
        
    def test_all_leagues(self, resume_from: Optional[str] = None) -> Dict[str, LeagueTestResult]:
        """Test all available leagues."""
        logger.info("=" * 80)
        logger.info("🏆 COMPREHENSIVE LEAGUE TESTING SUITE")
        logger.info("=" * 80)
        logger.info(f"Mode: {self.mode.upper()}")
        logger.info(f"Started at: {datetime.now()}")
        
        available_leagues = get_available_leagues()
        logger.info(f"Found {len(available_leagues)} leagues to test")
        
        # Resume functionality
        start_index = 0
        if resume_from:
            try:
                start_index = available_leagues.index(resume_from)
                logger.info(f"Resuming from league: {resume_from} (index {start_index})")
            except ValueError:
                logger.warning(f"Resume league '{resume_from}' not found, starting from beginning")
        
        for i, league_name in enumerate(available_leagues[start_index:], start_index + 1):
            logger.info(f"\n{'='*60}")
            logger.info(f"🔍 Testing League {i}/{len(available_leagues)}: {league_name}")
            logger.info(f"{'='*60}")
            
            result = self.test_single_league(league_name)
            self.results[league_name] = result
            
            if result.overall_status == "success":
                self.successful_leagues.append(league_name)
                logger.info(f"✅ {league_name}: SUCCESS")
            else:
                self.failed_leagues.append(league_name)
                logger.error(f"❌ {league_name}: FAILED")
                
            # Save individual league results and overall progress
            self.save_league_result(league_name, result)
            self.save_summary()
            
            # Print running summary every 10 leagues
            if i % 10 == 0:
                self.print_summary()
        
        self.print_final_summary()
        return self.results
    
    def test_single_league(self, league_name: str) -> LeagueTestResult:
        """Test a single league comprehensively."""
        result = LeagueTestResult(league_name)
        
        try:
            # Step 1: Test data loading
            logger.info("📊 Step 1: Testing data loading...")
            data_result = self.test_data_loading(league_name)
            result.data_loading = data_result
            
            if data_result["status"] != "success":
                result.overall_status = "failed"
                result.issues.append(f"Data loading failed: {data_result['error']}")
                return result
            
            # Step 2: Test feature preparation
            logger.info("🔧 Step 2: Testing feature preparation...")
            feature_result = self.test_feature_preparation(league_name, data_result["data"])
            result.feature_preparation = feature_result
            
            if feature_result["status"] != "success":
                result.overall_status = "failed"
                result.issues.append(f"Feature preparation failed: {feature_result['error']}")
                return result
            
            # Step 3: Test model training (only in full mode)
            if self.mode == "full":
                logger.info("🤖 Step 3: Testing model training...")
                model_result = self.test_model_training(feature_result["prepared_data"])
                result.model_training = model_result
                
                if model_result["status"] != "success":
                    result.overall_status = "failed"
                    result.issues.append(f"Model training failed: {model_result['error']}")
                    return result
                
                # Step 4: Test prediction
                logger.info("🎯 Step 4: Testing prediction...")
                pred_result = self.test_prediction(league_name, data_result["data"], model_result["models"])
                result.prediction_test = pred_result
                
                if pred_result["status"] != "success":
                    result.overall_status = "failed"
                    result.issues.append(f"Prediction failed: {pred_result['error']}")
                    return result
            
            result.overall_status = "success"
            logger.info("✅ All tests passed!")
            
        except Exception as e:
            result.overall_status = "failed"
            result.issues.append(f"Unexpected error: {str(e)}")
            logger.error(f"❌ Unexpected error testing {league_name}: {e}")
            
        return result
    
    def test_data_loading(self, league_name: str) -> Dict[str, Any]:
        """Test data loading for a league."""
        try:
            league_config = LEAGUE_CONFIGS.get(league_name, {})
            data_tuple, data_info = load_data(league_name, league_config)
            
            if data_tuple is None:
                return {
                    "status": "failed",
                    "error": "load_data returned None",
                    "details": {}
                }
            
            results, team_stats, league_stats, h2h_stats, league_table = data_tuple
            
            # Validate data
            issues = []
            details = {
                "results_shape": results.shape if not results.empty else (0, 0),
                "team_stats_shape": team_stats.shape if not team_stats.empty else (0, 0),
                "league_stats_shape": league_stats.shape if not league_stats.empty else (0, 0),
                "h2h_stats_shape": h2h_stats.shape if not h2h_stats.empty else (0, 0),
                "league_table_shape": league_table.shape if not league_table.empty else (0, 0),
            }
            
            if results.empty:
                issues.append("Results dataframe is empty")
            if team_stats.empty:
                issues.append("Team stats dataframe is empty")
            if league_stats.empty:
                issues.append("League stats dataframe is empty")
                
            # Check for minimum data requirements (lowered from 10 to 5 for testing)
            if not results.empty and len(results) < 5:
                issues.append(f"Very few matches: {len(results)}")
            if not team_stats.empty and len(team_stats) < 4:
                issues.append(f"Very few teams: {len(team_stats)}")
                
            status = "failed" if issues else "success"
            
            return {
                "status": status,
                "error": "; ".join(issues) if issues else None,
                "details": details,
                "data": data_tuple  # Keep for internal use, exclude from JSON
            }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": str(e),
                "details": {"traceback": traceback.format_exc()}
            }
    
    def test_feature_preparation(self, league_name: str, data_tuple: Tuple) -> Dict[str, Any]:
        """Test feature preparation for a league."""
        try:
            results, team_stats, league_stats, h2h_stats, league_table = data_tuple
            league_config = LEAGUE_CONFIGS.get(league_name, {})
            
            # Get column mappings
            mappings = {
                "team_stats": {
                    "points_per_game": "points_per_game",
                    "goals_scored_per_match_home": "goals_scored_per_match_home",
                    "goals_scored_per_match_away": "goals_scored_per_match_away",
                    "goals_conceded_per_match_home": "goals_conceded_per_match_home",
                    "goals_conceded_per_match_away": "goals_conceded_per_match_away",
                },
                "h2h_stats": {
                    "team_a_win_percentage": "team_a_win_percentage",
                    "team_b_win_percentage": "team_b_win_percentage",
                    "draw_percentage": "draw_percentage",
                }
            }
            combined_mapping = {**mappings["team_stats"], **mappings["h2h_stats"]}
            
            prepared_data = prepare_features(
                results, team_stats, league_stats, h2h_stats, league_table,
                combined_mapping, league_config.get('TEAM_NAME_MAPPING', {})
            )
            
            if prepared_data is None or prepared_data.empty:
                return {
                    "status": "failed",
                    "error": "Feature preparation returned empty data",
                    "details": {}
                }
            
            # Check for issues
            issues = []
            nan_columns = prepared_data.columns[prepared_data.isna().any()].tolist()
            if nan_columns:
                issues.append(f"NaN values in columns: {nan_columns[:5]}...")
            
            details = {
                "prepared_shape": prepared_data.shape,
                "nan_columns_count": len(nan_columns),
                "feature_count": len(prepared_data.columns)
            }
            
            status = "success" if not issues else "failed"
            
            return {
                "status": status,
                "error": "; ".join(issues) if issues else None,
                "details": details,
                "prepared_data": prepared_data  # Keep for internal use, exclude from JSON
            }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": str(e),
                "details": {"traceback": traceback.format_exc()}
            }
    
    def test_model_training(self, prepared_data: pd.DataFrame) -> Dict[str, Any]:
        """Test model training for prepared data."""
        try:
            # Prepare training data (simplified version)
            columns_to_drop = [
                "result", "three_way", "double_chance",
                "over_under_1_5", "over_under_2_5", "over_under_3_5",
                "btts", "home_goals", "away_goals", "total_goals",
                "three_way_encoded", "double_chance_encoded",
                "over_under_1_5_encoded", "over_under_2_5_encoded",
                "over_under_3_5_encoded", "btts_encoded",
                "form_data_valid_str",
            ]
            
            X = prepared_data.drop(
                [col for col in columns_to_drop if col in prepared_data.columns],
                axis=1
            )
            
            # Create minimal y_dict for testing
            y_dict = {}
            if 'three_way' in prepared_data.columns:
                y_dict['three_way'] = prepared_data['three_way']
            if 'over_under_2_5' in prepared_data.columns:
                y_dict['over_under_2_5'] = prepared_data['over_under_2_5']
            if 'btts' in prepared_data.columns:
                y_dict['btts'] = prepared_data['btts']
            
            if not y_dict:
                return {
                    "status": "failed",
                    "error": "No target variables found",
                    "details": {}
                }
            
            label_encoders = prepared_data.attrs.get("label_encoders", {})
            
            # Train models
            models = train_model(X, y_dict, label_encoders)
            
            if not models:
                return {
                    "status": "failed",
                    "error": "No models trained",
                    "details": {}
                }
            
            details = {
                "models_trained": list(models.keys()),
                "training_data_shape": X.shape,
                "target_variables": list(y_dict.keys())
            }
            
            return {
                "status": "success",
                "error": None,
                "details": details,
                "models": models  # Keep for internal use, exclude from JSON
            }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": str(e),
                "details": {"traceback": traceback.format_exc()}
            }
    
    def test_prediction(self, league_name: str, data_tuple: Tuple, models: Dict) -> Dict[str, Any]:
        """Test prediction for a league."""
        try:
            results, team_stats, league_stats, h2h_stats, league_table = data_tuple
            
            # Get first two teams for testing
            teams = team_stats["Team"].unique()
            if len(teams) < 2:
                return {
                    "status": "failed",
                    "error": "Not enough teams for prediction test",
                    "details": {}
                }
            
            home_team, away_team = teams[0], teams[1]
            
            # Test prediction
            avg_goals_per_match = 2.5  # Default value
            if not league_stats.empty:
                avg_goals_match = league_stats[league_stats["Stat"] == "avg_goals_per_match"]
                if not avg_goals_match.empty:
                    avg_goals_per_match = float(avg_goals_match["Value"].values[0])
            
            pred_results, error_message, correct_scores = predict_match(
                models,
                home_team,
                away_team,
                team_stats,
                league_stats,
                h2h_stats,
                league_table,
                {},  # column_mapping
                models.get("three_way", {}).get("feature_names", []),
                avg_goals_per_match,
                label_encoders=models.get("three_way", {}).get("encoder", {}),
                log_features=False
            )
            
            if error_message:
                return {
                    "status": "failed",
                    "error": error_message,
                    "details": {}
                }
            
            details = {
                "test_match": f"{home_team} vs {away_team}",
                "predictions_generated": list(pred_results.get("main_predictions", {}).keys()),
                "expected_goals": pred_results.get("expected_goals", {}),
                "correct_scores_count": len(correct_scores)
            }
            
            return {
                "status": "success",
                "error": None,
                "details": details
            }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": str(e),
                "details": {"traceback": traceback.format_exc()}
            }
    
    def save_league_result(self, league_name: str, result: LeagueTestResult):
        """Save individual league result to its own directory."""
        # Create league-specific directory
        league_dir = os.path.join(self.base_log_dir, league_name)
        os.makedirs(league_dir, exist_ok=True)
        
        # Save detailed league result
        league_file = os.path.join(league_dir, "result.json")
        with open(league_file, 'w') as f:
            json.dump(result.to_dict(), f, indent=2)
        
        # Create a simple status file for quick overview
        status_file = os.path.join(league_dir, "status.txt")
        with open(status_file, 'w') as f:
            f.write(f"League: {league_name}\n")
            f.write(f"Status: {result.overall_status.upper()}\n")
            f.write(f"Tested at: {datetime.now().isoformat()}\n\n")
            
            if result.issues:
                f.write("Issues:\n")
                for issue in result.issues:
                    f.write(f"  - {issue}\n")
            
            if result.warnings:
                f.write("\nWarnings:\n")
                for warning in result.warnings:
                    f.write(f"  - {warning}\n")
    
    def save_summary(self):
        """Save overall testing summary."""
        summary_data = {
            "timestamp": datetime.now().isoformat(),
            "mode": self.mode,
            "total_tested": len(self.results),
            "successful": len(self.successful_leagues),
            "failed": len(self.failed_leagues),
            "successful_leagues": self.successful_leagues,
            "failed_leagues": self.failed_leagues,
            "base_log_dir": self.base_log_dir
        }
        
        with open(self.summary_file, 'w') as f:
            json.dump(summary_data, f, indent=2)
        
        # Also create a simple text summary
        summary_text_file = os.path.join(self.base_log_dir, "summary.txt")
        with open(summary_text_file, 'w') as f:
            f.write(f"League Testing Summary\n")
            f.write(f"====================\n\n")
            f.write(f"Mode: {self.mode.upper()}\n")
            f.write(f"Started: {self.timestamp}\n")
            f.write(f"Last Updated: {datetime.now().isoformat()}\n\n")
            f.write(f"Progress: {len(self.results)} leagues tested\n")
            f.write(f"✅ Successful: {len(self.successful_leagues)}\n")
            f.write(f"❌ Failed: {len(self.failed_leagues)}\n\n")
            
            if self.successful_leagues:
                f.write("Successful Leagues:\n")
                for league in self.successful_leagues:
                    f.write(f"  ✅ {league}\n")
                f.write("\n")
            
            if self.failed_leagues:
                f.write("Failed Leagues:\n")
                for league in self.failed_leagues:
                    f.write(f"  ❌ {league}\n")
                f.write("\n")
            
            f.write(f"Detailed results available in: {self.base_log_dir}/[LEAGUE_NAME]/\n")
    
    def print_summary(self):
        """Print current testing summary."""
        total = len(self.results)
        success = len(self.successful_leagues)
        failed = len(self.failed_leagues)
        
        logger.info(f"\n📊 PROGRESS SUMMARY:")
        logger.info(f"   Tested: {total} leagues")
        logger.info(f"   ✅ Successful: {success} ({success/total*100:.1f}%)")
        logger.info(f"   ❌ Failed: {failed} ({failed/total*100:.1f}%)")
    
    def print_final_summary(self):
        """Print final testing summary with detailed analysis."""
        logger.info("\n" + "=" * 80)
        logger.info("🏆 FINAL TESTING SUMMARY")
        logger.info("=" * 80)
        
        total = len(self.results)
        success = len(self.successful_leagues)
        failed = len(self.failed_leagues)
        
        logger.info(f"Total Leagues Tested: {total}")
        logger.info(f"✅ Successful: {success} ({success/total*100:.1f}%)")
        logger.info(f"❌ Failed: {failed} ({failed/total*100:.1f}%)")
        
        # Analyze failure patterns
        failure_reasons = {}
        for league_name in self.failed_leagues:
            result = self.results[league_name]
            for issue in result.issues:
                category = issue.split(':')[0] if ':' in issue else issue
                failure_reasons[category] = failure_reasons.get(category, 0) + 1
        
        if failure_reasons:
            logger.info(f"\n📊 FAILURE ANALYSIS:")
            for reason, count in sorted(failure_reasons.items(), key=lambda x: x[1], reverse=True):
                logger.info(f"   {reason}: {count} leagues")
        
        # List successful leagues
        if self.successful_leagues:
            logger.info(f"\n✅ SUCCESSFUL LEAGUES ({len(self.successful_leagues)}):")
            for league in self.successful_leagues[:10]:  # Show first 10
                logger.info(f"   • {league}")
            if len(self.successful_leagues) > 10:
                logger.info(f"   ... and {len(self.successful_leagues) - 10} more")
        
        # List failed leagues
        if self.failed_leagues:
            logger.info(f"\n❌ FAILED LEAGUES ({len(self.failed_leagues)}):")
            for league in self.failed_leagues[:10]:  # Show first 10
                logger.info(f"   • {league}")
            if len(self.failed_leagues) > 10:
                logger.info(f"   ... and {len(self.failed_leagues) - 10} more")
        
        logger.info(f"\n📝 Detailed results saved to: {self.base_log_dir}")
        logger.info(f"📊 Summary available at: {self.summary_file}")
        logger.info(f"🕒 Testing completed at: {datetime.now()}")

def main():
    """Main function with command line argument parsing."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Comprehensive League Testing Suite")
    parser.add_argument('--mode', choices=['quick', 'full'], default='quick',
                       help='Testing mode: quick (data validation only) or full (complete pipeline)')
    parser.add_argument('--league', type=str, help='Test specific league only')
    parser.add_argument('--resume', type=str, help='Resume testing from specific league')
    
    args = parser.parse_args()
    
    tester = LeagueTester(mode=args.mode)
    
    if args.league:
        # Test single league
        logger.info(f"Testing single league: {args.league}")
        result = tester.test_single_league(args.league)
        logger.info(f"Result: {result.overall_status}")
        if result.issues:
            logger.error(f"Issues: {result.issues}")
    else:
        # Test all leagues
        tester.test_all_leagues(resume_from=args.resume)

if __name__ == "__main__":
    main()